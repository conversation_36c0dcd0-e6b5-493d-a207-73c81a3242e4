<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="11">
            <item index="0" class="java.lang.String" itemvalue="pandas" />
            <item index="1" class="java.lang.String" itemvalue="scipy" />
            <item index="2" class="java.lang.String" itemvalue="thop" />
            <item index="3" class="java.lang.String" itemvalue="seaborn" />
            <item index="4" class="java.lang.String" itemvalue="matplotlib" />
            <item index="5" class="java.lang.String" itemvalue="pillow" />
            <item index="6" class="java.lang.String" itemvalue="gitpython" />
            <item index="7" class="java.lang.String" itemvalue="numpy" />
            <item index="8" class="java.lang.String" itemvalue="torchvision" />
            <item index="9" class="java.lang.String" itemvalue="ultralytics" />
            <item index="10" class="java.lang.String" itemvalue="psutil" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N801" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>