<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="aa0fc3c4-95b3-407b-8c4c-079dab1c1928" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2uqvW0v0xtLKTTX4zPbfGnC48Ni" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.AITEST.executor": "Run",
    "Python.HIT你的壶.executor": "Run",
    "Python.util.executor": "Run",
    "Python.一键启动.executor": "Run",
    "Python.中线.executor": "Run",
    "Python.冰冰有礼.executor": "Run",
    "Python.冰冰薄荷糖.executor": "Run",
    "Python.简单球.executor": "Run",
    "Python.蒙特卡洛树.executor": "Run",
    "Python.蒙特卡洛树7788.executor": "Run",
    "Python.蒙特卡洛树7789.executor": "Run",
    "Python.蒙特卡洛树7790.executor": "Run",
    "Python.蒙特卡洛树7794.executor": "Run",
    "Python.蒙特卡洛树7795.executor": "Run",
    "Python.蒙特卡洛树7796.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "D:/university/社团/无人机实验室/2025/冰壶比赛/strategy/train",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "File.Encoding",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\university\社团\无人机实验室\2025\冰壶比赛\strategy\train" />
      <recent name="D:\university\社团\无人机实验室\2025\冰壶比赛\strategy\对手" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\university\社团\无人机实验室\2025\冰壶比赛\strategy\train\model\多开训练" />
      <recent name="D:\university\社团\无人机实验室\2025\冰壶比赛\strategy" />
      <recent name="D:\university\社团\无人机实验室\2025\冰壶比赛\strategy\train" />
      <recent name="D:\university\社团\无人机实验室\2025\冰壶比赛\strategy\对手" />
      <recent name="D:\university\社团\无人机实验室\2025\冰壶比赛" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-PY-251.26927.90" />
        <option value="bundled-python-sdk-41e8cd69c857-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="aa0fc3c4-95b3-407b-8c4c-079dab1c1928" name="更改" comment="" />
      <created>1742988947132</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1742988947132</updated>
      <workItem from="1742988948683" duration="2760000" />
      <workItem from="1742992160988" duration="84000" />
      <workItem from="1742992251062" duration="2708000" />
      <workItem from="1744633245856" duration="333000" />
      <workItem from="1744633594721" duration="596000" />
      <workItem from="1744716780148" duration="15053000" />
      <workItem from="1744956540323" duration="3505000" />
      <workItem from="1746431974468" duration="13717000" />
      <workItem from="1746458139116" duration="1464000" />
      <workItem from="1746521364509" duration="3052000" />
      <workItem from="1746606782813" duration="1143000" />
      <workItem from="1746948493727" duration="2578000" />
      <workItem from="1747017636492" duration="1206000" />
      <workItem from="1747188162120" duration="287000" />
      <workItem from="1748177432295" duration="506000" />
      <workItem from="1749089785876" duration="1158000" />
      <workItem from="1749179058122" duration="147000" />
      <workItem from="1749179215023" duration="598000" />
      <workItem from="1749728096662" duration="702000" />
      <workItem from="1749793917598" duration="118000" />
      <workItem from="1751353914250" duration="142000" />
      <workItem from="1751354063313" duration="1979000" />
      <workItem from="1751360235978" duration="2967000" />
      <workItem from="1751424296426" duration="3476000" />
      <workItem from="1751512236251" duration="10717000" />
      <workItem from="1751547700110" duration="1260000" />
      <workItem from="1751588720717" duration="1137000" />
      <workItem from="1751589875723" duration="12000" />
      <workItem from="1751589895213" duration="2362000" />
      <workItem from="1751592300215" duration="1109000" />
      <workItem from="1751593627410" duration="110000" />
      <workItem from="1751609136377" duration="110000" />
      <workItem from="1751609264242" duration="4074000" />
      <workItem from="1751617563207" duration="447000" />
      <workItem from="1751641473262" duration="1243000" />
      <workItem from="1751690018738" duration="242000" />
      <workItem from="1751690272426" duration="4764000" />
      <workItem from="1751711657115" duration="4890000" />
      <workItem from="1751809343856" duration="6666000" />
      <workItem from="1751847060283" duration="1453000" />
      <workItem from="1751848541735" duration="4243000" />
      <workItem from="1751869091832" duration="2128000" />
      <workItem from="1751877664408" duration="3139000" />
      <workItem from="1751935507143" duration="16006000" />
      <workItem from="1751970373488" duration="11178000" />
      <workItem from="1752027456756" duration="9805000" />
      <workItem from="1752050686232" duration="1233000" />
      <workItem from="1752068302567" duration="5862000" />
      <workItem from="1752112583365" duration="24325000" />
      <workItem from="1752198035227" duration="10289000" />
      <workItem from="1752215835182" duration="9602000" />
      <workItem from="1752242017304" duration="4686000" />
      <workItem from="1752484545857" duration="8272000" />
      <workItem from="1752508859708" duration="4423000" />
      <workItem from="1752567377052" duration="23596000" />
      <workItem from="1752667051287" duration="3056000" />
      <workItem from="1752714832304" duration="13849000" />
      <workItem from="1752801286416" duration="1757000" />
      <workItem from="1752803088441" duration="15436000" />
      <workItem from="1752977063541" duration="2524000" />
      <workItem from="1753345257590" duration="11751000" />
      <workItem from="1753413151956" duration="304000" />
      <workItem from="1753630006198" duration="3739000" />
      <workItem from="1753780424749" duration="12036000" />
      <workItem from="1753806463052" duration="4000" />
      <workItem from="1753842694624" duration="847000" />
      <workItem from="1753844508493" duration="4348000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/strategy/train/model/多开训练/util.py</url>
          <line>1550</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/一键启动.py</url>
          <line>16</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/_py$7790.coverage" NAME="蒙特卡洛树7790 覆盖结果" MODIFIED="1753803076251" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/_py$7796.coverage" NAME="蒙特卡洛树7796 覆盖结果" MODIFIED="1753803081577" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/$7788.coverage" NAME="蒙特卡洛树7788 覆盖结果" MODIFIED="1752668552658" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/$7794.coverage" NAME="蒙特卡洛树7794 覆盖结果" MODIFIED="1752636160107" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/$7795.coverage" NAME="蒙特卡洛树7795 覆盖结果" MODIFIED="1752572501733" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/_py$7794.coverage" NAME="蒙特卡洛树7794 覆盖结果" MODIFIED="1753803078207" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/$.coverage" NAME="冰冰薄荷糖 覆盖结果" MODIFIED="1752667148923" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy" />
    <SUITE FILE_PATH="coverage/_py$7789.coverage" NAME="蒙特卡洛树7789 覆盖结果" MODIFIED="1753803074676" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/$HIT.coverage" NAME="HIT你的壶 覆盖结果" MODIFIED="1752668535053" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/_py$7788.coverage" NAME="蒙特卡洛树7788 覆盖结果" MODIFIED="1753803072617" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/$7789.coverage" NAME="蒙特卡洛树7789 覆盖结果" MODIFIED="1752636157765" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/$AITEST.coverage" NAME="AITEST 覆盖结果" MODIFIED="1752667144048" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/$7790.coverage" NAME="蒙特卡洛树7790 覆盖结果" MODIFIED="1752636159098" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/01__ipynb$.coverage" NAME="冰冰薄荷糖 覆盖结果" MODIFIED="1746951777262" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy" />
    <SUITE FILE_PATH="coverage/$7796.coverage" NAME="蒙特卡洛树7796 覆盖结果" MODIFIED="1752572503138" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/_py$7795.coverage" NAME="蒙特卡洛树7795 覆盖结果" MODIFIED="1753803080104" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
    <SUITE FILE_PATH="coverage/_py$.coverage" NAME="一键启动 覆盖结果" MODIFIED="1753803040391" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$util.coverage" NAME="util 覆盖结果" MODIFIED="1752594329899" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/strategy/train/model/多开训练" />
  </component>
</project>