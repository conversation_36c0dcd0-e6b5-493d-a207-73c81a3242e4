{"cells": [{"cell_type": "markdown", "id": "a7553102-8e02-4cd2-b0fb-601b4fcfb2b3", "metadata": {}, "source": ["# 第一课 数字冰壶比赛简介\n", "\n", "## 1.1 冰壶比赛简介\n", "\n", "冰壶运动是在以队为单位冰上进行的投掷性竞赛，被誉为“冰上的国际象棋”，它考验参与者的体能与脑力，展现动静之美，取舍之智慧，属于冬奥会比赛项目。\n", "\n", "<center><img src=\"img/CurlingThrow.gif\"></center>\n", "\n", "简单来说，冰壶比赛就是2个队，每队4人，轮番把16个40斤的冰壶投进约摸30米远的大本营中（一人投俩）。比赛结束后，在大本营中，所有冰壶更靠近圆心的队伍得分，并且有多少冰壶更靠近圆心就得多少分。\n", "\n", "<center><img src=\"img/CurlingScore.png\" width=400></center>\n", "\n", "就这样进行10场比赛，每场比赛得分加起来作为最后的总得分。在冰壶比赛中，由于后投者可以将大本营中已有的冰壶撞开，所以这并不是一个简单的比准头的比赛，而是一场讲究策略的博弈。\n", "\n", "## 1.2 数字冰壶比赛平台\n", "\n", "数字冰壶比赛平台实现了冰壶运动在数字空间中的呈现，学生可以在虚拟空间中采用数字模拟器自行训练AI选手，设计各种策略实现数字空间中冰壶的智能投掷，还可以和其它学生设计的AI选手进行数字比赛对抗，深入理解强化学习、博弈论等理论并在实验中验证。\n", "\n", "数字冰壶比赛平台服务器端程序基于Unity开发，用作数字冰壶比赛的游戏引擎。该平台提供AI对战以及投掷调试功能，平台与选手编写的AI程序采用TCP协议进行通信，参赛选手编写的AI程序需要根据接收的消息制定出决策，并调用发送接口发送给对战平台。\n", "\n", "### 1.2.1 数字冰壶比赛平台坐标系\n", "\n", "数字冰壶系统采用的坐标系如图所示，在正面俯瞰视角下场地左上角为原点，向下为y轴正方向，向右为x轴正方向。系统中的长度单位为米，速度单位为米/秒，角速度单位为弧度/秒。场地长44.5米，宽4.75米，远端大本营圆心坐标为（2.375，4.88），远端前掷线到远端后卫线长度为7.56米，y分量上投球点距离远端大本营圆心27.6米。\n", "\n", "<center><img src=\"img/CurlingCourt.png\"></center>\n", "\n", "### 1.2.2 数字冰壶比赛平台通讯协议\n", "\n", "数字冰壶比赛平台通讯协议规约如下表所示。AI程序可以遵循此通讯协议和数字冰壶服务器进行通讯，获取得分区内的冰壶球位置，或者给出投掷指令，从而完成虚拟对战。\n", "\n", "<center><img src=\"img/protocol.png\"></center>\n", "\n", "信息代码对应描述如下：\n", "\n", "- CONNECTKEY：连接关键字（在数字冰壶比赛客户端右下角查看）\n", "- CONNECNAME：参数1-客户端给AI生成的临时选手名，Player1表示首局先手，Player2表示首局后手；\n", "- ISREADY：客户端准备完毕；\n", "- READYOK：AI选手准备完毕；\n", "- NAME：参数1-AI选手名，如不发送此消息则沿用临时选手名；\n", "- NEWGAME：开始比赛；\n", "- SETSTATE：设置比赛状态。参数1-当前完成投掷数，参数2-当前完成对局数，参数3-总对局数，参数4-预备投掷者（0为持蓝色球者，1为持红色球者）；\n", "- POSITION：16个冰壶球的当前坐标，顺序同当前对局投掷顺序，（0，0）坐标表示未投掷或已出界的球；\n", "- GO：请求CurlingAI执行动作；\n", "- BESTSHOT：给出投掷信息。参数1-冰壶初速度，参数2-冰壶投掷时的横向偏移，参数3-冰壶初始角速度；\n", "- MOTIONINFO：在冰壶球运动至赛道中间时，向CurlingAI发出运动状态信息。参数1-当前运动冰壶x坐标，参数2-当前运动冰壶y坐标，参数3-当前运动冰壶速度x方向分量，参数4-当前运动冰壶速度y方向分量，参数5-当前运动冰壶旋转角速度；\n", "- SWEEP：CurlingAI决定擦冰则发出此消息，擦冰最长持续到对方前卫线。参数1-擦冰距离；\n", "- SCORE：参数1-本局比赛该选手得分；\n", "- TOTALSCORE：参数1-持篮球选手总得分，参数2-持红球选手总得分；\n", "- GAMEOVER：参数1-胜利(WIN)、失败(LOSE)或平局(DRAW)。\n", "\n", "## 小结\n", "\n", "本课介绍了数字冰壶比赛平台的功能，并着重介绍了数字冰壶比赛平台所用的坐标系和通讯协议。虽然课程内容没有涉及到程序代码的编写，但后续课程的程序代码都需要遵循本课程的相关内容，请务必认真阅读。"]}], "metadata": {"kernelspec": {"display_name": "opencv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}