{"cells": [{"cell_type": "markdown", "id": "bc91094f-1548-4641-be58-5d932c92333c", "metadata": {}, "source": ["# 第二课 数字冰壶比赛中的Socket通信\n", "\n", "## 2.1 Socket通信\n", "\n", "Socket是一种抽象层，通常翻译为套接字，应用程序通过它来发送和接收数据，使用Socket可以将应用程序添加到网络中，与处于同一网络中的其他应用程序进行通信。\n", "\n", "Socket可以理解为是两个程序进行双向数据传输的网络通信的端点，一般由一个主机地址加上一个端口号来表示。每个程序都在一个端口上提供服务，而想要使用该服务的程序则需要连接该端口。\n", "\n", "这就好比是在两个程序之间搭建了一根管道，程序A可以通过管道向程序B发送数据，程序B可以接受管道传输来的数据，同样程序B也可以发送，这个管道就相当于Socket的作用。\n", "\n", "<center><img src=\"img/Socket.png\" width=600px></center>\n", "\n", "根据不同的的底层协议，Socket通信的实现是多样化的，最常用到的是基于TCP/IP协议族的Socket通信，而在这个协议族当中主要的Socket类型为流套接字（streamsocket）和数据报套接字(datagramsocket)。\n", "\n", "流套接字将TCP协议作为其端对端协议，提供了一个可信赖的字节流服务，是有连接的通信方式，通信双方在开始时必须建立一次连接过程，建立一条通信链路。数据报套接字将UDP协议作为其端对端协议，提供数据打包发送服务，是无连接的通信方式，通信双方不存在一个连接过程，一次网络I/O以一个数据报形式进行。\n", "\n", "在数字冰壶比赛中，用到的就是使用TCP协议的有连接的流套接字通信。\n", "\n", "有连接Socket通信和打电话很相似。你要打电话给一个朋友，一般要经历下面三个步骤：\n", "\n", "1. 拨号，对方听到电话铃声后提起电话，这时双方就建立起了连接；\n", "2. 双方通话；\n", "3. 挂断电话结束此次交谈。\n", "\n", "类似地，有连接Socket通信要经历下面三个步骤：\n", "\n", "1. 创建Socket，双方建立连接；\n", "3. 双方按照一定的协议对Socket进行读写操作；\n", "3. 关闭Socket。\n", "\n", "## 2.2 启动数字冰壶比赛服务器\n", "\n", "想要给朋友打电话，首先要有一个朋友。要编写程序和数字冰壶比赛服务器通讯，当然也要先启动数字冰壶比赛服务器。\n", "\n", "点击页面左上角Jupyter菜单中的[Run]菜单项，点击该该菜单项的[Start Curling Server]子菜单项，即可启动一个数字冰壶比赛服务器。\n", "\n", "<center><img src=\"img/StartServer.png\" width=600px></center>\n", "\n", "数字冰壶比赛服务器会在新开浏览器页面中启动，第一次加载速度较慢，请耐心等待。\n", "\n", "数字冰壶比赛服务器主界面如下图所示，请注意界面右下角给出的连接信息，<b>每次启动服务器该信息中的具体数据都会有变化</b>，这些数据在后续的程序代码编写中会用到。\n", "\n", "<center><img src=\"img/CurlingServer.png\"></center>\n", "\n", "## 2.3 编写AI选手实现和服务器的Socket通信\n", "\n", "### 2.3.1 创建Socket连接\n", "\n", "因为要操作Socket，所以需要用到Socket模块，python 內建的模块就很方便，运行导入模块的范例代码如下所示：\n", "\n", "> 本课程采用jupyter平台实现在线编程教学，给出的所有代码单元都可以在教程页面中直接运行，运行方式有以下两种：<br>\n", ">> 1. 点击代码单元放置光标在任意位置，然后点击课程页面上方工具条中的三角形按钮，会运行该段代码然后选中下一个单元；\n", ">> 2. 点击代码单元放置光标在任意位置，然后按下Ctrl+Enter组合键，会运行该段代码并停留在当前单元。\n"]}, {"cell_type": "code", "execution_count": null, "id": "e33ecafe-873a-4411-aa2f-4119e5979d41", "metadata": {"tags": []}, "outputs": [], "source": ["#导入socket模块\n", "import socket"]}, {"cell_type": "markdown", "id": "2228ff95-bf4d-4767-b4fa-319f4c55f354", "metadata": {}, "source": ["socket类的构造函数的语法格式如下所示：\n", "\n", "<b>s = socket.socket( [family [, type [, protocol]]] )</b><br>\n", "参数：family - 套接字家族，AF_INET代表基于网络类型，AF_UNIX代表基于文件类型，默认为AF_INET；<br>\n", "    　　　type - 套接字通信类型，SOCK_STREAM代表使用TCP协议的流套接字通信，SOCK_DGRAM代表使用UDP协议的数据报套接字通信，SOCK_RAW代表使用其他协议的套接字通信，默认为SOCK_STREAM。<br>\n", "　　　protocol: type为SOCK_RAW才需要这个参数，指定使用协议，默认为 0。<br>\n", "返回值：socket对象\n", "\n", "在和数字冰壶比赛服务器的通信过程中，使用的是基于网络类型的流套接字通信，新建Socket对象的范例代码如下所示："]}, {"cell_type": "code", "execution_count": null, "id": "765c8db8-1d42-433c-83ab-56f42bd3dc6e", "metadata": {"tags": []}, "outputs": [], "source": ["#新建Socket对象\n", "ai_sock =socket.socket()"]}, {"cell_type": "markdown", "id": "7fd40b79-9ad8-446d-8d14-015607f80f5b", "metadata": {}, "source": ["初始化Socket对象后，即可开始创建Socket连接，在数字冰壶比赛服务器上已经创建了服务器Socket，这里只需要创建客户端Socket。相关函数的语法格式如下所示：\n", "\n", "<b>s.connect(address)<br></b>\n", "参数：address - 连接地址，格式为元组(host,port)，其中host为服务器主机（域名或IP），port为连接端口。<br>\n", "返回值：无\n", "\n", "根据数字冰壶服务器主界面右下角显示的连接信息，编写创建Socket连接的范例代码如下所示："]}, {"cell_type": "code", "execution_count": null, "id": "188f2984-8316-4583-a539-9ea5370edb42", "metadata": {"tags": []}, "outputs": [], "source": ["#服务器主机：参照数字冰壶服务器界面中给出的连接信息填写，通常无需修改。\n", "host = 'curling-server-7788.jupyterhub.svc.cluster.local'\n", "#连接端口：参照数字冰壶服务器界面中给出的连接信息填写，通常无需修改。\n", "port = 7788\n", "\n", "#创建Socket连接\n", "ai_sock.connect((host,port))\n", "print(\"已建立socket连接\", host, port)"]}, {"cell_type": "markdown", "id": "d5d1590e-005a-46fb-b016-cb6b979612eb", "metadata": {"tags": []}, "source": ["### 2.3.2 连接到数字冰壶服务器核对密钥\n", "\n", "在数字冰壶服务器的主界面中点击【投掷调试】按钮，即可进入如下图所示的调试界面。\n", "\n", "<center><img src=\"img/CurlingTest1.png\"></center>\n", "\n", "投掷调试和正式对战的流程是相同的，AI选手需要先向服务器发送连接密钥，服务器核对密钥无误后，会向AI选手发送临时选手名。\n", "\n", "> 正式对战时，临时选手名为Player1代表首局先手，Player2代表首局后手。投掷调试时，临时选手名一律为Player1。\n", "\n", "Socket对象发送/接收消息相关函数的语法格式如下所示：\n", "\n", "<b>ret = s.send(data)</b><br>\n", "参数：data - 待发送的数据，要求是bytes类型数据，字符串类型数据需要调用encode()方法编码后再行发送。<br>\n", "返回值：发送的数据长度\n", "\n", "<b>data = s.recv(size)</b><br>\n", "参数：size - 接收数据的最大长度。<br>\n", "返回值：接收到的数据，类型为bytes，字符串数据需要调用decode()方法进行解码。\n", "\n", "向数字冰壶服务器发送密钥并从数字冰壶服务器接收临时选手名的范例代码如下所示，其中key的值需要根据如图所示数字冰壶服务器主界面右下角显示的连接信息中ConnectKey的内容进行修改："]}, {"cell_type": "code", "execution_count": null, "id": "a8ef7672-e80b-4507-b2a1-cd981b183ba4", "metadata": {"tags": []}, "outputs": [], "source": ["#连接密钥：参照数字冰壶服务器界面中给出的连接信息填写，注意这个参数每次新启动服务器都会改变。\n", "key = \"lidandan_66323abd-7414-4e52-94a9-6c75d5b21c11\"\n", "\n", "#通过socket对象发送消息\n", "msg_send = 'CONNECTKEY:' + key  # + '\\0'\n", "ai_sock.send(msg_send.encode())\n", "\n", "#通过socket对象接收消息\n", "msg_recv = ai_sock.recv(1024)\n", "print(msg_recv, flush=True)"]}, {"cell_type": "markdown", "id": "597d2a7e-367b-48b6-baf8-f0b86019ed0f", "metadata": {}, "source": ["上方代码成功运行后，会收到类似于 b'CONNECTNAME Player1 \\x00' 的消息数据。数据开头的b说明这个数据是bytes类型；消息代码和参数是以空格分隔开，如果有多个参数，各个参数之间也是以空格进行分隔；末尾的\\x00说明是以0结尾，实际处理时需要去掉末尾的0并转换成字符串类型再进一步解析。\n", "\n", "> 上方代码中调用print函数时使用了参数flush=True，这个参数可以刷新输出，如果在对战平台中遇到print()函数无法正确输出文本的问题，可以使用这个参数尝试解决。"]}, {"cell_type": "markdown", "id": "733ff51a-54c1-4ceb-8b74-4228de7f1b2a", "metadata": {}, "source": ["### 2.3.3 确认选手已准备完毕\n", "\n", "后续过程中需要多次编码发送消息，以及多次接收消息进行解析，按照编程惯例，将这两个过程编写为函数方便调用。范例代码如下所示："]}, {"cell_type": "code", "execution_count": null, "id": "995e6b14-7c5e-438b-a321-572deff60070", "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "#通过socket对象发送消息\n", "def send_msg(sock, msg):\n", "    print(\"  >>>> \" + msg)\n", "    #将消息数据从字符串类型转换为bytes类型后发送\n", "    sock.send(msg.strip().encode())\n", "    \n", "#通过socket对象接收消息并进行解析\n", "def recv_msg(sock):\n", "    #为避免TCP粘包问题，数字冰壶服务器发送给AI选手的每一条信息均以0（数值为0的字节）结尾\n", "    #这里采用了逐个字节接收后拼接的方式处理信息，多条信息之间以0为信息终结符\n", "    buffer = bytearray()\n", "    while True:\n", "        #接收1个字节\n", "        data = sock.recv(1)\n", "        #接收到空数据或者信息处终结符(0)即中断循环\n", "        if not data or data == b'\\0':\n", "            time.sleep(0.1)\n", "            break\n", "        #将当前字节拼接到缓存中\n", "        buffer.extend(data)\n", "    #将消息数据从bytes类型转换为字符串类型后去除前后空格\n", "    msg_str = buffer.decode().strip()\n", "    print(\"<<<< \" + msg_str)\n", "\n", "    #用空格将消息字符串分隔为列表\n", "    msg_list = msg_str.split(\" \")\n", "    #列表中第一个项为消息代码\n", "    msg_code = msg_list[0]\n", "    #列表中后续的项为各个参数\n", "    msg_list.pop(0)\n", "    #返回消息代码和消息参数列表\n", "    return msg_code, msg_list"]}, {"cell_type": "markdown", "id": "0baa9e99-0ae0-4337-b476-77cea592df43", "metadata": {"tags": []}, "source": ["成功连接到数字冰壶服务器核对密钥后，数字冰壶服务器的界面上会显示＜Player1 已连接＞，如图所示。\n", "\n", "> 在投掷调试模式中，数字冰壶服务器接收到AI选手的连接信息后，会启动一个笨笨的对手机器人，所以界面上还会显示＜Player2 已连接＞。\n", "\n", "<center><img src=\"img/CurlingTest2.png\"></center>\n", "\n", "点击界面上的【准备】按钮后，数字冰壶服务器会向AI选手发送“ISREADY”消息，AI选手需要回复“READYOK”消息，表示已经准备完毕，并需要发送带有参数的“NAME”消息，将AI选手的选手名发送到数字冰壶服务器，消息代码和参数之间用空格分隔。范例代码如下所示："]}, {"cell_type": "code", "execution_count": null, "id": "5af99d74-ce3a-4932-a8bc-90fdcc5bc568", "metadata": {"tags": []}, "outputs": [], "source": ["#接收消息并解析\n", "msg_code, msg_list = recv_msg(ai_sock)\n", "#如果消息代码是\"ISREADY\"\n", "if msg_code == \"ISREADY\":\n", "    #发送\"READYOK\"\n", "    send_msg(ai_sock, \"READYOK\")\n", "    #发送\"NAME\"和AI选手名\n", "    send_msg(ai_sock, \"NAME JupyterAI\")"]}, {"cell_type": "markdown", "id": "8c050d46-6f5e-4a71-9743-cfd05f4ae402", "metadata": {"tags": []}, "source": ["### 2.3.4 开始对战/投掷调试\n", "\n", "确认AI选手已准备好，数字冰壶服务器的界面上会显示＜JupyterAI 已准备＞，如图所示。\n", "\n", "> 在投掷调试模式中，数字冰壶服务器接收到AI选手的准备信息后，会通知笨笨的对手机器人也完成准备，所以界面上还会显示＜Robot 已准备＞。\n", "\n", "<center><img src=\"img/CurlingTest3.png\"></center>\n", "\n", "点击界面上的【开始对局】按钮后，服务器会向AI选手发送“NEWGAME”消息，并会跳转到对战/投掷调试界面。\n", "\n", "每局对战或投掷调试开始时，以及后续每一个冰壶球投掷完成后，会向AI选手发送多条消息通知当前的比赛状态和冰壶球坐标。\n", "\n", "我们先看下如何解析比赛状态信息。比赛状态信息的消息代码是“SETSTATE”，该消息有四个参数，参数1是当前完成投掷数，参数2是当前完成对局数，参数3是总对局数，参数4是预备投掷者（0为持蓝色球者，1为持红色球者）。\n", "\n", "接收并解析比赛状态信息的范例代码如下所示："]}, {"cell_type": "code", "execution_count": null, "id": "beca8811-9df5-4c54-9729-45535313d94f", "metadata": {"tags": []}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    #如果消息代码是\"SETSTATE\"\n", "    if msg_code==\"SETSTATE\":\n", "        print(\"当前完成投掷数：\", msg_list[0])\n", "        print(\"当前完成对局数：\", msg_list[1])\n", "        print(\"总对局数：\", msg_list[2])\n", "        if int(msg_list[3]) == 0:\n", "            print(\"预备投掷者：蓝壶\")\n", "        else:\n", "            print(\"预备投掷者：红壶\")\n", "        break"]}, {"cell_type": "markdown", "id": "ba3f61b9-8e3e-4d51-8b86-e5eebbed7d09", "metadata": {"tags": []}, "source": ["通过对接收到的比赛状态消息进行解析，可以看到比赛共有4局，当前是第1局的第1次投掷，预备投掷者是蓝壶。\n", "\n", "在轮到当前AI选手执行投掷动作时，服务器会向当前AI选手发送“GO”消息。\n", "\n", "在接收到服务器发来的“GO”消息后，AI选手就可以进行投壶了。投壶消息代码为“BESTSHOT”，这个消息带有三个参数，参数1是冰壶投掷时的初速度v0（0≤v0≤6），参数2是冰壶投掷时的横向偏移h0（-2.23≤h0≤2.23），参数3是冰壶投掷时的初始角速度ω0（-3.14≤ω0≤3.14），消息代码和各个参数之间均用空格分隔。\n", "\n", "如下所示范例代码，在接收到\"GO\"消息后，发出带参数的\"BESTSHOT\"消息，投出了初速度为3、横向偏移为0、初始角速度为0的冰壶。"]}, {"cell_type": "code", "execution_count": null, "id": "35c97c49-1075-4e55-9752-9854c2da4162", "metadata": {"tags": []}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    #如果消息代码是\"GO\"\n", "    if msg_code == \"GO\":\n", "        print(\"============第1局第1壶============\")\n", "        #发送投壶消息：初速度为3；横向偏移为0，初始角速度为0\n", "        send_msg(ai_sock, \"BESTSHOT 3 0 0\")\n", "        break"]}, {"cell_type": "markdown", "id": "11752c47-b585-4e19-8657-c2130d035ad5", "metadata": {}, "source": ["服务器接收到来自AI选手的投壶消息后，即会根据参数中指定的初速度、横向偏移和初始角速度对冰壶的运动轨迹进行仿真并在界面上实时展示，如下图所示。\n", "\n", "> 在投掷调试模式中，数字冰壶服务器接收到AI选手的投掷信息后，会通知笨笨的对手机器人投出一个出界壶，所以界面上还会展示红壶的投壶过程，但由于投出的是出界壶，所以该壶最终不会停留在界面中。\n", "\n", "<center><img src=\"img/CurlingTest4.png\"></center>\n", "\n", "需要注意的是，数字冰壶服务器中对于场地各点的摩擦系数引入了随机变量，<b>即便是相同的参数进行投掷，每次的落点也会有少许的差别</b>。这样就为比赛增加了一些偶然的成分，更贴近实际的冰壶比赛，也更具有趣味性。\n", "\n", "在实际对战中，AI选手从接收到“GO”消息开始，到发出“BESTSHOT”消息为止，间隔时间不能超过2分钟，超过2分钟就会判超时，轮到对手继续投壶。\n", "\n", "> 在投掷调试时没有超时限值。\n", "\n", "下面我们保持投壶的初速度和初始角速度不变，改变横向偏移为0.5，观察冰壶球的落点变化。"]}, {"cell_type": "code", "execution_count": null, "id": "d15d0cc7-f58f-4eb8-93c7-68aa116b2db2", "metadata": {"tags": []}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    #如果消息代码是\"GO\"\n", "    if msg_code == \"GO\":\n", "        print(\"============第1局第2壶============\")\n", "        #发送投壶消息：初速度为3；横向偏移为0.5，初始角速度为0\n", "        send_msg(ai_sock, \"BESTSHOT 3.0 0.5 0\")\n", "        break"]}, {"cell_type": "markdown", "id": "8076dab7-de29-49dd-b8cd-fbde839a0436", "metadata": {}, "source": ["投壶结果如下图所示，对比两个壶的位置，可以看到第2壶由于有横向偏移，和第1壶相比距离场地中线更远。\n", "\n", "> 正的横向偏移会使得投出的冰壶向右偏移，而负的横向偏移会使得投出的冰壶向左偏移。\n", "\n", "<center><img src=\"img/CurlingTest5.png\"></center>\n", "\n", "接下来我们保持投壶的初速度不变，将横向偏移恢复为0，改变初始角速度为-3.14，观察冰壶球的落点变化。"]}, {"cell_type": "code", "execution_count": null, "id": "1536cb12-30cd-4f0f-b2fd-725259e7bb1b", "metadata": {"tags": []}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    #如果消息代码是\"GO\"\n", "    if msg_code == \"GO\":\n", "        print(\"============第1局第3壶============\")\n", "        #发送投壶消息：初速度为3；横向偏移为0，初始角速度为-3.14\n", "        send_msg(ai_sock, \"BESTSHOT 3.0 0 -3.14\")\n", "        break"]}, {"cell_type": "markdown", "id": "8160b0d2-4e9d-4e0f-9de4-64ad1e9e4530", "metadata": {}, "source": ["投壶结果如下图所示，对比第3壶和第1壶的位置，可以看到第3壶也偏离了场地中线，这是因为给定了初始角速度就会投出弧线球，和原有的直线投壶落点自然会有差异。\n", "\n", "> 正的初始角速度会投出偏转到右侧的弧线球，而负的初始角速度会投出偏转到右侧的弧线球。\n", "\n", "<center><img src=\"img/CurlingTest6.png\"></center>\n", "\n", "接下来我们将投壶的初速度改为2.5，保持横向偏移为0不变，初始角速度也恢复为0，观察冰壶球的落点变化。"]}, {"cell_type": "code", "execution_count": null, "id": "5dad7359-2e9e-4d2c-95e2-433bc2c27755", "metadata": {"tags": []}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    #如果消息代码是\"GO\"\n", "    if msg_code == \"GO\":\n", "        print(\"============第1局第4壶============\")\n", "        #发送投壶消息：初速度为2.5，横向偏移为0，初始角速度为0\n", "        send_msg(ai_sock, \"BESTSHOT 2.5 0 0\")\n", "        break"]}, {"cell_type": "markdown", "id": "1be49d99-d874-4a51-b797-f5e103edfdd4", "metadata": {"tags": []}, "source": ["投壶结果如下图所示，可以看到第4壶由于初速度较小，未能将冰壶投到大本营中，而是停留在了防守区内。\n", "\n", "<center><img src=\"img/CurlingTest7.png\"></center>"]}, {"cell_type": "markdown", "id": "19d601a1-029d-4adc-8f0e-fd1d22f450a9", "metadata": {}, "source": ["### 2.3.5 分析得分区局势\n", "\n", "接下来我们看下如何对得分区的冰壶位置信息进行解析。冰壶位置信息的消息代码是“POSITION”，该消息有32个参数，分别是16个冰壶球的当前坐标，顺序同当前对局投掷顺序，（0，0）坐标表示未投掷或已出界的球。\n", "\n", "接收并解析比赛状态信息和冰壶位置信息的范例代码如下所示："]}, {"cell_type": "code", "execution_count": null, "id": "5a024ea1-4de7-4c61-b745-f7d6545846c5", "metadata": {}, "outputs": [], "source": ["init_x, init_y, gote_x, gote_y = [0]*8, [0]*8, [0]*8, [0]*8\n", "while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    #如果消息代码是\"POSITION\"\n", "    if msg_code==\"POSITION\":\n", "        for n in range(8):\n", "            init_x[n], init_y[n] = float(msg_list[n*4]), float(msg_list[n*4+1])\n", "            print(\"先手第%d壶坐标为(%.4f, %.4f)\" % (n+1, init_x[n], init_y[n]))\n", "            gote_x[n], gote_y[n] = float(msg_list[n*4+2]), float(msg_list[n*4+3])\n", "            print(\"后手第%d壶坐标为(%.4f, %.4f)\" % (n+1, gote_x[n], gote_y[n]))\n", "        break"]}, {"cell_type": "markdown", "id": "d26f83a7-995e-4efe-8eba-23bf860050bd", "metadata": {}, "source": ["通过对接收到的消息进行解析，可以看到先手第1壶、第2壶、第3壶和第4壶有坐标，其余的壶由于已出界或未投掷，坐标都为(0,0)。\n", "\n", "对于有坐标的冰壶，如何判断它们是不是在大本营内呢？根据《1.3 数字冰壶平台》的内容可知，冰壶半径为0.145米，场地远端大本营圆心坐标为(2.375, 4.88)，半径为1.830米。根据这些参数，就可以算出冰壶距离大本营圆心的距离，进一步判断这个壶是否在大本营内。\n", "\n", "范例代码如下所示："]}, {"cell_type": "code", "execution_count": null, "id": "ec7bc975-dffc-44a7-8002-d78ad1c53d73", "metadata": {"tags": []}, "outputs": [], "source": ["import math\n", "\n", "#与大本营中心距离\n", "def get_dist(x,y):\n", "    House_x = 2.375\n", "    House_y = 4.88\n", "    return math.sqrt((x-House_x)**2+(y-House_y)**2)\n", "\n", "#大本营内是否有壶\n", "def is_in_house(dist):\n", "    House_R = 1.830\n", "    Stone_R = 0.145\n", "    if dist<(House_R+Stone_R):\n", "        return 1\n", "    else:\n", "        return 0\n", "\n", "for n in range(8):\n", "    if (init_x[n] > 0) and (init_y[n] > 0):\n", "        distance = get_dist(init_x[n], init_y[n])\n", "        print(\"先手方第%d壶距离大本营中心%.2f米\" % (n+1, distance))\n", "        if ( is_in_house(distance) ):\n", "            print(\"该壶在大本营内！\")\n", "        else:\n", "            print(\"该壶不在大本营内！\")"]}, {"cell_type": "markdown", "id": "66debbba-721f-4b22-88aa-7908f6f59c91", "metadata": {}, "source": ["### 2.3.6 获取每局得分及整场比赛得分\n", "\n", "重复如上所述接收消息、解析消息、发送投掷命令的过程，完成本局剩余4个壶的投掷，范例代码如下："]}, {"cell_type": "code", "execution_count": null, "id": "c7ca9827-6bed-450d-b2c1-0c4443ba5b50", "metadata": {"tags": []}, "outputs": [], "source": ["import random\n", "\n", "#循环投掷剩余的4个壶\n", "for n in range(4):\n", "    while(1):\n", "        #接收消息并解析\n", "        msg_code, msg_list = recv_msg(ai_sock)\n", "        #如果消息代码是\"GO\"\n", "        if msg_code == \"GO\":\n", "            print(\"============第1局第\" + str(n+5) + \"壶============\")\n", "            #冰壶初始速度取2.5到3.5之间的随机数\n", "            v0 = 2.5 + random.random()\n", "            #冰壶横向偏移取-1到1之间的随机数\n", "            h0 = -1.0 + 2.0*random.random()\n", "            #发送投壶消息：初速度为v0；横向偏移为h0，初始角速度为0\n", "            send_msg(ai_sock, \"BESTSHOT \" + str(v0) + \" \" + str(h0) + \" 0\")\n", "            break"]}, {"cell_type": "markdown", "id": "ebf1abde-85b8-4350-9777-0c52b7e8b337", "metadata": {"tags": []}, "source": ["每局全部八个冰壶球投掷完毕后，服务器会向AI选手发送“SCORE”消息，参数是该局得分。正分说明是自己得分，负分说明是对方得分。\n", "\n", "忽略比赛状态消息和冰壶球坐标消息，仅处理得分的范例代码如下："]}, {"cell_type": "code", "execution_count": null, "id": "caad2033-bd51-4e36-a24c-c6666009fd78", "metadata": {"tags": []}, "outputs": [], "source": ["while (1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    #如果消息代码是\"SCORE\"\n", "    if msg_code == \"SCORE\":\n", "        #从消息参数列表中获取得分\n", "        score = int(msg_list[0])\n", "        if  score > 0:\n", "            print(\"我方得\"+str(score)+\"分\")\n", "        elif score < 0:\n", "            print(\"对方得\"+str(score*-1)+\"分\")\n", "        else:\n", "            print(\"双方均未得分\")\n", "        break"]}, {"cell_type": "markdown", "id": "66cce90b-e66b-45d9-80a5-6ad2950842ad", "metadata": {}, "source": ["此时界面上也会给出当前的得分情况，如下图所示。\n", "\n", "<center><img src=\"img/CurlingTest8.png\"></center>\n", "\n", "点击【下一局】按钮，即会开始新的一局对局/投掷调试，初赛阶段和投掷调试模式设定为每场比赛四局，决赛阶段设定为每场比赛八局。\n", "\n", "运行下方代码可以在随后的三局比赛中实现随机投壶。<b>注意每一局结束后都要在数字冰壶服务器界面中点击【下一局】按钮</b>。"]}, {"cell_type": "code", "execution_count": null, "id": "d9027021-026c-4405-8a70-55c352ebb6e8", "metadata": {"tags": []}, "outputs": [], "source": ["#循环后续3局比赛\n", "for m in range(3):\n", "    #循环投掷8个壶\n", "    for n in range(8):\n", "        while(1):\n", "            #接收消息并解析\n", "            msg_code, msg_list = recv_msg(ai_sock)\n", "            #如果消息代码是\"GO\"\n", "            if msg_code == \"GO\":\n", "                print(\"============第\" +str(m+2) + \"局第\" + str(n+1) + \"壶============\")\n", "                #冰壶初始速度取2.5到3.5之间的随机数\n", "                v0 = 2.5 + random.random()\n", "                #冰壶横向偏移取-1到1之间的随机数\n", "                h0 = -1.0 + 2.0*random.random()\n", "                #发送投壶消息：初速度为v0；横向偏移为h0，初始角速度为0\n", "                send_msg(ai_sock, \"BESTSHOT \" + str(v0) + \" \" + str(h0) + \" 0\")\n", "                break"]}, {"cell_type": "markdown", "id": "0a347306-e68d-48ee-8cb8-1de227eee1dc", "metadata": {}, "source": ["全部对局结束后，服务器会向AI选手发送“TOTALSCORE”消息和“GAMEOVER”消息。“TOTALSCORE”有两个参数，参数1是执蓝壶选手总得分，参数2是执红壶选手总得分；“GAMEOVER”消息有一个参数，“WIN”代表胜利，“LOSE”代表失败，“DRAW”代表平局。"]}, {"cell_type": "code", "execution_count": null, "id": "08f04fe8-c500-4f03-987b-a3b8b70a0953", "metadata": {"tags": []}, "outputs": [], "source": ["while (1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    #如果消息代码是\"GAMEOVER\"\n", "    if msg_code == \"GAMEOVER\":\n", "        score = int()\n", "        if  msg_list[0] == \"WIN\":\n", "            print(\"我方获胜\")\n", "        elif msg_list[0] == \"LOSE\":\n", "            print(\"对方获胜\")\n", "        else:\n", "            print(\"双方平局\")\n", "        break"]}, {"cell_type": "markdown", "id": "6d80390b-9578-4441-aa0f-2c9a547c29b8", "metadata": {}, "source": ["### 2.3.7 关闭Socket连接\n", "\n", "全部对局结束后，数字冰壶服务器界面上显示的计分板也给出总比分，如下图所示。\n", "\n", "<center><img src=\"img/CurlingTest9.png\"></center>\n", "\n", "点击【返回主菜单】按钮，即可退出投掷调试模式。此时服务器会向AI选手连续发送5条空信息。而AI选手在连续检测到5条空信息之后，就应该调用socket对象的close()方法关闭socket连接。范例代码如下：\n", "\n", "> 先点击【返回主菜单】按钮再运行下方代码"]}, {"cell_type": "code", "execution_count": null, "id": "7524a581-eda9-4cf4-b676-ee3b17fee6c0", "metadata": {"tags": []}, "outputs": [], "source": ["#空消息计数器归零\n", "retNullTime = 0\n", "while (1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    #如果接到空消息则将计数器加一\n", "    if msg_code == \"\":\n", "        retNullTime = retNullTime + 1\n", "    #如果接到五条空消息则关闭Socket连接\n", "    if retNullTime == 5:\n", "        #关闭Socket连接\n", "        ai_sock.close()\n", "        print(\"已关闭socket连接\")\n", "        break"]}, {"cell_type": "markdown", "id": "15afd883-32d8-4d09-b586-e4dcf4813140", "metadata": {}, "source": ["## 小结\n", "\n", "本课结合Socket通讯的流程，从AI选手和数字冰壶客户端建立连接开始，逐行代码介绍了如何和数字冰壶服务器的收发消息，最终在数字空间中完成了一场随机投壶的独角戏比赛。\n", "\n", "在真正的数字冰壶比赛中，每次投壶就不能这么草率了。在投壶前需要对大本营状态进行分析，再结合比赛状态信息，综合考虑各种情况，制定对战策略，确定每一壶的投掷目标，并努力实现。\n", "\n", "> 如何实现投掷目标？请参考《3.数字冰壶比赛中的冰壶运动模型》<br>\n", "> 如何制定对战策略？请参考《4.数字冰壶比赛中的对战策略》"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.14"}}, "nbformat": 4, "nbformat_minor": 5}