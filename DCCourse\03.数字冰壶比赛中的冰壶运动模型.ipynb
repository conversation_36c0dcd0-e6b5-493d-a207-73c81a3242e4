{"cells": [{"cell_type": "markdown", "id": "fc640188-cbc1-4071-a0a1-82d68b3a7182", "metadata": {}, "source": ["# 第三课 数字冰壶比赛中的冰壶运动模型\n", "\n", "冰壶的运动是基于一定的物理规律来进行的。当冰壶具一定的初始速度、旋转情况下它会进行运动，并会在各种阻力下最终停止运动。为了明确它的运动规律和轨迹，需要对它进行受力分析并搭建其运动的动力学模型。\n", "\n", "## 3.1 冰壶物理模型概述\n", "\n", "冰壶比赛中使用的冰壶石一般是由花岗岩石制成的，该材料制成的冰壶石坚硬耐撞，一般使用的冰壶石为圆形半径可达14cm左右，重约19kg。\n", "\n", "从运动学的角度来看，冰壶就是一块在冰面上一边自转一边平移的大石头。冰壶的运动主要由与冰面的摩擦决定。对于冰壶这种本身和冰面接触面积很大，压强很低的物体，降低摩擦的方法，就是适度减少接触面积。一方面，接触面积小了，压强就提高了，冰熔点会下降得更多；另一方面，更小的接触面积会让摩擦产生的热量被更少的冰吸收，这样这一小部分冰就更容易融化。\n", "\n", "为了减少接触面积，冰壶的底部不是平的，而是中部内凹——其真正可以接触冰面的面积只有一个圆环，直径一般为13厘米，宽度在3-8毫米左右。这大大降低了冰壶与冰面的接触面积。\n", "\n", "<center><img src=\"img/CurlingBall.png\" width=500></center>\n", "\n", "但这还不够，如果把40斤的冰壶放在平坦的冰面上，摩擦力还是太大，冰壶前进会很困难。因此，制冰师通常会采取【冰粒】的技术，来减少冰壶与冰的接触面积。他们会在平坦的冰面上挥舞喷头洒水，喷出的水凝结成小冰粒后，会让冰面变得凹凸不平，这样冰壶放在冰面上，实际上只会和冰粒的顶点附近接触，接触面积大大减小，相应地摩擦力也变小了。\n", "\n", "<center><img src=\"img/CurlingIce.png\" width=500></center>\n", "\n", "以上这两种因素使得冰壶与冰面的接触面积很小，且冰面的变化会改变冰壶石和冰面之间的摩擦力，所以在进行冰壶动力学模型设计时，需要充分考虑这些因素。"]}, {"cell_type": "markdown", "id": "5fd83615-0ef6-4d52-8b46-e1e7aef8f187", "metadata": {}, "source": ["## 3.2 冰壶动力学模型\n", "\n", "### 3.2.1 冰壶动力学模型理论基础\n", "\n", "冰壶的实际运动过程较为复杂，根据之前所提到的冰壶与冰面的接触形式，由于冰壶与冰面间的压力比较大，在冰壶与冰面进行相对的运动时，由于摩擦力的存在，冰壶会加热冰面，从而导致部分冰面的融化，形成液态的冰膜。当冰面不再受到摩擦产生的热力效果后，形成的液态冰膜又会重新冻结。\n", "\n", "根据上面的设想及考虑到冰壶与冰面的实际接触面，在冰壶运动速度较大时，可以把冰壶的接触环分为了前后两个部分，即为接触圆环的前半圆环和后半圆环部分。\n", "\n", "在沿着冰壶质心速度的前向方向上，前半圆环会先与固体冰面接触，而后在摩擦力的作用下加热冰面，提高温度，融化这一接触部分的冰面从而产生了液体膜，而后半圆环运动到该位置时，会与之前加热形成的液体膜产生作用，当后半圆环也离开该位置时，则液体膜重新冷却凝固成普通冰面。当冰壶的运动速度较小时，可以进一步把冰壶的前后半圆环再细分为前后半圆环的内外部分，重新讨论它们与冰面之间的作用。\n", "\n", "在下面的论述中，将统一把冰壶与未经摩擦力加热的普通冰面所形成的作用力称之为“干摩擦力”，而冰壶与加热冰面产生的液态膜间所形成的作用力形象地称为“湿摩擦力”。\n", "\n", "冰壶的速度差异会导致冰壶在不同速度下的干湿摩擦在接触面分布的范围不同。根据这一特性，可以将冰壶的整个运动过程分为三个阶段，如图所示。图中w代表冰壶的旋转速度，箭头指向的方向为冰壶自身旋转的方向。\n", "\n", "<center><img src=\"img/DryWet.png\" width=300></center>\n", "\n", "冰壶运动的第一阶段，此时冰壶的前向运动速度较大，前半圆环会与冰面先进行干摩擦，并加热冰面产生液态膜，而前半圆环则会因为干摩擦的效果产生干摩擦力。而且由于冰壶速度较大，所以冰壶移动足够快，在经过圆环内部凹陷区后，液态膜还未凝固，此时后半圆环会与液态膜接触，与冰面进行湿摩擦，从而会在后半圆环上产生湿摩擦力。\n", "\n", "当冰壶运动到第二阶段时，冰壶速度有所下降，并且相较于第一阶段冰壶速度小很多。此时前半圆环的外围部分会经历干摩擦，产生干摩擦力，并且动态融化冰面，而前半圆环的内部，则会与外围已经融化了的液态膜部分进行湿摩擦，产生湿摩擦力。当冰壶的后半圆环经过凹陷区重新到达此位置时，此时液态膜已经冻结凝固。冰壶的后半圆环则又会像前半圆环一样，内部先进行干摩擦加热融化冰面，外围部分经过时则进行湿摩擦，内外部分产生不同的干湿摩擦力。\n", "\n", "而当冰壶运动到第三阶段时，此时冰壶的速度很小，它的干湿摩擦的形态又会与前两个阶段有较大的差别。而冰壶是由花岗岩石制成的，当冰壶的运动速度较为缓慢的时候，冰壶会有与液体间的粘滞力，该力会使液体加速到与冰壶的速度相同。因此在第三阶段时，冰壶的前半圆环先进行干摩擦产生液态膜，而此液态膜又会因为粘滞力的作用始终围绕在冰壶的前半部分，同时冰壶又有自身的旋转，旋转会把冰壶前半圆环后半部分的液态膜拖拽到冰壶的外围位置，最终液态膜会附着在冰壶岩石的偏右半部分上。所以最后，冰壶前半圆环右半部分位置会进行湿摩擦，产生湿摩擦力，而冰壶前半圆环没有被液态膜覆盖的部分以及后半圆环则都会进行干摩擦，产生干摩擦力。\n", "\n", "由上分析可以看出冰壶在第三阶段有相较于前两个阶段明显的差异和特征，且其干湿摩擦的分布与第一阶段的完全相反。在观看比赛或者训练的时候，经常会看到冰壶运动到最后其轨迹会有明显的弯曲，这说明第三阶段冰壶干湿摩擦的分布会提高冰壶轨迹弯曲的程度是与实际认知相符的。"]}, {"cell_type": "markdown", "id": "9f747a5b-91ba-470b-9650-2f4496261da7", "metadata": {}, "source": ["### 3.2.2 冰壶动力学模型方程\n", "\n", "如图所示建立冰壶的坐标系，坐标系的原点在冰壶的重心位置上，y轴始终与冰壶的初始运动速度方向平行，取与y轴垂直的方向为x轴的方向。在实际冰壶的建模过程中，把冰壶的顺时针旋转的方向认为是冰壶旋转速度的正方向，逆时针为负方向。\n", "\n", "<center><img src=\"img/CurlingCoordinate.png\" width=300></center>\n", "\n", "冰壶在运动过程中由于接触的冰面不同会产生相应的干湿摩擦力，且在接触环上任意一点产生的干湿摩擦力其力的方向都与该点与冰面的相对速度相反。计算干湿摩擦力在接触环上任意一点的公式如下：\n", "\n", "$$△F_w=Ku(θ)$$\n", "$$△F_d=μMg\\frac{Δθ}{2π}$$\n", "    \n", "上式中$F_d$表示干摩擦，$F_w$表示干摩擦，μ是冰壶与冰面间的动摩擦系数，$M$指冰壶的质量，$K$为定值，$u(θ)$是有关冰壶接触环与冰面相对速度的函数，且速度越大，湿摩擦力也会越大。\n", "\n", "冰壶所受到的干湿摩擦力在接触环上是连续的，因此我们可以通过积分的形式求出冰壶在整个接触环上所受到的力，为了简化计算和符合之前所分析的干湿摩擦分布形式，将不会对整个冰壶的接触环进行积分，而是对冰壶所处的每一象限进行x、y轴方向的积分。\n", "\n", "此外，冰壶的旋转运动也要受到干湿摩擦力的影响。因为干湿摩擦力在x轴的分量其方向上是不尽相同的，所以会产生旋转力矩，使冰壶的角速度发生改变。将冰壶的干湿摩擦力产生的旋转力矩进行求和，即可得到总的转动力矩，再利用以下公式，便可以求解出该力矩下冰壶的旋转角加速度。\n", "\n", "$$\\sum M=Jα$$\n", "\n", "上式中，$J$表示冰壶的转动惯量，而$α$则是冰壶在$t$时刻的瞬时角加速度。若我们把冰壶的初始旋转角速度设为$ω_0$，那么我们可得到冰壶的任意时刻的角速度为：\n", "\n", "$$ω(t)=ω_0+\\int_0^tαdt$$\n", "\n", "同样的，我们也可以将冰壶之前算得的干湿摩擦力进行求和，求解出冰壶在任意时刻下的沿x轴、y轴的线速度，设冰壶初始时刻，沿着x、y方向的初速度为$v_x0$、$v_y0$，则计算公式如下：\n", "\n", "$$v_x (t)=v_{x0}+\\int_0^t α_x dt$$\n", "$$v_y (t)=v_{y0}+\\int_0^tα_y dt$$\n", "\n", "其中$α_x$和$α_y$是由干湿摩擦合力计算得的沿着x、y方向的瞬时加速度。\n", "\n", "> 限于篇幅，此处并未给出干摩擦力、湿摩擦力在x方向、y方向的分量以及旋转力矩的展开公式，具体参见参考文献<a href=\"https://aichallenge.embedded-ai.org/api/download-file?id=64a3bae61d3482d4e449d047\" target=_blank>《The motion of a curling rock》</a>。"]}, {"cell_type": "markdown", "id": "c96e543e-4e87-4278-9913-e59cc638e964", "metadata": {}, "source": ["### 3.2.3 冰壶动力学模型的仿真实现\n", "\n", "在第二课中，我们已经看到了改变横向偏移对冰壶落点的影响。下面我们看下如何通过设定横向偏移控制投出的冰壶球撞在指定的冰壶球上。\n", "\n", "首先点击页面左上角Jupyter菜单中的[Run]菜单项，点击该该菜单项的[Start Curling Server]子菜单项，即可启动一个数字冰壶比赛服务器。\n", "\n", "然后点击数字冰壶比赛服务器界面中的【投掷调试】按钮进入投掷调试模式。\n", "\n", "下方的代码单元中整理了第二课中学习的部分代码，根据数字冰壶服务器界面中给出的连接信息修改代码中CONNECTKEY相关内容，然后运行这个代码单元，配合数字冰壶比赛服务器界面中的操作，就可以完成如下所述的流程。\n", "\n", "1. AI选手发送带参数的\"CONNECTKEY\"消息连接服务器，注意要根据数字冰壶服务器界面中给出的文本修改参数；\n", "2. 在数字冰壶比赛服务器中点击【准备】；\n", "3. AI选手在接收到\"ISREADY\"消息后发送\"READYOK\"消息确认准备完毕；\n", "4. AI选手发送带参数的\"NAME\"消息设定参赛选手名；\n", "5. 在数字冰壶比赛服务器中点击【开始对局】；\n", "6. AI选手在接收到\"GO\"消息后，发送带参数的\"BESTSHOT\"消息进行初速度为3、横向偏移为0.5、初始角速度为0的投壶；\n", "7. 投壶结束后，解析收到的\"POSITION\"消息的参数获取得分区内冰壶的坐标。"]}, {"cell_type": "code", "execution_count": null, "id": "4b8ff7a6-eff2-4c90-a84b-1c03c5b5c4f0", "metadata": {}, "outputs": [], "source": ["import socket\n", "import time\n", "\n", "#连接密钥：参照数字冰壶服务器界面中给出的连接信息填写，注意这个参数每次新启动服务器都会改变。\n", "key = \"lidandan_af84ff22-c7d1-449c-806e-51af3add1107\"\n", "#服务器主机：参照数字冰壶服务器界面中给出的连接信息填写，通常无需修改。\n", "host = 'curling-server-7788.jupyterhub.svc.cluster.local'\n", "#连接端口：参照数字冰壶服务器界面中给出的连接信息填写，通常无需修改。\n", "port = 7788\n", "\n", "#新建Socket对象\n", "ai_sock =socket.socket()\n", "#创建Socket连接\n", "ai_sock.connect((host,port))\n", "print(\"已建立socket连接\", host, port)\n", "\n", "#通过socket对象发送消息\n", "def send_msg(sock, msg):\n", "    print(\"  >>>> \" + msg)\n", "    #将消息数据从字符串类型转换为bytes类型后发送\n", "    sock.send(msg.strip().encode())\n", "\n", "#通过socket对象接收消息并进行解析\n", "def recv_msg(sock):\n", "    #为避免TCP粘包问题，数字冰壶服务器发送给AI选手的每一条信息均以0（数值为0的字节）结尾\n", "    #这里采用了逐个字节接收后拼接的方式处理信息，多条信息之间以0为信息终结符\n", "    buffer = bytearray()\n", "    while True:\n", "        #接收1个字节\n", "        data = sock.recv(1)\n", "        #接收到空数据或者信息处终结符(0)即中断循环\n", "        if not data or data == b'\\0':\n", "            time.sleep(0.1)\n", "            break\n", "        #将当前字节拼接到缓存中\n", "        buffer.extend(data)\n", "    #将消息数据从bytes类型转换为字符串类型后去除前后空格\n", "    msg_str = buffer.decode().strip()\n", "    print(\"<<<< \" + msg_str)\n", "\n", "    #用空格将消息字符串分隔为列表\n", "    msg_list = msg_str.split(\" \")\n", "    #列表中第一个项为消息代码\n", "    msg_code = msg_list[0]\n", "    #列表中后续的项为各个参数\n", "    msg_list.pop(0)\n", "    #返回消息代码和消息参数列表\n", "    return msg_code, msg_list\n", "\n", "#通过socket对象发送连接密钥\n", "send_msg(ai_sock, \"CONNECTKEY:\" + key)\n", "#初始化先手壶和后手壶的坐标列表\n", "init_x, init_y, gote_x, gote_y = [0]*8, [0]*8, [0]*8, [0]*8\n", "\n", "while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    #如果消息代码是\"ISREADY\"\n", "    if msg_code == \"ISREADY\":\n", "        #发送\"READYOK\"\n", "        send_msg(ai_sock, \"READYOK\")\n", "        #发送\"NAME\"和AI选手名\n", "        send_msg(ai_sock, \"NAME JupyterAI\")\n", "    if msg_code == \"GO\":\n", "        print(\"============先手方第1壶============\")\n", "        #发送投壶消息：初速度为3；横向偏移为0，初始角速度为0\n", "        send_msg(ai_sock, \"BESTSHOT 3.0 0 0\")\n", "    if msg_code==\"POSITION\":\n", "        for n in range(8):\n", "            init_x[n], init_y[n] = float(msg_list[n*4]), float(msg_list[n*4+1])\n", "            gote_x[n], gote_y[n] = float(msg_list[n*4+2]), float(msg_list[n*4+3])\n", "        if (init_x[0]>0.0001) or (init_y[0]>0.0001):\n", "            print(\"先手第1壶坐标为(%.4f, %.4f)\" % (init_x[0], init_y[0]))\n", "            break"]}, {"cell_type": "markdown", "id": "1d3ddcb4-7c3d-45c6-a763-7812cc820067", "metadata": {}, "source": ["投壶结果如下图所示。\n", "\n", "<center><img src=\"img/CurlingMotion1.png\"></center>\n", "\n", "将上面解析出来的第1壶的坐标代入经过推导得到的经验公式，设置第二壶投壶的初速度和横向偏移，即可实现将第一壶击飞。\n", "\n", "范例代码如下所示。"]}, {"cell_type": "code", "execution_count": null, "id": "59d4f3da-1c27-40cd-b3ca-4484914ab178", "metadata": {"tags": []}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    if msg_code == \"GO\":\n", "        print(\"============先手方第2壶============\")\n", "        v0 = float(3.613-0.12234*init_y[0]+1)\n", "        h0 = float(init_x[0]-2.375)\n", "        #发送投壶消息：初速度为v0；横向偏移为h0，初始角速度为0\n", "        send_msg(ai_sock, \"BESTSHOT \" + str(v0) + \" \" + str(h0) + \" 0\")\n", "    if msg_code==\"POSITION\":\n", "        for n in range(8):\n", "            init_x[n], init_y[n] = float(msg_list[n*4]), float(msg_list[n*4+1])\n", "            gote_x[n], gote_y[n] = float(msg_list[n*4+2]), float(msg_list[n*4+3])\n", "        if (init_x[1]>0.0001) or (init_y[1]>0.0001):\n", "            print(\"先手第2壶坐标为(%.4f, %.4f)\" % (init_x[1], init_y[1]))\n", "            break"]}, {"cell_type": "markdown", "id": "dbaefe7e-7484-4f2f-9b89-d0bf492f552a", "metadata": {}, "source": ["投壶结果如下图所示，大本营中的壶是第2壶。可以看到，两个冰壶的碰撞遵守动量守恒定律，后发的壶会停留在撞击位置，而被碰撞的壶将会被击飞。\n", "\n", "<center><img src=\"img/CurlingMotion2.png\"></center>\n", "\n", "我们还可以将上面解析出来的第2壶的坐标代入经过推导得到的经验公式，投出第三个壶停留在第二个壶下方的自由防守区内。\n", "\n", "范例代码如下所示。"]}, {"cell_type": "code", "execution_count": null, "id": "c1a5e2e3-f858-4b4a-9867-8f01fbf8741f", "metadata": {"tags": []}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    if msg_code == \"GO\":\n", "        print(\"============先手方第3壶============\")\n", "        v0 = float(3.613-0.12234*init_y[1]-0.3)\n", "        h0 = float(init_x[1]-2.375)\n", "        #发送投壶消息：初速度为v0；横向偏移为h0，初始角速度为0\n", "        send_msg(ai_sock, \"BESTSHOT \" + str(v0) + \" \" + str(h0) + \" 0\")\n", "    if msg_code==\"POSITION\":\n", "        for n in range(8):\n", "            init_x[n], init_y[n] = float(msg_list[n*4]), float(msg_list[n*4+1])\n", "            gote_x[n], gote_y[n] = float(msg_list[n*4+2]), float(msg_list[n*4+3])\n", "        if (init_x[2]>0.0001) or (init_y[2]>0.0001):\n", "            print(\"先手第3壶坐标为(%.4f, %.4f)\" % (init_x[2], init_y[2]))\n", "            break"]}, {"cell_type": "markdown", "id": "61c9bf5a-1aae-45f2-8610-86573221b614", "metadata": {}, "source": ["投壶结果如下图所示。可以看到，第3壶会挡住后续壶的行进路线，从而起到保护大本营内的第2壶不被撞飞的作用，这样的壶称为保护壶。\n", "\n", "<center><img src=\"img/CurlingMotion3.png\"></center>\n", "\n", "接下来我们再尝试一下用快速壶击飞得分区中央的壶（先手方第2壶），范例代码如下所示："]}, {"cell_type": "code", "execution_count": null, "id": "0ae636c4-7e89-4e40-95af-a271ca9b26dc", "metadata": {}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    if msg_code == \"GO\":\n", "        print(\"============先手方第4壶============\")\n", "        v0 = 3.6 - 0.123*init_y[1] + 1\n", "        h0 = init_x[1] - 2.375\n", "        #发送投壶消息：初速度为v0；横向偏移为h0，初始角速度为0\n", "        send_msg(ai_sock, \"BESTSHOT \" + str(v0) + \" \" + str(h0) + \" 0\")\n", "    if msg_code==\"POSITION\":\n", "        for n in range(8):\n", "            init_x[n], init_y[n] = float(msg_list[n*4]), float(msg_list[n*4+1])\n", "            gote_x[n], gote_y[n] = float(msg_list[n*4+2]), float(msg_list[n*4+3])\n", "        if (init_x[3]>0.0001) or (init_y[3]>0.0001):\n", "            print(\"先手第4壶坐标为(%.4f, %.4f)\" % (init_x[3], init_y[3]))\n", "            break"]}, {"cell_type": "markdown", "id": "519402ca-31a6-41d7-8dac-d7c2862a2cb7", "metadata": {}, "source": ["投壶结果如下图所示。可以看到，第4壶瞄准的是在靶心的第2壶。但由于有了保护壶（第3壶）的存在，它只是将第3壶击入了得分区，第2壶依然稳居靶心。\n", "\n", "<center><img src=\"img/CurlingMotion4.png\"></center>\n", "\n", "这时如果想要突破保护球的封锁击飞得分区中的指定冰壶，就需要有战术布局了。运行下方范例代码投出第5壶。"]}, {"cell_type": "code", "execution_count": null, "id": "862cc169-65d7-4a98-9e83-d97eaf79e4b4", "metadata": {}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    if msg_code == \"GO\":\n", "        print(\"============先手方第5壶============\")\n", "        #发送投壶消息：初速度为2.7；横向偏移为-1，初始角速度为0\n", "        send_msg(ai_sock, \"BESTSHOT 2.7 -1 0\")\n", "    if msg_code==\"POSITION\":\n", "        for n in range(8):\n", "            init_x[n], init_y[n] = float(msg_list[n*4]), float(msg_list[n*4+1])\n", "            gote_x[n], gote_y[n] = float(msg_list[n*4+2]), float(msg_list[n*4+3])\n", "        if (init_x[4]>0.0001) or (init_y[4]>0.0001):\n", "            print(\"先手第5壶坐标为(%.4f, %.4f)\" % (init_x[4], init_y[4]))\n", "            break"]}, {"cell_type": "markdown", "id": "af7e3a2a-7edf-4f45-9bfd-b20f0b906bab", "metadata": {}, "source": ["投壶结果如下图所示。可以看到，第5壶好像投得有点奇怪——既没有得分？又没有保护得分区中的某个壶？这其实是一个“策应壶”。\n", "\n", "<center><img src=\"img/CurlingMotion5.png\"></center>\n", "\n", "它的“策应”体现在哪里呢？继续运行下方范例代码再看：\n", "\n", "> 为了看清楚冰壶的路线，建议将数字冰壶服务器的渲染速度调整为二倍速。"]}, {"cell_type": "code", "execution_count": null, "id": "5f52eebc-2869-48e0-b509-a7fe8a4dfdba", "metadata": {}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    if msg_code == \"GO\":\n", "        print(\"============先手方第6壶============\")\n", "        v0 = 3.6 - 0.123*init_y[4] + 1.5\n", "        h0 = init_x[4] - 2.375 - 0.08\n", "        #发送投壶消息：初速度为v0；横向偏移为h0，初始角速度为0\n", "        send_msg(ai_sock, \"BESTSHOT \" + str(v0) + \" \" + str(h0) + \" 0\")\n", "    if msg_code==\"POSITION\":\n", "        for n in range(8):\n", "            init_x[n], init_y[n] = float(msg_list[n*4]), float(msg_list[n*4+1])\n", "            gote_x[n], gote_y[n] = float(msg_list[n*4+2]), float(msg_list[n*4+3])\n", "        if (init_x[5]>0.0001) or (init_y[5]>0.0001):\n", "            print(\"先手第6壶坐标为(%.4f, %.4f)\" % (init_x[5], init_y[5]))\n", "            break"]}, {"cell_type": "markdown", "id": "57b10136-d28a-46e6-82a7-38c6ca7aa766", "metadata": {}, "source": ["投壶结果如下图所示。可以看到，第6壶快速击中了第5壶的左下侧，将之前看似无用的第5壶撞向靶心，通过这种传击的方式击飞了靶心的第2壶。\n", "\n", "<center><img src=\"img/CurlingMotion6.png\"></center>\n", "\n", "观察当前得分区中壶的分布，可以看到虽然第4壶挡住了直通靶心的路线，但靶心右侧是没有遮挡的，我们还可以通过同时设置横向偏移和初始角速度，绕过第4壶，从靶心右侧进营。范例代码如下所示："]}, {"cell_type": "code", "execution_count": null, "id": "06f87fe3-cdcd-41de-8067-a37268cef4e6", "metadata": {}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    if msg_code == \"GO\":\n", "        print(\"============先手方第7壶============\")\n", "        h0 = init_x[3] - 2.375 + 0.75    #绕过保护壶\n", "        #发送投壶消息：初速度为3；横向偏移为h0，初始角速度为-3.14\n", "        send_msg(ai_sock, \"BESTSHOT 3 \" + str(h0) + \" -3.14\")\n", "    if msg_code==\"POSITION\":\n", "        for n in range(8):\n", "            init_x[n], init_y[n] = float(msg_list[n*4]), float(msg_list[n*4+1])\n", "            gote_x[n], gote_y[n] = float(msg_list[n*4+2]), float(msg_list[n*4+3])\n", "        if (init_x[6]>0.0001) or (init_y[6]>0.0001):\n", "            print(\"先手第7壶坐标为(%.4f, %.4f)\" % (init_x[6], init_y[6]))\n", "            break"]}, {"cell_type": "markdown", "id": "4b555098-2153-4661-b9c9-1eca05343858", "metadata": {}, "source": ["投壶结果如下图所示，第7壶成功旋进靶心。\n", "\n", "<center><img src=\"img/CurlingMotion7.png\"></center>\n", "\n", "类似地，我们还可以投出一个速度更高的旋转壶，绕过保护壶将已在靶心的第7壶击飞。范例代码如下所示："]}, {"cell_type": "code", "execution_count": null, "id": "fd502372-9452-4a81-b7b4-5aa818c13274", "metadata": {}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    if msg_code == \"GO\":\n", "        print(\"============先手方第8壶============\")\n", "        h0 = init_x[3] - 2.375 + 0.75    #绕过保护壶\n", "        #发送投壶消息：初速度为3；横向偏移为h0，初始角速度为-3.14\n", "        send_msg(ai_sock, \"BESTSHOT 4 \" + str(h0) + \" -3.14\")\n", "    if msg_code==\"POSITION\":\n", "        for n in range(8):\n", "            init_x[n], init_y[n] = float(msg_list[n*4]), float(msg_list[n*4+1])\n", "            gote_x[n], gote_y[n] = float(msg_list[n*4+2]), float(msg_list[n*4+3])\n", "        if (init_x[7]>0.0001) or (init_y[7]>0.0001):\n", "            print(\"先手第8壶坐标为(%.4f, %.4f)\" % (init_x[7], init_y[7]))\n", "            break"]}, {"cell_type": "markdown", "id": "9ca48893-6c6c-45af-99a1-ce3a0ef39ddc", "metadata": {}, "source": ["投壶结果如下所示。第8壶如我们所料，绕过了保护壶将已在靶心的第7壶击飞。\n", "\n", "<center><img src=\"img/CurlingMotion8.png\"></center>\n", "\n", "至此当前局已经结束，关于投壶功能的展示部分也告一段落。还有很多种更精妙的对战策略，我们会在后面的课程中详细讲解。"]}, {"cell_type": "markdown", "id": "118f6ba6-7f43-48c4-8678-99b58a7292a5", "metadata": {"tags": []}, "source": ["## 3.3 擦冰对冰壶运动的影响\n", "\n", "考虑到冰壶在冰粒上产生的压强能达到好几MPa（具体值与温度和应变率有关），而冰壶赛场的冰面上通过喷洒细小的热水滴而形成了磨砂感较强的粗糙表面，有时候小冰粒还会发生断裂形成更小的冰渣和碎片，这都会增加冰壶和冰面的摩擦力。如果想要减少摩擦，常用的办法之一就是摩擦冰面，也就是【擦冰】。\n", "\n", "通过擦冰可以瞬时提高冰的表面温度，使冰面局部熔化，并再次凝结为较为光滑的表面，从而减低摩擦阻力，提高冰壶滑行速度，使冰壶走得更远。这种做法听上去非常暴力，实验上也很难精确测定到底会有多高的温度提升，只是知道大概应该在1℃的量级上。但是实践表明，大力是可以出奇迹的……特别是当石壶前方两侧刷冰的力道不同时，还可以使石壶受到左右不对称的摩擦力，从而起到拐弯的作用。\n", "\n", "<table>\n", "    <tr><td><img src=\"img/SweepCurve.png\" width=440px></td>\n", "        <td><img src=\"img/SweepIce.png\" width=200px></td></tr>\n", "</table>\n", "\n", "在上一局中，我们已经学习了冰壶动力学模型，掌握了进营、击飞、占位等各种操作。下面我们看下如何使用擦冰命令改变冰壶的落点。\n", "\n", "然后点击数字冰壶比赛服务器计分板界面正上方的【下一局】按钮开启新的一局对战。\n", "\n", "运行下方代码单元，AI选手在接收到\"GO\"消息后，会发送带参数的\"BESTSHOT\"消息进行初速度为2.76、横向偏移为-1.4、初始角速度为0的投壶。"]}, {"cell_type": "code", "execution_count": null, "id": "d11ffda5-dcec-447b-9eb9-621c5198039b", "metadata": {"tags": []}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    #如果消息代码是\"SETSTATE\"\n", "    if msg_code==\"SETSTATE\":\n", "        if int(msg_list[0]) == 0:\n", "            print(\"开始新的一局！\")\n", "    if msg_code == \"GO\":\n", "        print(\"============先手方第1壶============\")\n", "        #发送投壶消息：初速度为2.76；横向偏移为-1.4，初始角速度为0\n", "        send_msg(ai_sock, \"BESTSHOT 2.76 -1.4 0\")\n", "        break"]}, {"cell_type": "markdown", "id": "b0c6cfda-a5bf-4ef7-a392-7425200483a1", "metadata": {}, "source": ["投壶结果如下图所示。\n", "\n", "<center><img src=\"img/CurlingSweep1.png\"></center>\n", "\n", "冰壶投出后，在经过冰壶场地中线时，服务器会向当前AI选手发送“MOTIONINFO”消息，在参数中给出当前冰壶的坐标、速度和角速度。AI选手接收到该消息可以根据实际情况选择是否发送“SWEEP”消息控制擦冰，这个消息的参数是擦冰距离。\n", "\n", "> 注意：如果数字冰壶服务器处于“N倍速”渲染模式下，“SWEEP”消息无效。\n", "\n", "在投壶后解析\"MOTIONINFO\"消息，并给出擦冰指令的范例代码如下所示："]}, {"cell_type": "code", "execution_count": null, "id": "6289f8d6-7986-4a91-a315-f131fd2e0a04", "metadata": {}, "outputs": [], "source": ["while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    #如果消息代码是\"GO\"\n", "    if msg_code==\"SETSTATE\":\n", "        if int(msg_list[3]) == 0:\n", "            break\n", "\n", "for m in range(7):\n", "    while(1):\n", "        #接收消息并解析\n", "        msg_code, msg_list = recv_msg(ai_sock)\n", "        #如果消息代码是\"GO\"\n", "        if msg_code == \"GO\":\n", "            print(\"============先手方第\" +str(m+2) + \"壶============\")\n", "            h0 = -1.4 + (m+1)*0.4\n", "            #发送投壶消息：初速度为2.76；横向偏移为h0，初始角速度为0\n", "            send_msg(ai_sock, \"BESTSHOT 2.76 \" + str(h0) + \" 0\")\n", "        #如果消息代码是\"MOTIONINFO\"\n", "        if msg_code == \"MOTIONINFO\":\n", "            print(\"当前冰壶经过坐标点(%s,%s)时的x方向速度为%s，y方向速度为%s, 角速度为%s。\" % \n", "                  (msg_list[0], msg_list[1], msg_list[2], msg_list[3], msg_list[4]))\n", "            d0 = (m+1)*2\n", "            #发送擦冰消息：擦冰距离d0米\n", "            send_msg(ai_sock, \"SWEEP \" + str(d0))\n", "            break"]}, {"cell_type": "markdown", "id": "1da0ef30-2d7d-4d4e-8d47-7af7c391c309", "metadata": {}, "source": ["投壶结果如下图所示。\n", "\n", "<center><img src=\"img/CurlingSweep2.png\"></center>\n", "\n", "对比第一壶和后7壶的位置，可以观察到如下情况：\n", "\n", "- 在同样的初速度下，擦冰会使冰壶走的更远；\n", "- 擦冰距离越长，冰壶走的越远；\n", "- 12米大概是有效擦冰距离的上限。"]}, {"cell_type": "markdown", "id": "3cd6d8ca-559e-4a39-8401-10f137474967", "metadata": {"tags": []}, "source": ["## 小结\n", "\n", "本课中介绍了冰壶的物理模型、动力学模型以及擦冰对冰壶运动的影响，并且给出了投壶击飞、投保护壶、投策应壶以及实现擦冰的范例代码。\n", "\n", "通过对得分区局势的分析，结合简单的击飞、保护策略就可以编写一个简单的AI选手，而擦冰可以在当前速度范围上限的基础上，进一步对冰壶进行加速，在有特殊需要的情况下，是非常有用的。但想要在复杂的对战中取得胜利，还需要进一步学习对战策略。\n", "\n", "在学习下一课之前，务必要运行下方代码关闭Socket连接。"]}, {"cell_type": "code", "execution_count": null, "id": "f6c606f9-a122-4e4d-b0ab-2df929e3e59d", "metadata": {"tags": []}, "outputs": [], "source": ["#关闭Socket连接\n", "ai_sock.close()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.14"}}, "nbformat": 4, "nbformat_minor": 5}