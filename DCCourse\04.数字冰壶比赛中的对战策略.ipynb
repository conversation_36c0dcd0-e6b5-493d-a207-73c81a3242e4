{"cells": [{"cell_type": "markdown", "id": "44a8788a-7cbd-4b14-b6fb-e73799a24b05", "metadata": {}, "source": ["# 第四课 数字冰壶比赛中的对战策略\n", "\n", "## 4.1 数字冰壶比赛中的动量守恒\n", "\n", "不论冰壶的运动模型有多复杂，在冰壶比赛得分区中最激动人心的击壶则永远遵循基本的动量守恒原理。后投的壶可以将先投的壶击飞，并且还有可能产生传击（A壶击飞B壶，B壶又击飞C壶）和双飞（A壶同时击飞B壶和C壶）乃至多飞等精彩的场面。\n", "\n", "<center><img src=\"img/CurlingStrike.gif\" width=600px></center>\n", "\n", "正是由于这个动量守恒原理的存在，才使得冰壶运动的关键不在于速度快或者力量大，而是策略与战术的运用。每局比赛中，每一次投壶都是根据现场的状态随机应变。就像围棋一样，看似平静实则暗流涌动，而这种瞬息万变也正是冰壶运动的魅力。"]}, {"cell_type": "markdown", "id": "02ede78e-4851-4ad3-b252-084e8bd6a593", "metadata": {}, "source": ["## 4.2 冰壶比赛中的战术分析\n", "\n", "了解了基本规则后，我们会发现冰壶运动可比玻璃弹珠游戏复杂多了！场上队员要思考的问题包括但不限于：为了获得加分资格，如何让冰壶尽可能接近大本营圆心？费尽心思投出完美一击后，如何保护胜利果实？多个防守冰壶的位置如何布置，才能最高效率地化作拦路虎？面对对方严密的防守阵型，到底是凭借投掷技术见缝插针夺得分数，还是以蛮力全部撞开，凭最后一击直取圆心？还有一个最重要的问题：对方到底采取的是哪种战术，我方是否需要针对性地加以调整？\n", "\n", "### 4.2.1 竞技战术指导思想\n", "\n", "冰壶比赛是多局比赛，根据不同的局数以及此前几局的胜负状态，各局的战术策略必然是不同的。总得来说，会将比赛分为三个阶段。\n", "\n", "- 非决胜阶段：冰壶比赛的前几局，一般主要是防守为主，熟悉场地状态与对方临场状态；\n", "- 次决胜阶段：冰壶比赛的中间几局，制定符合本队的进攻战术，为后几局争取比分优势；\n", "- 决胜阶段：冰壶比赛的后几局，根据剩余局数，实时决策，夺取比赛胜利。\n", "\n", "由于所有人投掷完成后才开始计分，所以后手投掷的队伍拥有巨大优势，可以根据场上的石壶分布来专门制定投掷策略。因此先手方一般为防守方，目标是让对方最多只得1分，或趁机偷得本场得分；而后手方则为进攻方，目标是让己方尽可能多的获得分数，最终只得1分的话就算是本局进攻失败。\n", "\n", "和通常的直观理解相反，冰壶比赛中的防守战术指的是是将冰壶投掷到大本营内，诱导对方进行击打。而进攻战术则是指将冰壶投掷在自由防守区进行占位，为后续布局抢占先机。\n", "\n", "冰壶比赛中常用的投掷策略及其含义如下表所示。\n", "\n", "|投掷策略|具体含义|\n", "|:---:|:---:|\n", "|投进|表示投进营垒的有效冰壶|\n", "|占位|表示投在非营垒区的冰壶|\n", "|保护|表示挡住可以击打营垒区内得分壶路线的冰壶|\n", "|传进|将己方冰壶传进的同时保护了己方冰壶|\n", "|分进|将己方冰壶传进得分区或者清理对方的保护壶|\n", "|粘贴|将冰壶投掷粘贴于指定冰壶旁|\n", "|击打|将对方冰壶打出后停留于其位置上的冰壶|\n", "|击走|除了将对方冰壶打出之外还能占据不错的防守位置或得分|\n", "|清空|将对方与己方的冰壶同时击出局|\n", "|双击|将对方两个或两个以上的冰壶击出的同时留在营垒内的冰壶|\n", "|传击|通过击打产生的多壶碰撞将对方冰壶击出的同时使己方冰壶占据优势位置|\n", "|溜壶|故意不接触任何冰壶的投法|\n", "\n", "为了方便布置战术时更好的描述投壶位置，冰壶场地从远端前掷线至底线被划分为十个区域。1至3区在前掷线至大本营之间，主要用于放置防守壶，4-10区为大本营也就是得分区。\n", "\n", "<center><img src=\"img/CurlingArea.png\" width=300px></center>"]}, {"cell_type": "markdown", "id": "501fda4b-e207-43ac-a45e-a68f8215ff15", "metadata": {}, "source": ["### 4.2.2 开局战术分析\n", "\n", "先手开局一般会是防守战略，将球打进大本营。第二局和第三局会根据当前比分对策略进行调整。如果比分领先可以选择中路进营或者控制中路，如果比分落后可以选择占位发起进攻。\n", "\n", "<table>\n", "    <tr><td><img src=\"img/CurlingArea1.png\" width=200px></td>\n", "        <td><img src=\"img/CurlingArea2.png\" width=190px></td>\n", "        <td><img src=\"img/CurlingArea3.png\" width=195px></td></tr>\n", "    <tr><td align=center>中路5-7区进营</td>\n", "        <td align=center>中路3区占位</td>\n", "        <td align=center>中路2区占位</td></tr>\n", "</table>\n", "\n", "后手整体策略是保证比分和对方接近，争取能够获得2分。如果不能得到2分，后手可放弃得1分的局面，做出一个空局。后手的策略都是针对先手做出的反应。\n", "\n", "例如，针对先手的中路5-7区进营，后手可以采用打定或打甩将先手球击打出界。\n", "> 打定：用自己的一个壶，将对方的壶击出大本营后，自己的壶定住不动停留在大本营。<br>\n", "> 打甩：用自己的一个壶，将对方的球击出大本营后，自己的壶继续前进到预设位置。\n", "\n", "<table>\n", "    <tr><td><img src=\"img/CurlingArea4.png\" width=200px></td>\n", "        <td><img src=\"img/CurlingArea5.png\" width=198px></td></tr>\n", "    <tr><td align=center>打定</td>\n", "        <td align=center>打甩</td></tr>\n", "</table>\n", "\n", "又如：针对先手中路3区占位，后手方可以轻分先手方中路3区占位壶。\n", "> 分壶：将对方的壶打到两边但又刚刚好不出界，同时自己的壶可以进大本营或者出界。\n", "\n", "<table>\n", "    <tr><td><img src=\"img/CurlingArea6.png\" width=200px></td>\n", "        <td><img src=\"img/CurlingArea7.png\" width=198px></td></tr>\n", "    <tr><td align=center>轻分前</td>\n", "        <td align=center>轻分后</td></tr>\n", "</table>\n", "\n", "又如：针对先手的中路2区占位，后手可以绕过占位壶进入T线后8、9区位置。\n", "\n", "<table>\n", "    <tr><td><img src=\"img/CurlingArea8.png\" width=200px></td></tr>\n", "    <tr><td align=center>边路进营</td></tr>\n", "</table>"]}, {"cell_type": "markdown", "id": "43827691-5425-45ef-8103-8253bbbe08cd", "metadata": {}, "source": ["### 4.2.3 决胜战术分析\n", "\n", "在决胜局之前，先手方分数领先时可以继续执行防守战术；先手方落后或者均势时通常在开局实行进攻战术；保持绝对领先的先手方可以选择把壶直接打出界，以免被对方作为对旋壶的保护。先手方在比分领先或相同时，可采取防守战略诱导对方击打冰壶，采取送1分的方式获得下一轮的后手权。先手方的主要目的是确保对方不会得到2分。\n", "\n", "在决胜局之前，后手方本身就占据优势，因此需要将比分拉到2+分。为避免对方偷分，可以实施边路进攻战术，让中路开放，为最后一垒提供机会。也后手方可以选择打甩、打定做成0：0空局，以保证自己下一轮仍然是后手。\n", "\n", "到了决胜局，先手方领先时，倾向于使用防守战术。先手方落后或双方比分相等，倾向于使用进攻战术；\n", "\n", "后手方在决胜局是占有一定优势的，当其比分于先手方持平或者领先时，其倾向使用防守战略，保持中路畅通，为四垒最后一击铺路。后手方若落后，则需处理先手方的策略。面对先手方的进攻战略，后手方可以进行边区占位。\n", "<table>\n", "    <tr><td><img src=\"img/CurlingArea9-1.png\" width=198px></td>\n", "        <td><img src=\"img/CurlingArea9-2.png\" width=200px></td></tr>\n", "    <tr><td align=center colspan=2>边区占位</td></tr>\n", "</table>\n", "\n", "后手方也可以进行中路旋进。\n", "\n", "> 旋进：让冰壶旋转投出，以弧线轨迹避过对方的障碍。\n", "\n", "<table>\n", "    <tr><td><img src=\"img/CurlingArea10-1.png\" width=198px></td>\n", "        <td><img src=\"img/CurlingArea10-2.png\" width=200px></td></tr>\n", "    <tr><td align=center colspan=2>中路旋进</td></tr>\n", "</table>\n", "\n", "后手方也可采取轻分，将中路障碍扫清。\n", "<table>\n", "    <tr><td><img src=\"img/CurlingArea6.png\" width=200px></td>\n", "        <td><img src=\"img/CurlingArea7.png\" width=198px></td></tr>\n", "    <tr><td align=center>轻分前</td>\n", "        <td align=center>轻分后</td></tr>\n", "</table>"]}, {"cell_type": "markdown", "id": "eff0381f-6f6a-4085-929d-c2044c031135", "metadata": {"tags": []}, "source": ["## 4.3 数字冰壶平台中的对战调试\n", "\n", "在前面的课程中，我们一直用的是数字冰壶服务器提供的投掷调试模式，该模式下服务器启动的对手机器人将持续投掷出界壶，方便AI选手测试和服务器的Socket通讯，可以测试读取并处理得分区局势，还可以通过调整投掷参数、擦冰参数测试投壶操作。\n", "\n", "但如果想要调试对战策略，就必须实现两个AI选手在数字空间中的的对战。本节课程中我们来演示下，如何在jupyter中启动一个AI选手之后，再运行python脚本启动另外一个AI选手实现对战。\n", "\n", "### 4.3.1 在jupyter页面中启动AI选手\n", "\n", "首先点击页面左上角Jupyter菜单中的[Run]菜单项，点击该该菜单项的[Start Curling Server]子菜单项，即可启动一个数字冰壶比赛服务器。\n", "\n", "然后点击数字冰壶比赛服务器界面中的【四局制】按钮进入初赛模式，如下图所示。\n", "\n", "<center><img src=\"img/CurlingServer4.png\"></center>\n", "\n", "在当前课程页面左侧目录树区域中可以看到一个名为AIRobot.py的Python脚本，该脚本中将前面课程中学习的代码全部整理到了AIRobot类库中。\n", "\n", "下方给出了调用该类库创建对象并处理消息的脚本，实际调用前<b>注意要根据数字冰壶服务器界面中提供的信息修改变量key的赋值</b>。\n", "\n", "> 如果要修改投掷策略，可以定义子类继承AIRobot类，并重写父类的get_bestshot()方法。"]}, {"cell_type": "code", "execution_count": null, "id": "f574dd98-f635-4754-9f87-e6c4c702d49a", "metadata": {"tags": []}, "outputs": [], "source": ["from AIRobot import AIRobot\n", "\n", "#连接密钥：参照数字冰壶服务器界面中给出的连接信息填写，注意这个参数每次新启动服务器都会改变。\n", "key = \"lidandan_66323abd-7414-4e52-94a9-6c75d5b21c11\"\n", "\n", "#初始化AIRobot对象\n", "myrobot = AIRobot(key, name=\"JupyterAI\", host=\"curling-server-7788.jupyterhub.svc.cluster.local\", port=7788)\n", "#AIRobot对象开始接收并处理消息\n", "myrobot.recv_forever()"]}, {"cell_type": "markdown", "id": "91a797c3-571e-4a1e-b4d1-14815ff5df16", "metadata": {}, "source": ["### 4.3.2 运行Python脚本启动AI选手\n", "\n", "对于已经编写好的Python脚本AIRobot.py，也可以在控制台中直接运行该脚本。\n", "\n", "在控制台中运行AIRobot.py脚本启动AI选手的操作流程如下：\n", "\n", "1. 在目录树区域中双击AIRobot.py脚本打开Python脚本编辑页面，<b>注意要根据数字冰壶服务器界面中提供的信息修改变量key的赋值</b>，按下Ctrl+S组合键保存修改；\n", "\n", "2. 在Python脚本编辑页面中点击鼠标右键，在弹出的菜单中点击[Create Console for Editor]菜单项，启动控制台；\n", "\n", "<center><img src=\"img/CreateConsole.png\"></center>\n", "\n", "3. 点击Python脚本编辑页面，再点击页面左上角Jupyter菜单中的[Run]菜单项，点击该该菜单项的[Run All Code]子菜单项。\n", "\n", "<center><img src=\"img/RunAllCode.png\"></center>\n", "\n", "4. 切换到数字冰壶服务器界面中，确认界面上显示＜Player1 已连接＞＜Player2 已连接＞，点击【准备】按钮；\n", "\n", "5. 确认数字冰壶服务器界面上显示＜JupyterAI 已准备＞＜CurlingAI 已准备＞，点击【开始对局】按钮；\n", "\n", "> 此时即可在数字冰壶服务器界面中看到两个AI选手你来我往互相击飞对方的壶\n", "\n", "6. 每一局比赛完成后，点击界面中【下一局】，直至四局比赛全部完成，这一场四局制比赛就结束了；\n", "\n", "7. 点击数字冰壶服务器界面中【返回主菜单】按钮，Jupyter页面中启动的AI选手和运行Python脚本启动的AI选手都会自动结束程序运行。\n", "\n", "了解了如何启动两个AI选手进行对战，就可以在Jupyter页面中或者在Python脚本编辑页面中更改相应脚本，调整对战策略，优化自己的AI选手。"]}, {"cell_type": "markdown", "id": "e54b1649-fda8-425a-8486-48ceaa1201fb", "metadata": {}, "source": ["## 4.4 数字冰壶虚拟仿真平台中的实战范例\n", "\n", "### 4.4.1 对战AIRobot基本实例的战术制定\n", "\n", "看完刚刚这场对局，有没有感觉AIRobot很厉害呢？\n", "\n", "详细解读AIRobot.py中get_bestshot()方法的代码，会发现这个AI对手的逻辑其实非常简单：\n", "\n", "1. 如果大本营内没有壶，就向大本营中心投壶；\n", "2. 如果大本营内有壶，且离大本营中心最近的壶是己方的，就投保护壶；\n", "3. 如果大本营内有壶，且离大本营中心最近的壶是对方的，就投击飞壶。\n", "\n", "假定我方先手，了解了AI对手的基本逻辑，就可以有的放矢地制定先手战术了。\n", "\n", "1. 由于我方是先手，所以首壶投壶进营是没有意义的，一定会被对方击飞，首壶不妨投一个“策应壶”；\n", "2. 由于我方首壶投出了策应壶，AI对手的首壶一定是投壶进大本营中心，我方第2壶投一个快速的出界壶；\n", "3. 由于大本营中目前只有AI对手的首壶，AI对手第2壶一定是投保护壶，我方第3壶继续投出界壶；\n", "4. 后续数壶AI对手持续投保护壶，我方持续投出界壶；\n", "5. 直至我方第8壶，投传击壶从侧方撞击我方首壶（策应壶），击飞大本营中AI对手的首壶，占据大本营中心；\n", "6. 虽然AI对手还有1壶，但大本营中心已经被AI对手的一堆保护壶挡住，无法将我方入营壶击飞，我方获胜。\n", "\n", "### 4.4.2 对战AIRobot基本实例的战术执行\n", "\n", "点击数字冰壶比赛服务器界面中的【四局制】按钮进入初赛模式。\n", "\n", "下方范例代码中定义了一个InitRobot子类继承AIRobot类，想要修改投掷策略，就得在这个子类中重写父类的get_bestshot()方法。\n", "\n", "参照前面制定的先手战术重写父类的get_bestshot()方法后，<b>注意要根据数字冰壶服务器界面中提供的信息修改变量key的赋值</b>。运行下方范例代码，在数字冰壶服务器界面中可以看到＜Player1 已连接＞。"]}, {"cell_type": "code", "execution_count": null, "id": "953617c7-9db7-46d3-b077-5108a79a5ed1", "metadata": {}, "outputs": [], "source": ["from AIRobot import AIRobot\n", "\n", "class InitRobot(AIRobot):\n", "    #重写投掷策略\n", "    def get_bestshot(self):\n", "        if (self.show_msg):\n", "            print(\"============第%d局第%d壶============\" % (self.round_num+1, self.shot_num+1), flush=True)\n", "        #初始化先手壶和后手壶的坐标列表\n", "        init_x, init_y, gote_x, gote_y = [0]*8, [0]*8, [0]*8, [0]*8\n", "        #初始化大本营中冰壶球信息列表\n", "        stone_in_house = []\n", "        #获取大本营中冰壶球信息\n", "        for n in range(8):\n", "            stone_is_init = True\n", "            init_x[n], init_y[n] = float(self.position[n*4]), float(self.position[n*4+1])\n", "            gote_x[n], gote_y[n] = float(self.position[n*4+2]), float(self.position[n*4+3])\n", "            for (x, y) in [(init_x[n], init_y[n]), (gote_x[n], gote_y[n])]:\n", "                distance = self.get_dist(x, y)\n", "                if self.is_in_house(distance):\n", "                    stone_in_house.append([distance, x, y, stone_is_init])\n", "                stone_is_init = False\n", "\n", "        #先手首壶投策应壶\n", "        if (self.shot_num == 0):\n", "            shot_msg = \"BESTSHOT 2.7 -1 0\"\n", "        #先手第8壶投传击壶\n", "        elif (self.shot_num == 14):\n", "            v0 = 3.6 - 0.123*init_y[0] + 2\n", "            h0 = init_x[0] - 2.375 - 0.08\n", "            shot_msg = \"BESTSHOT \" + str(v0) + \" \" + str(h0) + \" 0\"\n", "        #其余壶均投出界壶\n", "        else:\n", "            shot_msg = \"BESTSHOT 6 -2.23 0\"\n", "\n", "        return shot_msg\n", "\n", "#根据数字冰壶服务器界面中给出的连接信息修改CONNECTKEY，注意这个数据每次启动都会改变。\n", "key = \"lidandan_e4c5e2b3-9940-4892-8691-bd0acf54cd56\"\n", "\n", "myrobot = InitRobot(key, name=\"JupyterAI\", host=\"curling-server-7788.jupyterhub.svc.cluster.local\", port=7788, show_msg=True)\n", "myrobot.recv_forever()"]}, {"cell_type": "markdown", "id": "97c1eda6-46f8-459b-9296-99c835b85bb9", "metadata": {}, "source": ["参照4.3.2中给出的步骤，在控制台中运行AIRobot.py脚本启动CurlingAI选手，在数字冰壶服务器界面中可以看到＜Player2 已连接＞。\n", "\n", "在数字冰壶服务器界面中点击【准备】按钮，再点击【开始对局】按钮，即可在JupyterAI和CurlingAI的对战中检验前面制定的战术是否有效。\n", "\n", "前面给出的是假定我方先手的对战策略，如果我方是后手，对战策略需要有怎样的变化呢？这个问题留给大家自行思考。:)"]}, {"cell_type": "markdown", "id": "26595e70-ccdf-4209-9cd6-0a1b787ccd32", "metadata": {"tags": []}, "source": ["## 小结\n", "\n", "本节课程中给出了一些基本的对战策略，介绍了如何启动两个AI选手进行对战调试，并以AIRobot基本实例为对手，制定了战术并在对战实践中进行检验。\n", "\n", "在这部分的测评中，我们完全了解AIRobot基本实例的逻辑策略，很容易就可以制定一个有效的战术。而在实际的对战中，我们并不了解对手的战术逻辑，就需要深思熟虑，综合运用本部分讲到的多种后手策略，步步为营地争取胜利。\n", "\n", "> 在实际的冰壶赛场上冰壶队员们日常练习的战术要比本文中呈现的更加复杂，大家想要深入学习的话，推荐搜索阅读穆亮老师的博士论文《冰壶竞技战术研究》>>>><a href=\"http://cnki.nbtvu.net.cn/KCMS/detail/detail.aspx?filename=1015407669.nh&dbcode=CDFD\" target_blank>知网链接</a><<<<。\n", "\n", "在学习战术的过程中，还要记得：<b>法有定论，兵无常形。</b>兵法有确定的理论，战斗却永远没有固定的模式。照搬兵法打仗，就如同照搬棋谱下棋、照搬套路搏击，向来都是自我取败之道。兵法提供的是活的智慧，而不是死的条文。高手对战，靠得不是固守兵法，而是要灵活运用兵法。"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.14"}}, "nbformat": 4, "nbformat_minor": 5}