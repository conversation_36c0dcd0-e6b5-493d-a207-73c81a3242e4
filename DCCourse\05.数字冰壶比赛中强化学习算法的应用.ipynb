{"cells": [{"cell_type": "markdown", "id": "b728aec7-812d-44e8-9a68-dd63b8263f9c", "metadata": {}, "source": ["# 第五课 数字冰壶比赛中强化学习算法的应用\n", "\n", "## 5.1 强化学习简介\n", "\n", "在当前阶段的很多语境下，人们所说的人工智能往往和深度学习划等号。和机器学习算法一样，深度学习算法也主要是数据驱动的。人们首先收集数据集，然后基于数据及构造模型，接着使用训练数据及对模型进行训练。\n", "\n", "依据训练数据级的类型，可以把模型的任务大致分成两类，监督学习（Supervised Learning）和无监督学习（Unsupervised Learning）。\n", "\n", "在监督学习中，训练数据往往包含一系列的特征（Feature）和特征对应的标签（Label），通过深度学习模型可以学习特征到标签的映射。\n", "\n", "而无监督学习的训练数据往往只有特征，模型训练的目的更多的是从特征中训练得到数据的内在关联，在这种类型的任务中，往往需要使用数据来构造一定的标签，从数据本身挖掘信息。注意这里所谓的标签只是给定数据后人为构造出来的，可以人为构造任何合理的标签进行预测。\n", "\n", "就整体的算法而言，深度强化学习和这两种任务，既有联系又有区别。\n", "\n", "深度强化学习和监督学习的联系在于，在深度强化学习的过程中，我们往往需要一个模型来拟合所谓的状态价值函数（给定一个状态，获取对应状态的价值）；或者有时候需要一个模型来拟合状态-动作价值函数（给定一个状态和该状态下采取的动作，获取具体的状态-动作对应的价值）。\n", "\n", "这两个函数可以通过监督学习的方法来学习的，因为算法会在强化学习的学习过程中获取奖励，从而计算出对应的状态价值或者状态-动作价值，这就相当于采集到了监督学习中的特征（状态或者状-态动作）和标签（对应的价值）。有了训练数据，就可以根据训练数据训练出一个模型。\n", "\n", "但是强化学习和监督学习也有区别，因为训练一个模型拟合函数只是过程，并不是最终目的。强化学习的最终目的是让智能体尽可能获得最大的奖励，而且很多情况下，强化学习中所谓的训练数据并不是给定的，而是随着智能体对环境的探索不断发生变化。\n", "\n", "深度强化学习同样也有一些无监督学习的特征，这个特征主要集中在对环境的探索上。因为强化学习模型在大多数情形下无法确知环境的具体情况，只能通过和环境的交互来获取环境相关信息，在这种情况下可以认为环境的信息是隐含的。\n", "\n", "就强化学习使用的模型而言，一开始并没有数据和标签来进行学习，只能通过智能体对于环境的探索过程中来得到数据，并且用数据来构造标签来学习，这也和一部分无监督学习过程中用数据来构造标签的做法类似。\n", "\n", "所以深度强化学习和深度学习（包括监督学习和无监督学习）的关系，应该是有部分交集的，如下图所示。\n", "\n", "<center><img src=\"img/RL_ML.png\" width=400></center>\n", "\n", "总体上看，强化学习理论，可以看作两部分的合成。第一部分是对环境的探索（Exploration），表现在智能体在一定条件下使用一定的策略进行尝试，并且收获尝试的奖励。第二部分则是对环境信息的利用（Exploitation），智能体通过不同的尝试，或起到关于环境的一些信息，尽可能使用这些信息来做出决策，使得从环境收获的奖励尽可能多。\n", "\n", "在实践中，这两个部分往往互相冲突，因为我们可以看到，如果鼓励智能体对环境的探索，那么可能冒很大风险，使得奖励很小，甚至为负。同样，如果尽可能鼓励环境信息的利用，很可能智能体获取的就是次优（Suboptimal）的策略，即局限在一个奖励比较小的区域，没有探索到奖励比较大的取。因此，在实际的强化学习算法中，往往需要平衡这两个方面，从而让智能体尽可能获取多的奖励。"]}, {"cell_type": "markdown", "id": "7c6cf255-e0ac-4f1f-8bf6-8c46e205894a", "metadata": {}, "source": ["# 5.2 强化学习的基本概念\n", "\n", "## 5.2.1 智能体相关概念\n", "\n", "从最广义的概念上说，通用人工智能（Artificial General Intelligence, AGI）的定义就是一个人造的智能体，这个主体能够感知周围的环境，并且能够对周围环境做出一定的响应，从而能够完成人类为其设定的目标。这个概念就和强化学习算法的一些基础思想非常接近，因此强化学习也在现阶段的研究中被视为最接近通用人工智能的方法之一。\n", "\n", "所谓强化学习（Reinforcement Learning, RL），就是给定一个学习环境（Environment）和智能体（Agent），通过调节智能体在环境中活动的策略，让智能体在活动中获取奖励（Reward），并且让智能体获得奖励最大化的过程。智能体的整个训练过程可以通过下图来表示。\n", "\n", "<center><img src=\"img/RL_process.png\" width=600></center>\n", "\n", "在图中，智能体在t时刻有一个状态（State）$S_t$，它执行了一个动作（Action）$A_t$，并获取了环境的反馈，即所谓的奖励（Reward）$R_t$，同时智能体将自身的状态更新到$S_{t+1}$。在这个过程中，我们称智能体执行动作所基于的依据为策略（Policy）π，一般来说，强化学习的策略应尽可能让智能体能够获得的奖励期望最高。\n", "\n", "这里需要注意两点。第一点是智能体和环境其实是密不可分的，决定智能体状态的不只是它本身，还有周围的环境。同时，智能体获取奖励也不是只由环境决定，在研究实际问题的时候不应该把智能体和环境简单地切割开来，需要认为这两个相互作用，共同决定了状态和奖励。第二点是智能体和环境有可能处在外界的不断干扰中，也就是说，环境会随时间不断发生变化。在构造强化学习算法的时候也需要对引起环境变化的因素做一定的考量。"]}, {"cell_type": "markdown", "id": "d70e6ca4-6810-4c8d-8de4-78cd3cfe5989", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### 5.2.2 马尔可夫决策过程\n", "\n", "在前面给出的智能体模型中，我们看到决策过程是一个顺序的过程，用状态的变化可以显示为$(s_1,a_1)→r_1→(s_2,a_2)→r_2→…→(s_t,a_t)→r_t$。\n", "\n", "定义如下一个决策过程为马尔可夫过程，即在智能体的策略π下，从状态$s_t$转移到状态$s_{t+1}$的概率完全由状态$s_t$和在改状态下采取的动作$a_t$来决定。因此，可以将这个条件概率写成如下形式：$p_π(r_t,s_{t+1}|a_t,s_t)$。\n", "\n", "同时我们注意到，这个概率只由当前状态和动作$(s_t,a_t)$来决定，意味着这个决策过程和过去的历史无关，即不需要追溯考虑更早时期如t-1,t-2,…时刻的状态和动作，只需要关注当前的状态和动作即可。所谓马尔可夫性，即意味着和过去的历史无关。对于大多数的现实问题，马尔可夫性近似是一个很好的近似，同时也减少了问题的复杂度，方便问题的抽象。在强化学习领域的算法中，大多数算法都假设问题具有马尔可夫性。\n", "\n", "如上所述的一系列连续的决策过程被称为任务（Task）。根据任务执行时间长短，可以将任务分成两类。第一类任务有一个确定的终止时间，即到达时间T之后，整个决策过程自动终止，这类任务被称为片段任务（Episodic Task）。第二类任务则可以无限执行下去，并没有一个确定的终止时间，这类任务被称为连续任务（Continuous Task）。\n", "\n", "对于一个任务执行中的某一步来说，人们一般并不会只考虑当前步骤的奖励，而是会综合考虑当前步骤后续的影响，即需要综合考虑当前步骤之后一系列步骤的综合奖励。在强化学习的任务里需要考虑到某些状态，虽然当前奖励比较小，但是未来的奖励非常丰厚。为了能够描述这个问题，这里需要定义回报（Return）。和奖励机制考虑当前步骤不同，回报考虑了所有未来的奖励。\n", "\n", "当然对于未来的奖励需要做一定的处理，这里需要引入一个概念，叫折扣系数（Discount Factor）γ(0<γ<1)。其定义如下式所示，其中式(5-1)为片段任务的回报定义，式(5-2)为连续任务的回报定义。\n", "\n", "$$G_t=\\sum_{K=t+1}^{T}γ^{k-t-1}γ_k\\tag{5-1}$$\n", "$$G_t=\\sum_{K=t+1}^{\\infty}γ^{k-t-1}γ_k\\tag{5-2}$$\n", "\n", "从直观上理解，因为折扣系数在0到1之间，当前时刻的回报最关联的应该是当前时刻的奖励，随着时间的推移，未来的奖励和当前时刻的状态，动作和奖励的关联越来越小，而且呈指数衰减，最后逐渐趋向于零。从公式中看，折扣系数起到了衰减未来奖励贡献的作用，同时还能够有效避免回报函数趋向无穷大。"]}, {"cell_type": "markdown", "id": "cce619b9-aa48-40d9-980d-3f021be284a4", "metadata": {}, "source": ["### 5.2.3 动作价值函数和状态-动作价值函数\n", "\n", "有了回报的定义，结合前面提到的条件概率$p_π(r_t,s_{t+1}|a_t,s_t)$，就可以进一步定义价值函数（Value Funtion）和动作-价值函数（Action-Value Funtion，又称Q函数），如式(5-3)和式(5-4)所示。\n", "\n", "$$V_π(S_t)=\\Bbb E_π[G_t|S_t=s_t]\\tag{5-3}$$\n", "$$Q_π(S_t,a_t)=\\Bbb E_π[G_t|S_t=s_t,A_t=a_t]\\tag{5-4}$$\n", "\n", "可以看到，式(5-3)和式(5-4)中的函数都有一个下标π，这里代表智能体的价值函数和动作-价值函数都是在给定的策略条件下计算得到的。这里的策略按照算法的不同，可以使策略神经网络生成的一组策略，也可以是随机策略或者贪心策略。\n", "\n", "另外，式(5-3)和式(5-4)中的两个函数都是对策略的期望，意味着在实际过程中，需要一段比较长的时间，在固定策略的情况下，通过智能体的不断行动，对不同状态或状态-动作进行采样，对采样的结果计算对应的回报值，取对应的期望，进而求得最终的价值函数或者动作价值函数的值。\n", "\n", "可以看出，式(5-3)是式(5-4)对于不同动作的期望，如式(5-5)所示。\n", "\n", "$$V_π(S_t)=\\sum_{a\\in A}π(a_t|s_t)Q_π(s_t,a_t)\\tag{5-5}$$\n", "\n", "其中，A为当前状态下所有可能动作的集合，而$π(a_t|s_t)$是给定当前状态$s_t$，智能体做出动作$a_t$的概率，这个条件概率可以认为描述了策略的分布。\n", "\n", "从式(5-5)可以看出，$V_π(S_t)$可以认为是$Q_π(S_t,a_t)$在当前策略下的平均表现。在很多情况下，想要知道某个动作预期能够得到回报是好于平均还是差于平均，这是就需要引入一个函数$A_π(S_t,a_t)$，我们称之为优势函数（Advantage Function），用这个函数来衡量当前动作的好坏，如式(5-6)所示。\n", "\n", "$$A_π(s_t,a_t) = Q_π(s_t,a_t) - V_π(S_t)\\tag{5-6}$$\n", "\n", "这个公式的原理同样很简单，就是用当前的动作-价值函数减去动作函数，如果函数大于零，说明这个动作的表现好于平均，反之，则差于平均。\n", "\n", "价值函数和动作-价值函数对于深度强化学习非常重要。在基于策略的深度强化学习算法中，为了了解一个策略的好坏，算法需要对当前策略的价值函数或者动作-价值函数进行估计，当一个策略能够提升这两个函数的时候，该策略才是好的策略，要尽可能往该方向优化；而算法应该尽可能避免价值函数的降低。在价值的深度强化学习算法中，由于一般情况下取得策略是平凡策略（如贪心策略，总是往价值高的状态前进），算法也需要知道具体某种动作和某种状态的价值，已确定采取的动作。\n", "\n", "为了估计这两个函数，人们引入了贝尔曼方程（Bellman Equation），通过迭代的方法来对这两个函数进行估计。如式(5-7)和式(5-8)所示。\n", "\n", "$$V'_π(S_t)=\\Bbb E_π[γ_t+γV(s_{t+1})]\\tag{5-7}$$\n", "$$Q'_π(S_t,a_t)=\\Bbb E_π[γ_t+γE_{a_{t+1}}    Q_π(s_{t+1},a_{t+1})]\\tag{5-8}$$\n", "\n", "这两个公式比较类似：假如在某一次迭代中已经有了一个$V_π(S_{t+1})$和$Q_π(s_{t+1},a_{t+1})$，为了能够得到下一次迭代的值，需要有当前迭代步骤t时间的奖励$γ_t$，结合前面所说的已经有的$V_π(S_{t+1})$和$Q_π(s_{t+1},a_{t+1})$，计算出下一步所有可能对应的状态或状态-动作函数，然后乘以折扣系数，采样求期望得到下一步迭代新的$V'_π(S_{t})$和$Q'_π(s_{t},a_{t})$。\n", "\n", "如此不断反复，知道最后对应的函数收敛（两次迭代的差值小于一定的标准）。从理解上说，可以认为$V_π(S_{t})$和$Q_π(s_{t},a_{t})$是对未来回报的一个近似，通过不断修正这个近似，最终可以让我们的价值函数和动作-价值函数收敛到正确的数值。"]}, {"cell_type": "markdown", "id": "17ab5412-ed97-4c73-a499-d5b64acbf5be", "metadata": {}, "source": ["## 5.3 深度强化学习算法的分类\n", "\n", "综上所述，强化学习的任务都可以描述为：给定一个强化学习环境，该环境在t时刻会有一个状态$s_t$，以及一系列的可供决策的有效动作的集合$A=\\{a_0, a_1, …,a_n\\}$，算法控制的智能体将会在这个动作集合中选择一个有效的动作$a_t \\in A$，并且在环境中执行这个动作$a_t$，获取t时刻的奖励$r_t$，同时环境的状态转移到$s_{t+1}$。在算法中，这个任务会一直执行直到到达指定的时长，或者环境到达终止状态为止。\n", "\n", "强化学习算法的目的是给定输入$(s_t, a_t, r_t, s_{t+1})$，通过改变控制智能体的算法，最大化在任务重获取的奖励。在深度强化学习算法中，通过训练一个深度模型来完成这个目的，即用深度学习模型根据$s_t$产生$a_t$。当前存在的强化学习算法有很多种，它们之间的关系也错综复杂。所以也有多种分类方法，这些分类之间并不是互相排斥的，有些模型会兼具两种不同的特征。\n", "\n", "### 5.3.1 基于模型的（Model-Based）和无模型的（Model-Free）\n", "\n", "在强化学习的过程中有时候需要对环境进行预测，这种预测往往在环境比较复杂，以及智能体和环境消耗的代价比较大的时候非常有用。典型的例子比如AlphaGo这个围棋算法，对于算法来说，执行到围棋棋局分出胜负为止往往需要耗费比较大的代价，这时候就可以使用一个模型对环境进行估计，比如估计局势究竟是哪一方占优等。\n", "\n", "通过建立环境的模型，智能体也可以有效地对自身的路径进行规划，以取得更高的奖励。需要注意的是，这里的模型指的是对环境建立一个模型来预测环境的变化，包括环境的奖励、环境自身状态的变化等。和前面介绍的使用模型来估计价值函数不同，那个估计是针对智能体自身的估计，而不是对环境的估计。\n", "\n", "如果没有对环境的建模过程，我们称之为无模型的算法。\n", "\n", "### 5.3.2 基于策略的（Policy-Based）和基于价值的（Value-Based）\n", "\n", "基于策略的深度强化学习算法中，通过使用神经网络对策略进行拟合，可以通过输入当前模型的状态，对智能体的下一步动作进行决策，通过使用神经网络输出的策略，并且使用策略梯度（Policy Gradient）对策略进行不断优化，可以让策略神经网络预测出最优的策略，最后让智能体通过采取这些策略来获取尽可能大的奖励，典型的算法如AC3算法。\n", "\n", "基于价值的深度强化学习算法中，主要拟合的是价值函数和动作-价值函数，通过估计不同的状态所处的价值，然后尽可能让智能体处于价值高的状态，这样就能获取最多的奖励，典型的算法如DQN算法。需要注意的是，有些网络可能综合了策略网络和机制网络的特点，典型的如SAC算法，会同时训练策略网络和价值网络，能够加快算法的收敛。\n", "\n", "基于策略和基于价值这两类算法并不是互斥的，还有一类算法将这两个算法结合起来，称为Actor-Critic算法。 Actor-Critic从名字上看包括两部分，演员 (Actor) 和评价者 (Critic) 。其中Actor代入了策略函数，负责生成动作 (Action) 并和环境交互。而Critic代入了价值函数，负责评估Actor的表现，并指导Actor下一阶段的动作。\n", "\n", "### 5.3.3 在线（On-policy）算法和离线（Off-policy）算法\n", "\n", "由于深度强化学习算法需要对智能体的状态、动作和获得的奖励进行采样，因此需要采集数据。根据采集数据训练的方法不同，我们把算法分为在线算法和离线算法两种。\n", "\n", "在线算法典型的就是基于策略梯度神经网络的算法，在训练的同时，策略因为训练会不断发生改变，需要对改变的策略进行重新采样，这样按照不断改变的策略进行采样，即为在线采样的一种。而很多价值神经网络的训练是基于离线的算法。通过固定的策略对环境进行采样后的结果，在离线算法中可以直接用来对模型进行训练，让模型能够学习到价值函数。"]}, {"cell_type": "markdown", "id": "51eba1f0-28a7-4607-a513-752786c61460", "metadata": {"tags": []}, "source": ["## 5.4 将强化学习算法应用到数字冰壶比赛中\n", "\n", "在前面的课程中我们已经了解到，冰壶运动考验的不仅是参赛选手投壶的准确性，还有在冰壶场地得分区排兵布阵的能力，是连续空间内的动态博弈。\n", "\n", "所谓连续，是指冰壶在得分区内不像棋盘上的棋子只有有限个位置（离散分布）可占，而是在连续空间内处处可占位。所谓动态，是指参赛选手的行动有先后顺序，而且行动在后者可以观察到行动在先者的选择，并据此做出相应的选择。这种博弈无论如何都无法看做同时决策，所以叫做动态博弈，也称“多阶段博弈”。\n", "\n", "动态博弈的困难在于，在前一刻最优的决策在下一刻可能不再为最优，因此在求解上发生很大的困难。而且冰壶比赛具有典型的马尔科夫性质，和其他各种棋类游戏一样，强化学习算法是最适合用于解决动态博弈问题的算法。\n", "\n", "### 5.4.1 数字冰壶的马尔科夫建模\n", "\n", "由于冰壶比赛具有典型的马尔科夫性质，因此可以使用马尔科夫决策过程在数字冰壶虚拟仿真平台中模拟冰壶对战中智能体可实现的策略与回报。 \n", "\n", "按照马尔科夫决策过程最基本的模型对冰壶比赛进行构建，主要包括以下要素：冰壶局面状态、投掷动作$a_i$、状态转移概率$p(s'|s,a)$和奖励$r_i$。在马尔科夫决策过程的模拟中，智能体根据当前冰壶局面状态$s_i$运用策略$π$来生成当前局面下的投掷动作$a_i$，由于冰壶比赛具有执行不确定性的特点，因此不论在实际冰壶比赛还是数字仿真环境里，在同一状态下执行同一动作后的下一状态也不尽相同，因此用状态转移概率$p(s'|s,a)$来表示在冰壶局面状态$s$下执行动作$a$转移到状态$s'$的概率，奖励$r_i$则表示执行此次投掷后所得到的奖励，如此循环往复直到比赛结束。具体如图所示。\n", "\n", "<center><img src=\"img/markov.png\" width=800></center>\n", "\n", "在冰壶比赛中，称从冰壶局面状态到投掷动作的映射为冰壶投掷策略$π$，由于冰壶比赛同围棋等比赛类似，仅在最终状态下才能获取奖励，因此此类延迟\n", "回报的问题不能只用奖励来评价策略的好坏，但可以用如式(5-3)所示的状态价值函数来代表冰壶投掷策略$π$的长期影响。在冰壶比赛中$V_π(S_t)$表示冰壶投掷策略$π$在冰壶局面状态$S_t$下的状态价值函数。因此，在冰壶比赛中，马尔科夫决策过程的最优策略就是在任意冰壶局面状态$s$下能够最大化状态价值函数$V_π(S)$的冰壶投掷策略$π^*$，如式(5-9)所示。\n", "\n", "$$π^*=argmaxV_π(S), \\forall s\\tag{5-9}$$"]}, {"cell_type": "markdown", "id": "fd3a3fac-439c-49b9-8a2e-e9c156224be8", "metadata": {"tags": []}, "source": ["### 5.4.1 数字冰壶比赛中强化学习算法的选取\n", "\n", "前面提到了强化学习算法可以分为基于价值的算法和基于策略的算法，每一类中都有很多经典的算法。\n", "\n", "#### 深度Q网络算法（Deep Q-Network, DQN）\n", "\n", "做为一种经典的基于价值的强化学习算法，异策略时序差分控制算法（Q-learning）将状态与动作构成一张Q表来储存状态动作价值，然后通过学习状态动作价值来构建最优策略，但正由于Q-learning算法是基于对由状态和动作构成的Q表进行更新来实现的，因此对于具有特别多的状态甚至连续状态空间的情况，要建立这样的Q表是不现实的，由此可见，Q-learning算法具有很强的局限性，只能运用于少部分简单的离散情况下。\n", "\n", "得益于深度学习的发展，DQN算法融合了价值函数近似与神经网络技术，用这种方法替换了Q-learning中的Q表，使得DQN算法能在连续状态空间中得以运用。值得一提的是DQN算法通过引入经验回放的方法来解决强化学习中的相关性和非静态分布问题，并引入了目标网络，让主网络产生当前Q值、目标网络产生目标Q值，以降低由非线性网络产生的不稳定情况，同时也能打乱相关性。\n", "\n", "#### 近端策略优化算法（Proximal Policy Optimization，PPO）\n", "\n", "原始策略梯度算法（Vanilla Policy Gradient，VPG）算法作为一种经典的基于策略的强化学习算法，由于该算法跳过了价值函数的学习直接进行策略的优化，通过增加带来高奖励的动作的概率来更新策略，因此不同于基于价值的强化学习算法，该方法除了可以应用在离散动作空间上还能应用于连续的动作空间上，应用范围更广，并且对不确定性和噪声更加鲁棒。\n", "\n", "由于VPG算法的方差很大，同时对超参数设置较为敏感，步长的大小将严重影响生成策略的质量，因此为了限制步长过大导致的更新前后策略的过大差异，TRPO算法使用trust region约束，限制梯度的变化量，从而使损失函数单调递增，PPO算法的提出则是因为TRPO算法需要使用对偶梯度法进行优化，这有着很大的难度，而PPO算法则使用一个正则项作为trust region约束，该正则项的系数是根据trust region约束是否被遵守来设定，从而避免使用对偶法。\n", "\n", "#### 蒙特卡洛树搜索算法(Monte Carlo Tree Search, MCTS)\n", "\n", "对于大多数的棋类游戏，我们可以把游戏过程抽象成在一个博弈树（Game Tree）上进行决策的过程。其中，游戏树的每个结点相当于棋盘的一个状态，而游戏树的每条边相当于某一个玩家（智能体）做出某一个决策。游戏玩家的对弈过程就相当于在这个博弈树上进行决策，决策目标是使自己得到的回报尽可能大，对方的回报尽可能小。\n", "\n", "为了实现这个目标，算法就需要从决策的当前结点出发，穷举（或者启发式的搜索）所有可能的动作，并且得到这些动作的奖励的估计，而且尽可能采取奖励比较大的动作。基于深度学习的蒙特卡洛树搜索方法被开发出来，主要目的是利用深度学习的模型拟合能力，学习到对弈局面的价值函数和对弈的策略，并且利用深度学习模型在博弈树上进行决策，从而能够达到最大限度利用计算资源、有效搜索当前局面的最优动作的目的。\n", "\n", "在后面的课程中会逐一讲解如何将这三种算法应用在数字冰壶比赛中。"]}, {"cell_type": "markdown", "id": "b6a1fcd2-c2b2-4278-ae9b-a38c7a299f12", "metadata": {}, "source": ["### 5.4.2 数字冰壶比赛中的状态描述与动作空间\n", "\n", "在数字冰壶比赛中，智能体是参赛的AI选手，环境是数字冰壶智能对战平台搭建的仿真环境，动作是AI选手投壶时给出的横向偏移、初始速度和初始角速度，状态就是每一个壶投出后双方冰壶在得分区的分布情况。如果想要应用强度学习算法，必须对每一时刻的状态给出相应的描述，并给出可用的动作空间。\n", "\n", "#### 状态描述\n", "\n", "本教程后续章节中，根据场上的冰壶与营垒圆心的距离由近至远进行排序，每个冰壶包含五个信息：x坐标、y坐标、离营垒圆心的距离、投掷顺序、是否为有效得分壶，提取了共80个特征作为数字冰壶比赛的状态描述。这种描述方式仅供参考，对于环境的描述是强化学习的重要环节，不同的环境描述会影响到算法训练模型的效率与性能。\n", "\n", "#### 动作空间\n", "\n", "在前面讲过数字冰壶比赛中三个动作参数的取值范围：初速度v0（0≤v0≤6）、横向偏移h0（-2.23≤h0≤2.23）和初始角速度ω0（-3.14≤ω0≤3.14）。\n", "\n", "实际上，在大本营中没有壶的情况下，能够投出得分壶的初始速度范围大概在2.8-3.2之间，而初速度在2.4-2.8之间的壶大概率是停留在防守区。如果在0-6的取值范围内等概率设定投壶初始速度，则大概率情况下投出的壶都无法停留在大本营，所以应该合理设定初速度v0的取值范围，在增大进营概率的基础上，也给保护壶和击打壶留出一席之地。\n", "\n", "类似地，当投壶初始速度为3（球停在7区）时，能够投出得分壶的横向偏移范围大概在(-2,2)之间，为了增大进营概率，也应该合理设定横向偏移h0的取值范围。"]}, {"cell_type": "markdown", "id": "dae162cf-12c7-4a11-b867-bcdca29f439f", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### 5.4.3 数字冰壶比赛中的奖励设置\n", "\n", "在强化学习算法中，每一个动作执行之后，都需要结合动作前后的状态描述给出该动作的奖励分数。由于数字冰壶比赛是一种博弈，从理论上讲不应该根据当前局面的得失对上一个动作给出评价，毕竟有时战术上的后退是为了战略上更好的进攻。但如果一直等到一局投完有了比分之后再给出（最后一个投壶动作的）奖励，对于该局中每个投壶动作的激励延时过长，很难训练出有效的模型。\n", "\n", "在本教程后续章节中，采用了一个比较简单的奖励设定机制：\n", "\n", "1. 如果投壶后相比投壶前，我方的分数有变化，则以投壶后分数减去投壶前分数的差值作为该投壶动作的奖励；\n", "2. 如果投壶后相比投壶前，我方的分数没有变化，则根据投壶落点到大本营圆心的距离给出该投壶动作的奖励，在圆心处奖励为1，未在大本营内奖励为0；\n", "3. 每一局最后一壶，将该局我方的得分乘以5，做为该投壶动作的奖励。\n", "\n", "可以看到，这个奖励设定机制仅考虑到了当前局面的得失，必然存在不合理性，仅适用于示范强化学习算法的使用。如果想要训练出可用的强化学习模型，需要设定出更加复杂有效的奖励机制。"]}, {"cell_type": "markdown", "id": "951e8568-df29-4ca6-b18e-d2124af96a10", "metadata": {}, "source": ["## 小结\n", "\n", "本课介绍了什么是强化学习算法的特点，并围绕智能体的概念、马尔可夫决策过程、动作价值函数和状态-动作价值函数讲解了强化学习算法的基本概念，进一步给出了包括有无模型、不同的拟合对象以及在线离线在内的多种强化学习算法分类方式。\n", "\n", "以数字冰壶比赛为应用实例，本课讲解了如何选取强化学习算法、如何给定状态描述并规范动作空间、如何设定奖励机制，为后续具体强化学习算法在数字冰壶比赛中的应用奠定了基础。"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.14"}}, "nbformat": 4, "nbformat_minor": 5}