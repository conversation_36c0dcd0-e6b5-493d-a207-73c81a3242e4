{"cells": [{"cell_type": "markdown", "id": "e851a8d5-1d2a-45a8-9486-a3c40d4a5dc7", "metadata": {"tags": []}, "source": ["# 第七课 冰壶比赛中PPO算法的实现\n", "\n", "## 7.1 PPO算法简介\n", "\n", "在第六课介绍了DQN及其相关的一系列算法，这些算法的特点是利用深度强化学习模型来拟合状态-动作价值函数，默认使用的策略一般是ε-贪心策略，通过逐渐更新状态-动作函数的估计来完成对应的策略迭代和价值迭代的过程。\n", "\n", "本课会介绍一个更加直接的思路来进行强化学习，也就是所谓的策略梯度（Policy Gradient）。这类算法会使用一个深度学习模型来根据当前智能体所处的环境状态直接生成对应的决策，策略梯度的存在让策略可以输出连续的动作，就解决了DQN算法只能适用离散空间的问题。同时策略网络因为可以直接输出对应的动作，在实际预测过程中就不再需要状态-动作价值函数了。"]}, {"cell_type": "markdown", "id": "d2ea7da6-1453-4470-b390-5d3a9d1d1061", "metadata": {"tags": []}, "source": ["### 7.1.1 原始策略梯度（Vanilla Policy Gradient，VPG）算法\n", "\n", "在强化学习的过程中，决策的每一步都能获得一定的奖励。如果有这样一个算法，每当奖励为正时，智能体就让对应概率分布的概率变大；反之，则令对应的概率变小。这样就需要搭建一个预测策略的概率分布$π(a|s)$的深度学习模型并设计一个损失函数，这个损失函数同时考虑了每一步获取的奖励和对应的策略概率分布，通过极小化损失函数，来训练一个能够把奖励最大化的策略网络。\n", "\n", "因为优化损失函数需要使用损失函数对于策略网络参数的梯度，因此这类算法被称为策略梯度算法。VPG算法就是使用一个策略网络来拟合具体的策略概率分布$π(a|s)$，使得在这个概率分布下，获取的奖励尽可能多。\n", "\n", "在策略梯度中，智能体能够控制的是每一步的动作，优化的也是策略网络的参数。假设策略梯度网络的参数为θ，则策略优化的目标函数如式(7-1)所示。\n", "\n", "$$J(θ)=\\Bbb E_{a\\simπ(a|s;θ)} [R(a)] \\tag{7-1}$$\n", "\n", "其中，$J(θ)$代表的是在$π(a|s;θ)$策略下，采样得到对应的动作，然后根据对应的动作获取奖励。为了能够对这个函数进行策略梯度优化，需要对这个函数求梯度。梯度求取结果如式(7-2)所示，因为该式涉及策略对数概率分布的梯度，所以对应的算法称为策略梯度算法。\n", "\n", "$$\\nabla_θ J(θ) = \\Bbb E_{a\\simπ(a|s;θ)}[\\nabla_θ \\log π(a|s;θ)R(a)] \\tag{7-2}$$\n", "\n", "在实际应用中，需要做的是构建具体的策略网络的深度学习模型，用深度学习模型做出决策，记录对应决策获得的奖励，然后根据式(7-3)计算对应的策略梯度的损失函数，反向传播得到对应的策略网络的梯度，用随机梯度优化的方法优化对应的策略网络即可。\n", "\n", "$$L = -\\frac{1}{BT}\\sum_{b=1}^B \\sum_{t=1}^T \\log π(a_t|s_t;θ)R_b(a_t) \\tag{7-3}$$"]}, {"cell_type": "markdown", "id": "5659551e-e643-47af-ae79-85090cf538a5", "metadata": {"tags": []}, "source": ["### 7.1.2 演员-评价者（Actor-Critic，AC）算法\n", "\n", "VPG算法主要的问题来源在于策略梯度算法对于基准（对回报函数期望的估计）的估计往往存在很大的偏置。为了解决这个问题，Actor-Critic算法引入了第二个深度学习模型，用这个深度学习模型来估计基准函数，以提高策略梯度算法的效率。\n", "\n", "在Actor-Critic算法的策略梯度网络中，需要同时考虑当前的状态$s_t$和当前智能体的动作$a_t$。这里的回报函数和当前状态与智能体的动作同时相关，而且还有了一个新添加的基准项，这个基准项和当前的动作$a_t$无关，仅和当前智能体所处的强化学习环境的状态$s_t$有关。\n", "\n", "也就是说，我们可以在策略梯度中引入一个和策略网络参数无关的基准函数$B(s_t)$，当回报函数$G(a,s_t)$减去这个基准函数的时候，整个策略网络的策略梯度不会发生任何改变。如式(7-4)所示。\n", "\n", "$$\\nabla_θ J(θ) = \\Bbb E_{a\\simπ(a|s;θ)}[\\nabla_θ \\log π(a|s;θ)(G(a,s_t)-B(s_t))] \\tag{7-4}$$\n", "\n", "其中，基准函数在这里应该设定为回报函数的期望，当回报函数大于基准函数的时候，对应的策略选择的动作就是比较好的动作，策略梯度应该增加这个动作的概率。反之，策略梯度会减少这个动作的概率。\n", "\n", "为了实现式(7-4)中策略梯度的计算，需要有两个网络，一个网络即策略网络，这个网络与VPG算法中使用的策略网络类似，对应的损失函数如式(7-5)所示；第二个网络即价值网络，这个网络使用了另外一个网络来拟合对应的价值函数。假设对应的基准函数表示为$B(s_i;\\phi)$，其中$\\phi$为基准函数的参数，则对应的损失函数如式(7-6)所示。\n", "\n", "$$L_{actor} = -\\frac{1}{BT}\\sum_{b=1}^B \\sum_{t=1}^T \\log π(a_t|s_t;θ)(G(a,s_t)-B(s_t)) \\tag{7-5}$$\n", "$$L_{critic} = -\\frac{1}{BT}\\sum_{b=1}^B \\sum_{t=1}^T (G(a,s_t)-B(s_t;\\phi)) \\tag{7-6}$$\n", "\n", "可以看到，第二个网络使用了MSE损失函数，这个函数的目标是为了让基准函数$B(s_t;\\phi)$的值等于回报函数r(a_t,s_t)的期望。第一个网络是策略网络，训练的函数 π(a_t|s_t;θ)负责“表演”，称为“演员”；第二个网络是价值网络，训练的函数$B(s_t;\\phi)$负责衡量策略函数给出的动作的好坏，称为“评论家”。这也是算法中“演员-评论家”名字的来源。\n", "\n", "在这两个策略梯度损失函数中，如何估计回报函数$G(a_t,s_t)$是非常重要的，因为回报函数的值的准确计算需要考虑到未来获取的折扣奖励，而未来折扣奖励的数据是很长甚至是无穷多的，无法准确计算，所以需要考虑使用深度学习模型来拟合未来的折扣奖励。\n", "\n", "这和在DQN中碰到的问题类似，只是DQN参与估计的是Q函数，而这里参数估计需要用到的是基准函数。DQN算法中可以采用单步估计或多部估计来估计Q函数，类似的，在优势演员-评论家（Advantage Actor-Critic, A2C）算法中，也使用了多步估计的方法来计算回报函数，从而计算对应的优势函数。\n", "\n", "假设使用策略梯度做了一个n步的决策，动作分别是$(a_1, a_2, …, a_n)$，对应获取的奖励分别为$(r_1, r_2, …, r_n)$，假设折扣因子为γ，那么可以得到对应的回报函数的估计，如式(7-7)所示。\n", "\n", "$$G(a_t,s_t) = \\sum_{i=t}^n γ^{i-t}r_i + γ^nB(s_{n+1})\\tag{7-7}$$\n", "\n", "结合式(7-5)和式(7-7)可知，优势函数的估计$G(a,s_t)-B(s_t)$和决策的采样长度n有关。当长度n比较长时，回报函数$G(a_t,s_t)$的估计比较准确，但是对应优势函数的波动也比较大，这种情况称为低偏置、高方差。如果减少决策采样长度n，对应的回报函数$G(a_t,s_t)$的估计就会不准，但梯度波动也比较小，这种情况称为高偏置、低方差。实际应用时需要根据具体情况斟酌选取。"]}, {"cell_type": "markdown", "id": "06a4ce69-7f47-4e84-b123-082a3eaf98be", "metadata": {"tags": []}, "source": ["### 7.1.3 近端策略优化（Proximal Policy Optimization，PPO）算法\n", "\n", "虽然AC算法的稳定性和收敛性比VPG算法有了很大提高，但相对来说策略梯度的波动，以及奖励在算法运行过程中的波动还是很大。另外，还有个问题就是策略地图对采样数据的利用效率不太高，需要考虑如何利用一定的采样数据尽量增加优化的步数，让对应的策略尽可能获得更多的奖励。\n", "\n", "为了减少策略梯度的波动，同时尽量提高优势函数比较大的动作的概率，我们可以考虑在初始策略附近的一个置信区间内对策略进行优化，也就是初始的策略和优化过程中的策略尽量不要偏离过大，让这个策略对应的动作能够尽量增加正的优势函数产生的概率，减少负的优势函数产生的概率。这就是基于置信区间的策略梯度算法，包括置信区间策略优化（Trust Region Policy Optimization, TRPO）算法和近端策略优化（Proximal Policy Optimization，PPO）算法的基本思想。\n", "\n", "PPO与TRPO旨在解决相同的问题：在策略梯度定理的步长的选取中，如何选取合适的步长，使得更新的参数尽可能对应最好的策略，但也不至于走得太远，以至于导致性能崩溃。TRPO和PPO的核心思想都是引入重要性采样，提高样本效率；同时，通过某种方式来约束新旧策略间的差异不要太大。\n", "\n", "#### 广义优势估计（Generalized Advantage Estimation，GAE）\n", "\n", "TRPO和PPO中都是采用GAE的方法对优势函数进行估计，具体而言，其计算公式为\n", "\n", "$$\\hat{A_t}^{GAE(γ,λ)}=\\sum_{l=0}^{\\infty}(γλ)^lδ_{t+l}^V=\\sum_{l=0}^{\\infty}(γλ)^l[r_{t+l}+γV(s_{t+l+1})-V(s_{t+l})]\\tag{7-8}$$\n", "\n", "其估计advantage的方法与$TD(λ)$类似，从公式上可以看出GAE中分别考虑了状态$s_t$后续各个时刻的优势值，然后按照距离当前状态的远近加权求和，从而起到了平滑作用。为了便于理解，考虑两种极限情况：\n", "\n", "1. $λ=0$时，$\\hat{A_t}=δ_t=r_t+γV(s_{t+1})-V(s_{t})$，优势值便是使用$TD(0)$估计的Q值与V值的差；\n", "2. $λ=1$时，$\\hat{A_t}=\\sum_{l=0}^{\\infty}(γ)^lδ_{t+l}=\\sum_{l=0}^{\\infty}γ^lr_{t+l}-V(s_{t+l})$，优势值则是使用蒙特卡洛方法估计的收益$G_t$值与V值的差。\n", "\n", "可以看出λ作为GAE算法的调整因子，它越接近1时，方差越大，偏差越小，接近0时反之。这也是GAE的一个优势，即可以更加不同环境的情况让我们可以调整参数来找到更合理的advantage。\n", "\n", "实际求取GAE的时候，不需要在整个流程中进行平均，只需选取关联性较大的N步即可。\n", " \n", "#### PPO惩罚\n", "\n", "TRPO在目标函数中，另外增加了一个约束条件。在推导该式的过程中， 涉及到了一个将KL散度作为惩罚项的极值问题，转化为KL散度作为约束条件的优化问题的过程，将KL散度作为惩罚项的问题。公式如下：\n", "\n", "$$\\max\\limits_θ  \\hat{\\Bbb E}_t \\big[\\frac{π_θ(a_t|s_t)}{π_{θ_{old}}(a_t|s_t)}\\hat A_t-βKL[π_{θ_{old}}(\\cdot|s_t),π_θ(\\cdot|s_t)]\\big] \\tag{7-9}$$\n", "\n", "PPO1算法用拉格朗日乘数法直接将KL散度的限制放入了目标函数，将有约束的优化问题转为无约束的优化问题，在迭代的过程中不断更新KL散度前的系数，使用几个阶段的小批量SGD，优化KL惩罚目标，其更新方式即为式(7-9)。\n", "\n", "为了对β进行动态调整，PPO1算法还提出了自适应KL散度（Adaptive KL Divergence）的思想。具体做法是，在每个epoch对KL惩罚目标进行优化后，计算$d=\\hat{\\Bbb E}_t \\big[KL[π_{θ_{old}}(\\cdot|s_t),π_θ(\\cdot|s_t)]\\big]$\n", "：\n", "\n", "- 如果$d<\\frac{δ}{1.5}$，则$β\\leftarrow \\frac{β}{2}$；\n", "- 如果$d>1.5δ$，则$β\\leftarrow 2β$；\n", "- 否则，$β$保持不变。\n", "\n", "在这里，更新的$β$用于下一次迭代时的参数更新。\n", "\n", "#### PPO截断\n", "\n", "PPO2在限制新的策略参数与旧的策略参数的距离上，相比于PPO1更加直接。区别于PPO1使用KL散度的方式进行限制，PPO2直接在目标函数上进行限制，如式(7-10)所示。\n", "\n", "$$L^{CLIP}(θ)=\\hat{\\Bbb E}_t[\\min (r_t(θ)\\hat{A}_t, clip(r_t(θ),1-ε,1+ε)\\hat{A}_t] \\tag{7-10}$$\n", "\n", "其中，$r_t(θ)]=\\frac{π_θ(a_t,s_t)}{π_{θ_old}(a_t,s-t)}$称为概率比，易得$r_t(θ_{old})=1$。$clip(r_t(θ),1-ε,1+ε)$指的是将$r_t(θ)$限制在$[1-ε,1+ε]$的范围内。ε为超参数，表示进行截断操作的范围，一般取ε=0.2。这样，就始终保证了新旧策略的比值在[0.8, 1.2]的范围内，保证了两个策略的差距不会太大。\n", "\n", "PPO2中，较为精妙的一点是在clip操作后乘了$\\hat{A}_t$（以下用A表示），而优势函数是有正负的。如下面两张图所示， $\\frac{π_θ(a_t,s_t)}{π_{θ_old}(a_t,s-t)}$ 是绿色的线；clip(r_t(θ),1-ε,1+ε) 是蓝色的线；在绿色的线与蓝色的线中间，我们要取一个最小的结果。\n", "\n", "如左图所示，假设前面乘上的项A>0，取最小的结果，就是红色的这条线。如右图所示，如果A<0，取最小结果的以后，就得到红色的这条线。\n", "\n", "<center><img src=\"img/PPO_clip.png\" width=600></center>\n", "\n", "虽然Clip操作已经很大程度上限制了新策略与旧策略之间的差距，但最终新旧策略依然有可能相差太远。不同的PPO算法采用了不同的技巧来避免这种情况。比如说，提前挺值得策略：如果新策略和旧策略的平均KL散度超过了某个阈值，则停止采取更新参数的步骤。"]}, {"cell_type": "markdown", "id": "a96ddabb-3605-49aa-b31c-a40c0f0c3aa0", "metadata": {"tags": []}, "source": ["### 7.1.4 PPO算法伪代码\n", "\n", "<img src=\"img/PPO_code.png\">\n", "\n", "其中，第3-5行实现了采样数据、计算优势函数（GAE的方式）和策略初始化。第6-9行采用公式（7-9）实现了Actor网络（策略网络）更新，参数M的含义为策略网络的参数个数。第10-13行实现了Critic网络（价值函数网络）更新，通过Critic网络的预测值为$V_\\phi(s_t)$，Label为\n", "$\\sum_{t'>t}γ^{t'-t}r_{t'}$。第14-17行实现了对权重进行调整。\n", "\n", "## 7.2 PPO算法在数字冰壶中的实现\n", "\n", "### 7.2.1 网络搭建\n", "\n", "#### >> 导入算法实现所需要的模块"]}, {"cell_type": "code", "execution_count": null, "id": "614e42e9-c3cd-4270-bf6f-7eec70a6f887", "metadata": {}, "outputs": [], "source": ["# -*- coding: utf-8 -*-\n", "import math\n", "import numpy as np\n", "import torch\n", "from collections import deque\n", "import torch.nn as nn\n", "from torch.autograd import Variable"]}, {"cell_type": "markdown", "id": "d3c7528b-53f5-41d9-87b3-9945d705b98d", "metadata": {}, "source": ["#### >> 网络输入设置\n", "\n", "根据场上的冰壶与营垒圆心的距离又近至远进行排序，每个冰壶包含五个信息：x坐标、y坐标、离营垒圆心的距离、投掷顺序、是否为有效得分壶，共80个特征作为网络输入。"]}, {"cell_type": "code", "execution_count": null, "id": "9f9d8420-a367-4b6d-b556-001071d1ae73", "metadata": {}, "outputs": [], "source": ["#获取某一冰壶距离营垒圆心的距离\n", "def get_dist(x, y):\n", "    House_x = 2.375\n", "    House_y = 4.88\n", "    return math.sqrt((x-House_x)**2+(y-House_y)**2)\n", "\n", "#根据冰壶比赛服务器发送来的场上冰壶位置坐标列表获取得分情况并生成信息状态数组\n", "def get_infostate(position):\n", "    House_R = 1.830\n", "    Stone_R = 0.145\n", "\n", "    init = np.empty([8], dtype=float)\n", "    gote = np.empty([8], dtype=float)\n", "    both = np.empty([16], dtype=float)\n", "    #计算双方冰壶到营垒圆心的距离\n", "    for i in range(8):\n", "        init[i] = get_dist(position[4 * i], position[4 * i + 1])\n", "        both[2*i] = init[i] \n", "        gote[i] = get_dist(position[4 * i + 2], position[4 * i + 3])\n", "        both[2*i+1] = gote[i]\n", "    #找到距离圆心较远一方距离圆心最近的壶\n", "    if min(init) <= min(gote):\n", "        win = 0                     #先手得分\n", "        d_std = min(gote)\n", "    else:\n", "        win = 1                     #后手得分\n", "        d_std = min(init)\n", "    \n", "    infostate = []  #状态数组\n", "    init_score = 0  #先手得分\n", "    #16个冰壶依次处理\n", "    for i in range(16):\n", "        x = position[2 * i]         #x坐标\n", "        y = position[2 * i + 1]     #y坐标\n", "        dist = both[i]              #到营垒圆心的距离\n", "        sn = i % 2 + 1              #投掷顺序\n", "        if (dist < d_std) and (dist < (House_R+Stone_R)) and ((i%2) == win):\n", "            valid = 1               #是有效得分壶\n", "            #如果是先手得分\n", "            if win == 0:\n", "                init_score = init_score + 1\n", "            #如果是后手得分\n", "            else:\n", "                init_score = init_score - 1\n", "        else:\n", "            valid = 0               #不是有效得分壶\n", "        #仅添加有效壶\n", "        if x!=0 or y!=0:\n", "            infostate.append([x, y, dist, sn, valid])\n", "    #按dist升序排列\n", "    infostate = sorted(infostate, key=lambda x:x[2])\n", "    \n", "    #无效壶补0\n", "    for i in range(16-len(infostate)):\n", "        infostate.append([0,0,0,0,0])\n", "\n", "    #返回先手得分和转为一维的状态数组\n", "    return init_score, np.array(infostate).flatten()"]}, {"cell_type": "markdown", "id": "a304c502-bc16-469c-b5eb-a576cf94e27b", "metadata": {}, "source": ["#### >> 网络输出设置\n", "\n", "如下所示范例代码继承torch.nn.Module类，搭建了两个相同结构的四层神经网络——Actor网络和Critic网络。网络每一层都是线性层（全连接层），实现将80维的输入张量映射为256维张量再经tanh函数激活，继而映射为64维张量再经tanh函数激活，继而映射为10维张量再经tanh函数激活，最终映射为3维的输出张量。\n", "\n", "其中Critic的网络输出就是神经网络输出层的数据，是当前状态的估计价值。Actor的网络输出则是以神经网络输出层的数据为均值，以std为标准差，经由torch.normal()函数实现正态分布近似后得到最终投掷动作。这里的标准差std越小越接近于均值，算法越倾向于利用，反之则越倾向于探索。范例代码中将std设置为固定值1，在实际训练中建议对其进行动态设置，来达到前期重视探索，后期重视利用的目的。\n", "\n", "在训练开始时，Actor网络各层的权重均为随机数值，因此网络的输出也是完全随机的，有很大概率根本就不在投掷动作参数的取值范围内，因此需要对网络的输出按照相应参数的取值范围截断后再作为动作参数使用。可以预见，在训练前期，大部分的动作参数都落在了截断区间的上下限，因此PPO算法和DQN算法相比，需要更多个训练周期后才能输出合理的动作参数。"]}, {"cell_type": "code", "execution_count": null, "id": "d9fd8d0b-45c4-453b-9e52-90eb37259685", "metadata": {}, "outputs": [], "source": ["#创建Actor网络类继承自nn.<PERSON><PERSON><PERSON>\n", "class Actor(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self):\n", "        super(Actor, self).__init__()\n", "        self.fc1 = nn.Linear(80, 128)       # 定义全连接层1\n", "        self.fc2 = nn.<PERSON>ar(128, 64)       # 定义全连接层2\n", "        self.fc3 = nn.<PERSON><PERSON>(64, 10)        # 定义全连接层3\n", "        self.out = nn.<PERSON><PERSON>(10, 3)         # 定义输出层\n", "        self.out.weight.data.mul_(0.1)      # 初始化输出层权重\n", "\n", "    def forward(self, x):\n", "        x = self.fc1(x)                     # 输入张量经全连接层1传递\n", "        x = torch.tanh(x)                   # 经tanh函数激活\n", "        x = self.fc2(x)                     # 经全连接层2传递\n", "        x = torch.tanh(x)                   # 经tanh函数激活\n", "        x = self.fc3(x)                     # 经全连接层3传递\n", "        x = torch.tanh(x)                   # 经tanh函数激活\n", "\n", "        mu = self.out(x)                    # 经输出层传递得到输出张量\n", "        logstd = torch.zeros_like(mu)       # 生成shape和mu相同的全0张量\n", "        std = torch.exp(logstd)             # 生成shape和mu相同的全1张量\n", "        return mu, std, logstd\n", "\n", "    def choose_action(self, state):\n", "        x = torch.FloatTensor(state)        #输入状态数组转为张量 shape-torch.<PERSON><PERSON>([80])\n", "        mu, std, _ = self.forward(x)        #网络前向推理 mu.shape-torch.Size([3]) std.shape-torch.Size([3])\n", "        action = torch.normal(mu, std).data.numpy()   #按照给定的均值和方差生成输出张量的近似数据\n", "\n", "        action[0] = np.clip(action[0], 2.4, 6)        #按照[2.4, 6]的区间截取action[0]（初速度）\n", "        action[1] = np.clip(action[1], -2, 2)         #按照[-2, 2]的区间截取action[1]（横向偏移）\n", "        action[2] = np.clip(action[2], -3.14, 3.14)   #按照[-3.14, 3.14]的区间截取action[2]（初始角速度）\n", "        return action\n", "\n", "#创建Critic网络类继承自nn.<PERSON><PERSON>le\n", "class Critic(nn.<PERSON><PERSON>):\n", "    def __init__(self):\n", "        super(Critic, self).__init__()\n", "        self.fc1 = nn.Linear(80, 128)       # 定义全连接层1\n", "        self.fc2 = nn.<PERSON>ar(128, 64)       # 定义全连接层2\n", "        self.fc3 = nn.<PERSON><PERSON>(64, 10)        # 定义全连接层3\n", "        self.out = nn.<PERSON>ar(10, 1)         # 定义输出层\n", "        self.out.weight.data.mul_(0.1)      # 初始化输出层权重\n", "\n", "    def forward(self, x):\n", "        x = self.fc1(x)                     # 输入张量经全连接层1传递\n", "        x = torch.tanh(x)                   # 经tanh函数激活\n", "        x = self.fc2(x)                     # 经全连接层2传递\n", "        x = torch.tanh(x)                   # 经tanh函数激活\n", "        x = self.fc3(x)                     # 经全连接层3传递\n", "        x = torch.tanh(x)                   # 经tanh函数激活\n", "        return self.out(x)                  # 经输出层传递得到输出张量"]}, {"cell_type": "markdown", "id": "7969dfa2-ca45-4ab9-8a81-2d2e2cbe6818", "metadata": {}, "source": ["### 7.2.2 PPO模型搭建\n", "\n", "#### >> 模型训练超参数设置"]}, {"cell_type": "code", "execution_count": null, "id": "4156533b-5444-4667-a568-3f7445c83de6", "metadata": {}, "outputs": [], "source": ["BATCH_SIZE = 32                             # 批次尺寸\n", "GAMMA = 0.9                                 # 奖励折扣因子\n", "LAMDA = 0.9                                 # GAE算法的调整因子\n", "EPSILON = 0.1                               # 截断调整因子\n", "\n", "#生成动态学习率\n", "def LearningRate(x):\n", "    lr_start = 0.0001                       # 起始学习率\n", "    lr_end = 0.0005                         # 终止学习率\n", "    lr_decay = 20000                        # 学习率衰减因子\n", "    return lr_end + (lr_start - lr_end) * math.exp(-1. * x / lr_decay)"]}, {"cell_type": "markdown", "id": "94bc94a6-0710-4662-a0c7-2e0a6d8cf31c", "metadata": {}, "source": ["#### >> 模型搭建"]}, {"cell_type": "code", "execution_count": null, "id": "00a194d1-cd3a-40bf-a864-9c162effb2d5", "metadata": {}, "outputs": [], "source": ["# 输出连续动作的概率分布\n", "def log_density(x, mu, std, logstd):\n", "    var = std.pow(2)\n", "    log_density = -(x - mu).pow(2) / (2 * var) - 0.5 * math.log(2 * math.pi) - logstd\n", "    return log_density.sum(1, keepdim=True)\n", "\n", "# 使用GAE方法计算优势函数\n", "def get_gae(rewards, masks, values):\n", "    rewards = torch.Tensor(rewards)\n", "    masks = torch.Tensor(masks)\n", "    returns = torch.zeros_like(rewards)\n", "    advants = torch.zeros_like(rewards)\n", "    running_returns = 0\n", "    previous_value = 0\n", "    running_advants = 0\n", "\n", "    for t in reversed(range(0, len(rewards))):\n", "        running_returns = rewards[t] + GAMMA * running_returns * masks[t]\n", "        running_tderror = rewards[t] + GAMMA * previous_value * masks[t] - values.data[t]\n", "        running_advants = running_tderror + GAMMA * LAMDA * running_advants * masks[t]\n", "\n", "        returns[t] = running_returns\n", "        previous_value = values.data[t]\n", "        advants[t] = running_advants\n", "    advants = (advants - advants.mean()) / advants.std()\n", "    return returns, advants\n", "\n", "# 替代损失函数\n", "def surrogate_loss(actor, advants, states, old_policy, actions, index):\n", "    mu, std, logstd = actor(torch.Tensor(states))\n", "    new_policy = log_density(actions, mu, std, logstd)\n", "    old_policy = old_policy[index]\n", "    ratio = torch.exp(new_policy - old_policy)\n", "    surrogate = ratio * advants\n", "    return surrogate, ratio\n", "\n", "# 训练模型\n", "def train_model(actor, critic, memory, actor_optim, critic_optim):\n", "    memory = np.array(memory, dtype=object)\n", "    states = np.vstack(memory[:, 0])\n", "    actions = list(memory[:, 1])\n", "    rewards = list(memory[:, 2])\n", "    masks = list(memory[:, 3])\n", "    values = critic(torch.Tensor(states))\n", "    loss_list = []\n", "    # step 1: get returns and GAEs and log probability of old policy\n", "    returns, advants = get_gae(rewards, masks, values)\n", "    mu, std, logstd = actor(torch.Tensor(states))\n", "    old_policy = log_density(torch.Tensor(np.array(actions)), mu, std, logstd)\n", "    old_values = critic(torch.Tensor(states))\n", "    criterion = torch.nn.MS<PERSON><PERSON>()\n", "    n = len(states)\n", "    arr = np.arange(n)\n", "    # step 2: get value loss and actor loss and update actor & critic\n", "    for epoch in range(10):\n", "        np.random.shuffle(arr)\n", "        for i in range(n // BATCH_SIZE):\n", "            batch_index = arr[BATCH_SIZE * i: BATCH_SIZE * (i + 1)]\n", "            batch_index = torch.LongTensor(batch_index)\n", "            inputs = torch.Tensor(states)[batch_index]\n", "            returns_samples = returns.unsqueeze(1)[batch_index]\n", "            advants_samples = advants.unsqueeze(1)[batch_index]\n", "            actions_samples = torch.Tensor(np.array(actions))[batch_index]\n", "            oldvalue_samples = old_values[batch_index].detach()\n", "            loss, ratio = surrogate_loss(actor, advants_samples, inputs,\n", "                                         old_policy.detach(), actions_samples,\n", "                                         batch_index)\n", "            values = critic(inputs)\n", "            clipped_values = oldvalue_samples + torch.clamp(values - oldvalue_samples, -EPSILON, EPSILON)\n", "            critic_loss1 = criterion(clipped_values, returns_samples)\n", "            critic_loss2 = criterion(values, returns_samples)\n", "            critic_loss = torch.max(critic_loss1, critic_loss2).mean()\n", "\n", "            clipped_ratio = torch.clamp(ratio, 1.0 - EPSILON, 1.0 + EPSILON)\n", "            clipped_loss = clipped_ratio * advants_samples\n", "            actor_loss = -torch.min(loss, clipped_loss).mean()\n", "\n", "            loss = actor_loss + critic_loss\n", "\n", "            loss_list.append(loss)\n", "            critic_optim.zero_grad()\n", "            critic_loss.backward(retain_graph=True)\n", "            critic_optim.step()\n", "\n", "            actor_optim.zero_grad()\n", "            actor_loss.backward()\n", "            actor_optim.step()\n", "\n", "    return 0, sum(loss_list)/10"]}, {"cell_type": "markdown", "id": "4c6756f4-d62d-485d-9921-79d1911a05e6", "metadata": {}, "source": ["### 7.2.3 PPO模型训练/部署"]}, {"cell_type": "markdown", "id": "120dcdb4-0477-484e-91c0-10c13e8246e0", "metadata": {}, "source": ["#### >> 启动数字冰壶比赛服务器\n", "\n", "首先点击页面左上角Jupyter菜单中的[Run]菜单项，点击该该菜单项的[Start Curling Server]子菜单项，即可启动一个数字冰壶比赛服务器。然后点击数字冰壶比赛服务器界面中的【无限对局】按钮进入该模式。\n", "\n", "下方给出的范例代码中创建了AIRobot类库的子类PPORobot，并重写了类的__init__()函数、recv_setstate()函数和get_bestshot()函数，并新增了get_reward()函数以获取奖励分数。\n", "\n", "#### >> 运行训练/部署PPO模型的AI选手\n", "\n", "根据数字冰壶服务器界面中给出的连接信息修改下方代码中的连接密钥，再运行这段代码，即可启动一个应用PPO算法进行投壶的AI选手。\n", "\n", "训练代码中根据传回的信息自动判断是先手还是后手，对于奖励的设置目前仅对最后一壶根据比赛得分进行设置，其余的奖励均为0，后续可根据规则、经验等对奖励进行自行设计。"]}, {"cell_type": "code", "execution_count": null, "id": "c85b2e5d-5d83-407c-a537-8748e67df57f", "metadata": {}, "outputs": [], "source": ["import time, os\n", "from AIRobot import AIRobot\n", "\n", "class PPORobot(AIRobot):\n", "    def __init__(self, key, name, host, port, round_max=10000):\n", "        super().__init__(key, name, host, port)\n", "\n", "        #初始化并加载先手actor模型\n", "        self.init_actor = Actor()\n", "        self.init_actor_file = 'model/PPO_init_actor.pth'\n", "        if os.path.exists(self.init_actor_file):\n", "            print(\"加载模型文件 %s\" % (self.init_actor_file))\n", "            self.init_actor.load_state_dict(torch.load(self.init_actor_file))\n", "\n", "        #初始化并加载先手critic模型\n", "        self.init_critic = Critic()\n", "        self.init_critic_file = 'model/PPO_init_critic.pth'\n", "        if os.path.exists(self.init_critic_file):\n", "            print(\"加载模型文件 %s\" % (self.init_critic_file))\n", "            self.init_critic.load_state_dict(torch.load(self.init_critic_file))\n", "\n", "        #初始化并加载后手actor模型\n", "        self.dote_actor = Actor()\n", "        self.dote_actor_file = 'model/PPO_dote_actor.pth'\n", "        if os.path.exists(self.dote_actor_file):\n", "            print(\"加载模型文件 %s\" % (self.dote_actor_file))\n", "            self.dote_actor.load_state_dict(torch.load(self.dote_actor_file))\n", "  \n", "        #初始化并加载后手critic模型\n", "        self.dote_critic = Critic()        \n", "        self.dote_critic_file = 'model/PPO_dote_critic.pth'\n", "        if os.path.exists(self.dote_critic_file):\n", "            print(\"加载模型文件 %s\" % (self.dote_critic_file))\n", "            self.dote_critic.load_state_dict(torch.load(self.dote_critic_file))\n", "          \n", "        self.memory = deque()               # 清空经验数据\n", "        self.round_max = round_max          # 最多训练局数\n", "        self.log_file_name = 'log/PPO_' + time.strftime(\"%y%m%d_%H%M%S\") + '.log' # 日志文件     \n", "\n", "    #根据当前比分获取奖励分数\n", "    def get_reward(self, this_score):\n", "        House_R = 1.830\n", "        Stone_R = 0.145\n", "        # 以投壶后得分减去投壶前得分为奖励分\n", "        reward = this_score - self.last_score\n", "        if (reward == 0):\n", "            x = self.position[2*self.shot_num]\n", "            y = self.position[2*self.shot_num+1]\n", "            dist = self.get_dist(x, y)\n", "            #对于大本营内的壶按照距离大本营圆心远近给奖励分\n", "            if dist < (House_R+Stone_R):\n", "                reward = 1 - dist / (House_R+Stone_R)\n", "        return reward\n", "        \n", "    #处理投掷状态消息\n", "    def recv_setstate(self, msg_list):\n", "        #当前完成投掷数\n", "        self.shot_num = int(msg_list[0])\n", "        #总对局数\n", "        self.round_total = int(msg_list[2])\n", "\n", "        #达到最大局数则退出训练\n", "        if self.round_num == self.round_max:\n", "            self.on_line = False\n", "            return\n", "        \n", "        #每一局开始时将历史比分清零\n", "        if (self.shot_num == 0):\n", "            self.last_score = 0\n", "            #根据先后手设定当前选手第一壶是当局比赛的第几壶并选取模型\n", "            if self.player_is_init:\n", "                self.first_shot = 0\n", "                self.actor = self.init_actor\n", "                self.critic = self.init_critic\n", "            else:\n", "                self.first_shot = 1\n", "                self.actor = self.dote_actor\n", "                self.critic = self.dote_critic\n", "        this_score = 0\n", "            \n", "        #当前选手第1壶投出前\n", "        if self.shot_num == self.first_shot:\n", "            init_score, self.s1 = get_infostate(self.position)      # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s1)         # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1-2*self.first_shot)*init_score           # 先手为正/后手为负\n", "        #当前选手第1壶投出后\n", "        if self.shot_num == self.first_shot+1:\n", "            init_score, _ = get_infostate(self.position)            # 获取当前得分和状态描述\n", "            this_score = (1-2*self.first_shot)*init_score                # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)                    # 获取动作奖励\n", "            self.memory.append([self.s1, self.action, reward, 1])   # 保存经验数据\n", "        #当前选手第2壶投出前\n", "        if self.shot_num == self.first_shot+2:\n", "            init_score, self.s2 = get_infostate(self.position)      # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s2)         # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1-2*self.first_shot)*init_score           # 先手为正/后手为负\n", "        #当前选手第2壶投出后\n", "        if self.shot_num == self.first_shot+3:\n", "            init_score, _ = get_infostate(self.position)            # 获取当前得分和状态描述\n", "            this_score = (1-2*self.first_shot)*init_score                # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)                    # 获取动作奖励\n", "            self.memory.append([self.s2, self.action, reward, 1])   # 保存经验数据\n", "        #当前选手第3壶投出前\n", "        if self.shot_num == self.first_shot+4:\n", "            init_score, self.s3 = get_infostate(self.position)      # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s3)         # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1-2*self.first_shot)*init_score           # 先手为正/后手为负\n", "        #当前选手第3壶投出后\n", "        if self.shot_num == self.first_shot+5:\n", "            init_score, _ = get_infostate(self.position)            # 获取当前得分和状态描述\n", "            this_score = (1-2*self.first_shot)*init_score                # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)                    # 获取动作奖励\n", "            self.memory.append([self.s3, self.action, reward, 1])   # 保存经验数据\n", "        #当前选手第4壶投出前\n", "        if self.shot_num == self.first_shot+6:\n", "            init_score, self.s4 = get_infostate(self.position)      # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s4)         # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1-2*self.first_shot)*init_score           # 先手为正/后手为负\n", "        #当前选手第4壶投出后\n", "        if self.shot_num == self.first_shot+7:\n", "            init_score, _ = get_infostate(self.position)            # 获取当前得分和状态描述\n", "            this_score = (1-2*self.first_shot)*init_score                # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)                    # 获取动作奖励\n", "            self.memory.append([self.s4, self.action, reward, 1])   # 保存经验数据\n", "        #当前选手第5壶投出前\n", "        if self.shot_num == self.first_shot+8:\n", "            init_score, self.s5 = get_infostate(self.position)      # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s5)         # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1-2*self.first_shot)*init_score           # 先手为正/后手为负\n", "        #当前选手第5壶投出后\n", "        if self.shot_num == self.first_shot+9:\n", "            init_score, _ = get_infostate(self.position)            # 获取当前得分和状态描述\n", "            this_score = (1-2*self.first_shot)*init_score                # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)                    # 获取动作奖励\n", "            self.memory.append([self.s5, self.action, reward, 1])   # 保存经验数据\n", "        #当前选手第6壶投出前\n", "        if self.shot_num == self.first_shot+10:\n", "            init_score, self.s6 = get_infostate(self.position)      # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s6)         # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1-2*self.first_shot)*init_score           # 先手为正/后手为负\n", "        #当前选手第6壶投出后\n", "        if self.shot_num == self.first_shot+11:\n", "            init_score, _ = get_infostate(self.position)            # 获取当前得分和状态描述\n", "            this_score = (1-2*self.first_shot)*init_score                # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)                    # 获取动作奖励\n", "            self.memory.append([self.s6, self.action, reward, 1])   # 保存经验数据\n", "        #当前选手第7壶投出前\n", "        if self.shot_num == self.first_shot+12:\n", "            init_score, self.s7 = get_infostate(self.position)      # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s7)         # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1-2*self.first_shot)*init_score           # 先手为正/后手为负\n", "        #当前选手第7壶投出后\n", "        if self.shot_num == self.first_shot+13:\n", "            init_score, _ = get_infostate(self.position)            # 获取当前得分和状态描述\n", "            this_score = (1-2*self.first_shot)*init_score                # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)                    # 获取动作奖励\n", "            self.memory.append([self.s7, self.action, reward, 1])   # 保存经验数据\n", "        #当前选手第8壶投出前\n", "        if self.shot_num == self.first_shot+14:\n", "            init_score, self.s8 = get_infostate(self.position)      # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s8)         # 根据状态获取对应的动作参数列表\n", "            \n", "        if self.shot_num == 16:\n", "            if self.score > 0:\n", "                reward = 10 * self.score                            # 获取动作奖励\n", "            else:\n", "                reward = 0      \n", "            self.memory.append([self.s8, self.action, reward, 0])   # 保存经验数据\n", "            \n", "            self.round_num += 1\n", "            #如果处于训练模式且有12局数据待训练\n", "            if (self.round_max > 0) and (self.round_num % 12 == 0):\n", "                #训练模型\n", "                actor_optim = torch.optim.Adam(self.actor.parameters(), lr=LearningRate(self.round_num))\n", "                critic_optim = torch.optim.Adam(self.critic.parameters(), lr=LearningRate(self.round_num),\n", "                                                weight_decay=0.0001)\n", "                self.actor.train(), self.critic.train()\n", "                _, loss = train_model(self.actor, self.critic, self.memory, actor_optim, critic_optim)\n", "                #保存模型\n", "                if self.player_is_init:\n", "                    torch.save(self.actor.state_dict(), self.init_actor_file)\n", "                    torch.save(self.critic.state_dict(), self.init_critic_file)\n", "                else:\n", "                    torch.save(self.actor.state_dict(), self.dote_actor_file)\n", "                    torch.save(self.critic.state_dict(), self.dote_critic_file)\n", "                print('============= Checkpoint Saved =============')\n", "                #清空训练数据\n", "                self.memory = deque()\n", "                \n", "            #将本局比分和当前loss值写入日志文件\n", "            log_file = open(self.log_file_name, 'a+')\n", "            log_file.write(\"score \"+str(self.score)+\" \"+str(self.round_num)+\"\\n\")\n", "            if self.round_num % 12 == 0:\n", "                log_file.write(\"loss \"+str(float(loss))+\" \"+str(self.round_num)+\"\\n\")\n", "            log_file.close()\n", "            \n", "    def get_bestshot(self):\n", "        return  \"BESTSHOT \" + str(self.action)[1:-1].replace(',', '')\n", "\n", "#连接密钥：参照数字冰壶服务器界面中给出的连接信息填写，注意这个参数每次新启动服务器都会改变。\n", "key = \"lidandan_e4c5e2b3-9940-4892-8691-bd0acf54cd56\"\n", "\n", "myrobot = PPORobot(key, name=\"PPORobot\", host=\"curling-server-7788.jupyterhub.svc.cluster.local\", port=7788)\n", "myrobot.recv_forever()"]}, {"cell_type": "markdown", "id": "3042770a-7cf3-4685-83f0-4db14f343bc4", "metadata": {}, "source": ["#### >> 运行基础AI选手\n", "\n", "训练强化学习算法需要一个对手，我们可以在控制台中运行AIRobot.py脚本启动CurlingAI选手，尝试训练一个PPO模型打败这个简单逻辑的基础AI，在数字冰壶比赛服务器界面可以看到＜Player2已连接＞。\n", "\n", "> 注意在运行脚本前要<b>根据数字冰壶服务器界面中提供的连接信息修改变量key的赋值</b>。\n", "\n", "#### >> 在无限对战中开始训练\n", "\n", "在数字冰壶服务器界面中点击【准备】按钮，再点击【开始对局】按钮，即可开始PPO模型的训练/部署。\n", "\n", "> 为提高训练效率，训练全程都要保证单机版数字冰壶服务器的WEB页面在前台运行（窗口模式且露出部分窗口在屏幕上即可）。\n", "\n", "在模型训练的过程中，随时可以通过在数字冰壶服务器界面中点击【返回主菜单】停止训练。"]}, {"cell_type": "markdown", "id": "e0fe52c2-1500-40dc-a895-f3e0d625d0ae", "metadata": {}, "source": ["### 7.2.4 训练过程曲线的绘制\n", "\n", "读取日志文件中的数据，绘制训练过程中的比分变化曲线和loss值变化曲线。"]}, {"cell_type": "code", "execution_count": null, "id": "4f5d24e7-c0b6-448e-87f9-93cc44a46bd9", "metadata": {"tags": []}, "outputs": [], "source": ["#导入matplotlib函数库\n", "import matplotlib.pyplot as plt\n", "\n", "#定义两个曲线的坐标数组\n", "score_x, score_y = [], []\n", "loss_x, loss_y = [], []\n", "\n", "#读取日志文件\n", "log_file = open(myrobot.log_file_name, 'r')\n", "for line in log_file.readlines():\n", "    var_name, var_value, round_num = line.split(' ')\n", "    #存储比分曲线数据\n", "    if var_name == 'score':\n", "        score_x.append(int(round_num))\n", "        score_y.append(int(var_value))\n", "    #存储loss曲线数据\n", "    if var_name == 'loss':\n", "        loss_x.append(int(round_num))\n", "        loss_y.append(float(var_value))\n", "\n", "#分两个子图以散点图的方式绘制比分曲线和loss值曲线\n", "fig, axes = plt.subplots(2,1)\n", "axes[0].scatter(np.array(score_x),np.array(score_y),s=5)\n", "axes[1].scatter(np.array(loss_x),np.array(loss_y),s=5)\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "id": "e99603cc-4b31-4961-b77e-84aa5cf0bd80", "metadata": {}, "source": ["## 小结\n", "\n", "本课介绍了从PPO算法及各种相关算法的原理，并给出了如何在数字冰壶比赛中应用PPO算法的范例代码。\n", "\n", "作为强化学习算法的一种重要思想，策略梯度类型算法让我们能够处理连续的动作空间，对于解决很多连续动作空间的控制问题非常有效。同时，作为一种算法思想，策略梯度的方法也渗透到了深度学习领域的很多思想中。在很多深度学习模型中，引入强化学习的框架，并且使用策略梯度的算法能够有效提高算法的效果。\n", "\n", "特别地，演员-评论家算法将价值网络和策略网络结合到一起，用这两个网络共同解决了一些强化学习领域的实际问题。\n", "\n", "<b>注意：由于强化学习模型需要长期持续训练才能有好的效果，而我们的Jupyter在线课程平台很难保证长期持续的网络连接，所以本教程仅用于讲解并演示强化学习模型的训练和部署，实际的模型训练建议下载单机版数字冰壶平台在本机进行。详见本教程的第八课《08.并行DQN以及并行PPO》。</b>"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.14"}}, "nbformat": 4, "nbformat_minor": 5}