{"cells": [{"cell_type": "markdown", "id": "85610c28-a1b7-48df-af81-f83cc8d97050", "metadata": {}, "source": ["# 第八课 并行训练算法\n", "\n", "经过前面七课的学习，我们已经可以基于强化学习训练自己的AI选手了。为了加快训练速度，可以采用并行训练的方法，本节课就来学习如何进行并行训练。并行训练需要同时运行多个数字冰壶服务器实例，受限于平台算力支持以及网络连接稳定性，在训练时需要使用单机版数字冰壶服务器。\n", "\n", "## 8.1 单机版数字冰壶服务器\n", "\n", "这门线上数字冰壶课程中的所有内容都需要结合数字冰壶服务器才能完成编程实践，方便同学们快速上手从零开始学习编写数字冰壶AI。大家在掌握了核心理论与算法之后，就可以<a href=\"https://aichallenge.embedded-ai.org/api/download-file?id=6688bd33a607189b339e956b\">>>下载单机版数字冰壶服务器<<</a>，在本地调试自己的数字冰壶AI程序了。\n", "\n", "下载好的单机版数字冰壶客户端是一个压缩包文件，解压缩后的目录中可以看到如下所示的内容：\n", "\n", "├─conf<br>\n", "│　└─config.ini：配置文件<br>\n", "├─CurlingAI<br>\n", "│　├─player1<br>\n", "│　│　└─CurlingAI.exe：测试用AI选手1<br>\n", "│　└─player2：<br>\n", "│　 　└─CurlingAI.exe：测试用AI选手2<br>\n", "├─resource<br>\n", "│  └─robot.py：数字冰壶AI选手python范例代码（投掷调试模式下的陪练AI）<br>\n", "├─web<br>\n", "│  └─index.html：单机版数字冰壶服务器WEB页面<br>\n", "├─curling.log：日志文件<br>\n", "├─curling_server.exe：单机版数字冰壶服务器主程序<br>\n", "└─CurlingUI：调用浏览器加载单机版数字冰壶服务器WEB页面的快捷方式<br>\n", "\n", "打开配置文件conf/config.ini，可以看到如下所示的内容："]}, {"cell_type": "raw", "id": "c686d174-0b1f-4602-9e57-1fd8ca843932", "metadata": {}, "source": ["[ROOT]\n", "HttpPort = 9007\n", "TcpPort = 7788\n", "LogLevel = \"INFO\"\n", "RobotPython = \"./resource/robot.py\""]}, {"cell_type": "markdown", "id": "1473b00c-7a5f-4400-9ef5-fc4d1358ae70", "metadata": {}, "source": ["编辑此配置文件可以修改单机版数字冰壶服务器的WEB端口（HttpPort）和TCP端口（TcpPort），同时运行多个数字冰壶服务器的时候需要修改这两个端口。\n", "\n", "运行单机版数字冰壶服务器主程序curling_server.exe，可以看到如图所示的界面。\n", "\n", "<center><img src=\"img/SingleServer1.png\" width=800 border=1></center>\n", "\n", "从界面中也可以看到当前运行的单机版数字冰壶服务器的WEB端口是9007、TCP端口是7788。\n", "\n", "运行快捷方式CurlingUI，或者手动调用浏览器访问地址localhost:9007（配置文件中的HttpPort），即可看到熟悉的数字冰壶服务器界面，如下图所示。\n", "\n", "<center><img src=\"img/SingleServer2.png\" width=800></center>\n", "\n", "从图中可以看到单机版数字冰壶服务器界面右下角没有连接信息，这是因为单机版数字冰壶服务器不验证接入AI的密钥，提供任意密钥都可以完成连接。\n", "\n", "在单机版数字冰壶服务器界面上点击【四局制】，分别运行测试用AI选手CurlingAI/player1/CurlingAI.exe和CurlingAI/player2/CurlingAI.exe，运行界面如下图所示。\n", "\n", "<center><img src=\"img/SingleServer3.png\" width=800></center>\n", "\n", "即会在单机版数字冰壶服务器界面上看到两个测试用AI选手已连接。依次点击【准备】和【开始对局】，看到单机版数字冰壶服务器界面中两个AI选手开始对战，即表明单机版数字冰壶服务器一切功能正常。\n", "\n", "<b>确保本地PC机上已经自行安装好python程序的运行环境</b>，将课程平台上的编写好的数字冰壶AI选手的python源码拷贝/下载到本地，<b>修改源码中的服务器主机（本地访问都是127.0.0.1）和端口（配置文件中的TcpPort）</b>，运行python程序即可在本地对数字冰壶AI选手进行调试。"]}, {"cell_type": "markdown", "id": "12d25b46-87f5-4f1a-af04-1b73063d5e4c", "metadata": {}, "source": ["## 8.2 并行DQN模型的训练与部署\n", "\n", "深度Q网络（DQN）是强化学习中的一个里程碑算法，它结合了Q学习与深度神经网络。并行DQN是对标准DQN的扩展，旨在通过并行化来提高学习效率和性能。\n", "\n", "### 8.2.1 并行DQN模型训练的专有组件\n", "\n", "并行DQN模型的训练过程主要包含以下组件：\n", "\n", "1. 全局DQN模型\n", "\n", "全局DQN模型是所有Agent共享的核心。它包含两个网络：一个用于初始化（init），另一个用于后续操作（dote）。这种设计允许系统适应不同阶段的任务特性。"]}, {"cell_type": "raw", "id": "607a40b0-997b-49d9-920b-744052a0e602", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["#范例代码的一部分，无法单独运行，因此请【不要】尝试运行本单元。\n", "\n", "class ParallelDQN:\n", "    def __init__(self, num_agents, key, ports):\n", "        self.global_dqn_init = DQN(N_STATES, N_ACTIONS)\n", "        self.global_dqn_dote = DQN(N_STATES, N_ACTIONS)\n", "# ..."]}, {"cell_type": "markdown", "id": "00809bc4-91d6-4eda-b258-188f9e4c738d", "metadata": {}, "source": ["2. 多个并行运行的Agent\n", "\n", "每个Agent是一个独立的实体，它们并行运行，与环境交互，并贡献到全局模型的学习过程。这种并行架构借鉴了A3C算法的思想。"]}, {"cell_type": "raw", "id": "5a9bec7f-348b-44f4-85a3-c1699a74fec3", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["#范例代码的一部分，无法单独运行，因此请【不要】尝试运行本单元。\n", "\n", "class ParallelDQNRobot(DQNRobot):\n", "    def __init__(self, agent_id, key, port, shared_memory, global_dqn_init, global_dqn_dote,  optimizer_init, optimizer_dote, init_model_file, dote_model_file):\n", "    # ..."]}, {"cell_type": "markdown", "id": "564b51a8-057d-439c-a314-6bb87f813e9c", "metadata": {}, "source": ["3. 共享内存（经验回放缓冲区）\n", "\n", "共享内存实现了经验回放机制，这是DQN算法的关键组成部分。在并行设置中，所有Agent共享这个内存，增加了经验的多样性。"]}, {"cell_type": "raw", "id": "78f1528d-95ad-41f6-8764-7ddb147d5c95", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["#范例代码的一部分，无法单独运行，因此请【不要】尝试运行本单元。\n", "\n", "self.shared_memory = self.ctx.Queue(maxsize=MEMORY_CAPACITY)"]}, {"cell_type": "markdown", "id": "bc92dc1b-e001-40f8-a38d-99bc73ef18d8", "metadata": {}, "source": ["4. 参数同步机制\n", "\n", "参数同步确保所有Agent的本地模型与全局模型保持一致。这是分布式学习系统的关键特性。"]}, {"cell_type": "raw", "id": "0189b737-da8d-4345-b85c-1337beadfb79", "metadata": {}, "source": ["#范例代码的一部分，无法单独运行，因此请【不要】尝试运行本单元。\n", "\n", "def sync_with_global(self):\n", "    self.dqn_init.eval_net.load_state_dict(self.global_dqn_init.eval_net.state_dict())\n", "    self.dqn_init.target_net.load_state_dict(self.global_dqn_init.target_net.state_dict())\n", "    # ..."]}, {"cell_type": "markdown", "id": "3f6936e0-9282-46d6-87d5-7701f035e105", "metadata": {}, "source": ["学习过程：Agents与环境交互，将经验存储在共享内存中。这个过程类似于标准DQN，但在并行环境中进行。包括从共享内存采样，计算损失，反向传播，更新全局模型。"]}, {"cell_type": "markdown", "id": "c09bf80e", "metadata": {}, "source": ["### 8.2.2 并行DQN模型训练的范例代码\n", "\n", "并行DQN模型训练的示例代码如下所示。使用时需要根据agent的设置数量（参考本地PC机的CPU内核数量）打开同等数量的数字冰壶单机版副本，需要注意每个数字冰壶单机版需要进入conf文件夹修改config.ini中的HttpPort以及TcpPort，每个线程连接不同的Port，从而实现多组agent同时对局。\n", "\n", "> 下方范例代码需要拷贝到本地PC上配合单机版数字冰壶服务器运行，请【不要】尝试运行下方代码单元。"]}, {"cell_type": "raw", "id": "309a2645-f966-49ba-9e17-262fb7a1ee9b", "metadata": {}, "source": ["# -*- coding: utf-8 -*-\n", "\n", "import os, time, math\n", "import numpy as np\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch.autograd import Variable\n", "import multiprocessing as mp\n", "import torch.multiprocessing as tmp\n", "from AIRobot import AIRobot\n", "\n", "BATCH_SIZE = 32  # 批次尺寸\n", "LR = 0.0001  # 学习率\n", "EPSILON = 0.7  # 最优选择动作百分比\n", "GAMMA = 0.9  # 奖励折扣因子\n", "TARGET_REPLACE_ITER = 500  # Q现实网络的更新频率\n", "MEMORY_CAPACITY = 10000  # 记忆库大小\n", "\n", "# 获取某一冰壶距离营垒圆心的距离\n", "def get_dist(x, y):\n", "    House_x = 2.375\n", "    House_y = 4.88\n", "    return math.sqrt((x - House_x) ** 2 + (y - House_y) ** 2)\n", "\n", "# 根据冰壶比赛服务器发送来的场上冰壶位置坐标列表获取得分情况并生成信息状态数组\n", "def get_infostate(position):\n", "    House_R = 1.830\n", "    Stone_R = 0.145\n", "\n", "    init = np.empty([8], dtype=float)\n", "    gote = np.empty([8], dtype=float)\n", "    both = np.empty([16], dtype=float)\n", "    # 计算双方冰壶到营垒圆心的距离\n", "    for i in range(8):\n", "        init[i] = get_dist(position[4 * i], position[4 * i + 1])\n", "        both[2 * i] = init[i]\n", "        gote[i] = get_dist(position[4 * i + 2], position[4 * i + 3])\n", "        both[2 * i + 1] = gote[i]\n", "    # 找到距离圆心较远一方距离圆心最近的壶\n", "    if min(init) <= min(gote):\n", "        win = 0  # 先手得分\n", "        d_std = min(gote)\n", "    else:\n", "        win = 1  # 后手得分\n", "        d_std = min(init)\n", "\n", "    infostate = []  # 状态数组\n", "    init_score = 0  # 先手得分\n", "    # 16个冰壶依次处理\n", "    for i in range(16):\n", "        x = position[2 * i]  # x坐标\n", "        y = position[2 * i + 1]  # y坐标\n", "        dist = both[i]  # 到营垒圆心的距离\n", "        sn = i % 2 + 1  # 投掷顺序\n", "        if (dist < d_std) and (dist < (House_R + Stone_R)) and ((i % 2) == win):\n", "            valid = 1  # 是有效得分壶\n", "            # 如果是先手得分\n", "            if win == 0:\n", "                init_score = init_score + 1\n", "            # 如果是后手得分\n", "            else:\n", "                init_score = init_score - 1\n", "        else:\n", "            valid = 0  # 不是有效得分壶\n", "        # 仅添加有效壶\n", "        if x != 0 or y != 0:\n", "            infostate.append([x, y, dist, sn, valid])\n", "    # 按dist升序排列\n", "    infostate = sorted(infostate, key=lambda x: x[2])\n", "\n", "    # 无效壶补0\n", "    for i in range(16 - len(infostate)):\n", "        infostate.append([0, 0, 0, 0, 0])\n", "\n", "    # 返回先手得分和转为一维的状态数组\n", "    return init_score, np.array(infostate).flatten()\n", "\n", "#低速：在(2.4,2.7)之间以0.1为步长进行离散\n", "slow = np.arange(2.4, 2.7, 0.1)\n", "#中速：在(2.8,3.2)之间以0.05为步长进行离散\n", "normal = np.arange(2.8, 3.2, 0.05)\n", "#高速\n", "fast = np.array([4,5,6])\n", "#将低速、中速、高速三个数组连接起来\n", "speed = np.concatenate((slow, normal, fast))\n", "\n", "#横向偏移在(-2,2)之间以0.4为步长进行离散\n", "deviation = np.arange(-2, 2, 0.4)\n", "#角速度在(-3.14, 3.14)之间以0.628为步长进行离散\n", "angspeed = np.arange(-3.14, 3.14, 0.628)\n", "\n", "n = 0\n", "#初始化动作列表\n", "action_list = np.empty([1600, 3], dtype=float)\n", "#遍历速度、横向偏移、角速度组合成各种动作\n", "for i in speed:\n", "    for j in deviation:\n", "        for k in angspeed:\n", "            action_list[n,] = [i, j, k]\n", "            n += 1\n", "\n", "class Net(nn.Module):\n", "    # 初始化网络\n", "    def __init__(self):\n", "        super(Net, self).__init__()\n", "        self.fc1 = nn.Linear(80, 256)  # 定义全连接层1\n", "        self.fc1.weight.data.normal_(0, 0.1)  # 按(0, 0.1)的正态分布初始化权重\n", "        self.fc2 = nn.Linear(256, 1024)  # 定义全连接层2\n", "        self.fc2.weight.data.normal_(0, 0.1)  # 按(0, 0.1)的正态分布初始化权重\n", "        self.out = nn.<PERSON>ar(1024, 1600)  # 定义输出层\n", "        self.out.weight.data.normal_(0, 0.1)  # 按(0, 0.1)的正态分布初始化权重\n", "\n", "    # 网络前向推理\n", "    def forward(self, x):\n", "        x = self.fc1(x)  # 输入张量经全连接层1传递\n", "        x = F.relu(x)  # 经relu函数激活\n", "        x = self.fc2(x)  # 经全连接层2传递\n", "        x = F.relu(x)  # 经relu函数激活\n", "        return self.out(x)  # 经输出层传递得到输出张量\n", "\n", "class DQN(object):\n", "    def __init__(self):\n", "        self.eval_net = Net()  # 初始化评价网络\n", "        self.target_net = Net()  # 初始化目标网络\n", "        self.sum_loss = 0  # 初始化loss值\n", "        self.learn_step_counter = 0  # 用于目标网络更新计时\n", "        self.memory_counter = 0  # 记忆库计数\n", "        self.memory = np.zeros((MEMORY_CAPACITY, 80 * 2 + 2))  # 初始化记忆库\n", "        self.optimizer = torch.optim.Adam(self.eval_net.parameters(), lr=LR)  # 设定torch的优化器为Adam\n", "        self.loss_func = nn.MSELoss()  # 以均方误差作为loss值\n", "        self.min_loss = 10000\n", "\n", "    # 根据输入状态x返回输出动作的索引（而不是动作）\n", "    def choose_action(self, x):\n", "        # 选最优动作\n", "        if np.random.uniform() < EPSILON:\n", "            x = Variable(torch.FloatTensor(x))  # 将x转为pytorch变量 shape-torch.<PERSON><PERSON>([80])\n", "            actions_eval = self.eval_net(x)  # 评价网络前向推理 shape-torch.Size([1600])\n", "            action = int(actions_eval.max(0)[1])  # 返回概率最大的动作索引\n", "        # 选随机动作\n", "        else:\n", "            action = np.random.randint(0, 1600)  # 在0-1600之间选一个随机整数\n", "        return action\n", "\n", "        # 存储经验数据（s是输入状态，a是输出动作，r是奖励，s_是下一刻的状态）\n", "\n", "    def store_transition(self, s, a, r, s_):\n", "        transition = np.hstack((s, a, r, s_))  # 将输入元组的元素数组按水平方向进行叠加\n", "        # 如果记忆库满了, 就覆盖老数据\n", "        index = self.memory_counter % MEMORY_CAPACITY\n", "        self.memory[index, :] = transition\n", "        self.memory_counter += 1\n", "\n", "    # 学习经验数据\n", "    def learn(self):\n", "        # 每隔TARGET_REPLACE_ITER次更新目标网络参数\n", "        if self.learn_step_counter % TARGET_REPLACE_ITER == 0:\n", "            self.target_net.load_state_dict(self.eval_net.state_dict())\n", "        self.learn_step_counter += 1\n", "\n", "        # 抽取记忆库中的批数据\n", "        size = min(self.memory_counter, MEMORY_CAPACITY)\n", "        sample_index = np.random.choice(size, BATCH_SIZE)\n", "        b_memory = self.memory[sample_index, :]  # 抽取出来的数据 shape-(32, 162)\n", "        b_s = Variable(torch.FloatTensor(b_memory[:, :80]))  # 输入数据的状态 shape-torch.Size([32, 80])\n", "        b_a = Variable(torch.LongTensor(b_memory[:, 80:81]))  # 输入数据的动作 shape-torch.Size([32, 1])\n", "        b_r = Variable(torch.FloatTensor(b_memory[:, 81:82]))  # 输入数据的奖励 shape-torch.Size([32, 1])\n", "        b_s_ = Variable(torch.FloatTensor(b_memory[:, -80:]))  # 输入数据的下一个状态 shape-torch.Size([32, 80])\n", "\n", "        # 针对做过的动作 b_a 来选 q_eval 的值\n", "        self.eval_net.train()  # 设定当前处于训练模式\n", "        actions_eval = self.eval_net(b_s)  # 评价网络前向推理 shape-torch.Size([32, 1600])\n", "        q_eval = actions_eval.gather(1, b_a)  # 选取第1维第b_a个数为评估Q值 shape-torch.Size([32, 1])\n", "\n", "        max_next_q_values = torch.zeros(32, dtype=torch.float).unsqueeze(dim=1)  # shape-torch.Size([32, 1])\n", "        for i in range(BATCH_SIZE):\n", "            action_target = self.target_net(b_s_[i]).detach()  # 目标网络前向推理 shape-torch.Size([1600])\n", "            max_next_q_values[i] = float(action_target.max(0)[0])  # 返回输出张量中的最大值\n", "        q_target = (b_r + GAMMA * max_next_q_values)  # 计算目标Q值 shape-torch.Size([32, 1])\n", "\n", "        # 计算loss值\n", "        loss = self.loss_func(q_eval, q_target)\n", "        loss_item = loss.item()\n", "        if loss_item < self.min_loss:\n", "            self.min_loss = loss_item\n", "\n", "        self.optimizer.zero_grad()  # 梯度清零\n", "        loss.backward()  # 将loss进行反向传播并计算网络参数的梯度\n", "        self.optimizer.step()  # 优化器进行更新\n", "        return loss_item\n", "\n", "class SharedAdam(torch.optim.Adam):\n", "    def __init__(self, params, lr=1e-3, betas=(0.9, 0.99), eps=1e-8, weight_decay=0):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(params, lr=lr, betas=betas, eps=eps, weight_decay=weight_decay)\n", "        for group in self.param_groups:\n", "            for p in group['params']:\n", "                state = self.state[p]\n", "                state['step'] = 0\n", "                state['exp_avg'] = torch.zeros_like(p.data)\n", "                state['exp_avg_sq'] = torch.zeros_like(p.data)\n", "                state['exp_avg'].share_memory_()\n", "                state['exp_avg_sq'].share_memory_()\n", "\n", "class ParallelDQN:\n", "    def __init__(self, ports):\n", "        self.ports = ports\n", "        self.ctx = mp.get_context('spawn')\n", "        self.shared_memory = mp.Queue(maxsize=2000)\n", "\n", "        self.global_dqn_init = DQN()\n", "        self.global_dqn_init.eval_net.share_memory()\n", "        self.global_dqn_init.target_net.share_memory()\n", "        self.optimizer_init = SharedAdam(self.global_dqn_init.eval_net.parameters(), lr=LR)\n", "\n", "        self.global_dqn_dote = DQN()\n", "        self.global_dqn_dote.eval_net.share_memory()\n", "        self.global_dqn_dote.target_net.share_memory()\n", "        self.optimizer_dote = SharedAdam(self.global_dqn_dote.eval_net.parameters(), lr=LR)\n", "\n", "        self.init_model_file = 'model/DQN_init_parallel.pth'\n", "        self.dote_model_file = 'model/DQN_dote_parallel.pth'\n", "\n", "        # 加载已有的模型（如果存在）\n", "        if os.path.exists(self.init_model_file):\n", "            self.global_dqn_init.eval_net.load_state_dict(torch.load(self.init_model_file))\n", "            self.global_dqn_init.target_net.load_state_dict(torch.load(self.init_model_file))\n", "        if os.path.exists(self.dote_model_file):\n", "            self.global_dqn_dote.eval_net.load_state_dict(torch.load(self.dote_model_file))\n", "            self.global_dqn_dote.target_net.load_state_dict(torch.load(self.dote_model_file))\n", "        # 日志文件\n", "        self.log_file_name = 'log/DQN_' + time.strftime(\"%y%m%d_%H%M%S\") + '.log'\n", "\n", "    def train(self):\n", "        processes = []\n", "        for i in range(len(self.ports)):\n", "            p = mp.Process(target=self.agent_process, args=(i, self.ports[i]))\n", "            p.start()\n", "            processes.append(p)\n", "        for p in processes:\n", "            p.join()\n", "\n", "    def agent_process(self, agent_id, port):\n", "        agent = ParallelDQNRobot(agent_id, port, self.shared_memory, self.log_file_name,\n", "                                 self.global_dqn_init, self.global_dqn_dote,\n", "                                 self.optimizer_init, self.optimizer_dote,\n", "                                 self.init_model_file, self.dote_model_file)\n", "        agent.run()\n", "\n", "class ParallelDQNRobot(AIRobot):\n", "    def __init__(self, agent_id, port, shared_memory, log_file_name, global_dqn_init, global_dqn_dote,\n", "                 optimizer_init, optimizer_dote, init_model_file, dote_model_file):\n", "        super().__init__(key=\"local\", name=f\"Agent_{agent_id}\", host=\"127.0.0.1\", port=port)\n", "\n", "        self.agent_id = agent_id\n", "        self.shared_memory = shared_memory\n", "        self.global_dqn_init = global_dqn_init\n", "        self.global_dqn_dote = global_dqn_dote\n", "        self.optimizer_init = optimizer_init\n", "        self.optimizer_dote = optimizer_dote\n", "        self.init_model_file = init_model_file\n", "        self.dote_model_file = dote_model_file\n", "        self.log_file_name = log_file_name\n", "        self.learn_start = 100  # 学习起始局数\n", "        self.round_max = 10000  # 最大训练局数\n", "\n", "        # 创建本地DQN\n", "        self.dqn_init = DQN()\n", "        self.dqn_dote = DQN()\n", "        self.sync_with_global()\n", "\n", "    # 根据当前比分获取奖励分数\n", "    def get_reward(self, this_score):\n", "        House_R = 1.830\n", "        Stone_R = 0.145\n", "        reward = this_score - self.last_score\n", "        if (reward == 0):\n", "            x = self.position[2 * self.shot_num]\n", "            y = self.position[2 * self.shot_num + 1]\n", "            dist = self.get_dist(x, y)\n", "            if dist < (House_R + Stone_R):\n", "                reward = 1 - dist / (House_R + Stone_R)\n", "        return reward\n", "\n", "    def store_transition(self, s, a, r, s_):\n", "        transition = np.hstack((s, [a, r], s_))\n", "        if not self.shared_memory.full():\n", "            self.shared_memory.put(transition)\n", "\n", "    def learn(self):\n", "        if self.shared_memory.qsize() < BATCH_SIZE:\n", "            return 0\n", "\n", "        batch = [self.shared_memory.get() for _ in range(BATCH_SIZE)]\n", "        b_memory = np.array(batch)\n", "        b_s = torch.FloatTensor(b_memory[:, :80])\n", "        b_a = torch.LongTensor(b_memory[:, 80:81].astype(int))\n", "        b_r = torch.FloatTensor(b_memory[:, 81:82])\n", "        b_s_ = torch.FloatTensor(b_memory[:, -80:])\n", "\n", "        if self.player_is_init:\n", "            dqn = self.dqn_init\n", "            global_dqn = self.global_dqn_init\n", "            optimizer = self.optimizer_init\n", "        else:\n", "            dqn = self.dqn_dote\n", "            global_dqn = self.global_dqn_dote\n", "            optimizer = self.optimizer_dote\n", "\n", "        # 使用本地网络计算损失\n", "        q_eval = dqn.eval_net(b_s).gather(1, b_a)\n", "        q_next = dqn.target_net(b_s_).detach()\n", "        q_target = b_r + GAMMA * q_next.max(1)[0].view(BATCH_SIZE, 1)\n", "        loss = dqn.loss_func(q_eval, q_target)\n", "\n", "        # 计算梯度\n", "        optimizer.zero_grad()\n", "        loss.backward()\n", "        # 将本地梯度应用到全局网络\n", "        for local_param, global_param in zip(dqn.eval_net.parameters(),\n", "                                             global_dqn.eval_net.parameters()):\n", "            if global_param.grad is not None:\n", "                global_param._grad = local_param.grad\n", "\n", "        # 更新全局网络\n", "        optimizer.step()\n", "        return loss.item()\n", "\n", "    def sync_with_global(self):\n", "        self.dqn_init.eval_net.load_state_dict(self.global_dqn_init.eval_net.state_dict())\n", "        self.dqn_init.target_net.load_state_dict(self.global_dqn_init.target_net.state_dict())\n", "        self.dqn_dote.eval_net.load_state_dict(self.global_dqn_dote.eval_net.state_dict())\n", "        self.dqn_dote.target_net.load_state_dict(self.global_dqn_dote.target_net.state_dict())\n", "\n", "    def recv_setstate(self, msg_list):\n", "        #当前完成投掷数\n", "        self.shot_num = int(msg_list[0])\n", "        #总对局数\n", "        self.round_total = int(msg_list[2])\n", "\n", "        #达到最大局数则退出训练\n", "        if self.round_num == self.round_max:\n", "            self.on_line = False\n", "            return\n", "\n", "        #每一局开始时将历史比分清零\n", "        if (self.shot_num == 0):\n", "            self.last_score = 0\n", "            #根据先后手选取模型并设定当前选手第一壶是当局比赛的第几壶\n", "            if self.player_is_init:\n", "                self.dqn = self.dqn_init\n", "                self.first_shot = 0\n", "            else:\n", "                self.dqn = self.dqn_dote\n", "                self.first_shot = 1\n", "        this_score = 0\n", "\n", "        #当前选手第1壶投出前\n", "        if self.shot_num == self.first_shot:\n", "            init_score, self.s1 = get_infostate(self.position)\n", "            self.A = self.dqn.choose_action(self.s1)\n", "            self.action = action_list[self.A]\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score\n", "        #当前选手第1壶投出后\n", "        elif self.shot_num == self.first_shot + 1:\n", "            init_score, s1_ = get_infostate(self.position)\n", "            this_score = (1 - 2 * self.first_shot) * init_score\n", "            reward = self.get_reward(this_score)\n", "            self.store_transition(self.s1, self.A, reward, s1_)\n", "            if self.dqn.memory_counter > self.learn_start:\n", "                loss = self.learn()\n", "        #当前选手第2壶投出前\n", "        elif self.shot_num == self.first_shot + 2:\n", "            init_score, self.s2 = get_infostate(self.position)\n", "            self.A = self.dqn.choose_action(self.s2)\n", "            self.action = action_list[self.A]\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score\n", "        #当前选手第2壶投出后\n", "        elif self.shot_num == self.first_shot + 3:\n", "            init_score, s2_ = get_infostate(self.position)\n", "            this_score = (1 - 2 * self.first_shot) * init_score\n", "            reward = self.get_reward(this_score)\n", "            self.store_transition(self.s2, self.A, reward, s2_)\n", "            if self.dqn.memory_counter > self.learn_start:\n", "                loss = self.learn()\n", "        #当前选手第3壶投出前\n", "        elif self.shot_num == self.first_shot + 4:\n", "            init_score, self.s3 = get_infostate(self.position)\n", "            self.A = self.dqn.choose_action(self.s3)\n", "            self.action = action_list[self.A]\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score\n", "        #当前选手第3壶投出后\n", "        elif self.shot_num == self.first_shot + 5:\n", "            init_score, s3_ = get_infostate(self.position)\n", "            this_score = (1 - 2 * self.first_shot) * init_score\n", "            reward = self.get_reward(this_score)\n", "            self.store_transition(self.s3, self.A, reward, s3_)\n", "            if self.dqn.memory_counter > self.learn_start:\n", "                loss = self.learn()\n", "        #当前选手第4壶投出前\n", "        elif self.shot_num == self.first_shot + 6:\n", "            init_score, self.s4 = get_infostate(self.position)\n", "            self.A = self.dqn.choose_action(self.s4)\n", "            self.action = action_list[self.A]\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score\n", "        #当前选手第4壶投出后\n", "        elif self.shot_num == self.first_shot + 7:\n", "            init_score, s4_ = get_infostate(self.position)\n", "            this_score = (1 - 2 * self.first_shot) * init_score\n", "            reward = self.get_reward(this_score)\n", "            self.store_transition(self.s4, self.A, reward, s4_)\n", "            if self.dqn.memory_counter > self.learn_start:\n", "                loss = self.learn()\n", "        #当前选手第5壶投出前\n", "        elif self.shot_num == self.first_shot + 8:\n", "            init_score, self.s5 = get_infostate(self.position)\n", "            self.A = self.dqn.choose_action(self.s5)\n", "            self.action = action_list[self.A]\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score\n", "        #当前选手第5壶投出后\n", "        elif self.shot_num == self.first_shot + 9:\n", "            init_score, s5_ = get_infostate(self.position)\n", "            this_score = (1 - 2 * self.first_shot) * init_score\n", "            reward = self.get_reward(this_score)\n", "            self.store_transition(self.s5, self.A, reward, s5_)\n", "            if self.dqn.memory_counter > self.learn_start:\n", "                loss = self.learn()\n", "        #当前选手第6壶投出前\n", "        elif self.shot_num == self.first_shot + 10:\n", "            init_score, self.s6 = get_infostate(self.position)\n", "            self.A = self.dqn.choose_action(self.s6)\n", "            self.action = action_list[self.A]\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score\n", "        #当前选手第6壶投出后\n", "        elif self.shot_num == self.first_shot + 11:\n", "            init_score, s6_ = get_infostate(self.position)\n", "            this_score = (1 - 2 * self.first_shot) * init_score\n", "            reward = self.get_reward(this_score)\n", "            self.store_transition(self.s6, self.A, reward, s6_)\n", "            if self.dqn.memory_counter > self.learn_start:\n", "                loss = self.learn()\n", "        #当前选手第7壶投出前\n", "        elif self.shot_num == self.first_shot + 12:\n", "            init_score, self.s7 = get_infostate(self.position)\n", "            self.A = self.dqn.choose_action(self.s7)\n", "            self.action = action_list[self.A]\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score\n", "        #当前选手第7壶投出后\n", "        elif self.shot_num == self.first_shot + 13:\n", "            init_score, s7_ = get_infostate(self.position)\n", "            this_score = (1 - 2 * self.first_shot) * init_score\n", "            reward = self.get_reward(this_score)\n", "            self.store_transition(self.s7, self.A, reward, s7_)\n", "            if self.dqn.memory_counter > self.learn_start:\n", "                loss = self.learn()\n", "        #当前选手第8壶投出前\n", "        elif self.shot_num == self.first_shot + 14:\n", "            _, self.s8 = get_infostate(self.position)\n", "            self.A = self.dqn.choose_action(self.s8)\n", "            self.action = action_list[self.A]\n", "        #当前选手第8壶投出后\n", "        elif self.shot_num == self.first_shot + 15:\n", "            _, self.s8_ = get_infostate(self.position)\n", "\n", "        if self.shot_num == 16:\n", "            if self.score > 0:\n", "                reward = 5 * self.score\n", "            else:\n", "                reward = 0\n", "            self.store_transition(self.s8, self.A, reward, self.s8_)\n", "            if self.dqn.memory_counter > self.learn_start:\n", "                loss = self.learn()\n", "\n", "            self.round_num += 1\n", "            log_file = open(self.log_file_name, 'a+')\n", "            log_file.write(f\"Agent {self.agent_id} - score {self.score} {self.round_num}\\n\")\n", "            if self.dqn.memory_counter > self.learn_start:\n", "                log_file.write(f\"Agent {self.agent_id} - loss {loss} {self.round_num}\\n\")\n", "            log_file.close()\n", "\n", "            if self.round_num % 50 == 0:\n", "                if self.player_is_init:\n", "                    net_params = self.global_dqn_init.eval_net.state_dict()\n", "                    torch.save(net_params, self.init_model_file)\n", "                else:\n", "                    net_params = self.global_dqn_dote.eval_net.state_dict()\n", "                    torch.save(net_params, self.dote_model_file)\n", "                print(f'Agent {self.agent_id}: Checkpoint Saved')\n", "        self.sync_with_global()\n", "\n", "    def get_bestshot(self):\n", "        return \"BESTSHOT \" + str(self.action)[1:-1].replace(',', '')\n", "\n", "    def run(self):\n", "        self.recv_forever()\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    # 定义端口列表\n", "    ports = [7788, 7789]  # 为每个agent指定一个端口\n", "\n", "    parallel_dqn = ParallelDQN(ports=ports)\n", "    parallel_dqn.train()"]}, {"cell_type": "markdown", "id": "f9296f55-9a72-4e02-9ea0-be4b56c6995d", "metadata": {}, "source": ["### 8.2.3 并行DQN模型训练的实操流程\n", "\n", "#### >> 启动多个数字冰壶比赛服务器\n", "\n", "上面给出的的范例代码中，同时启动了两个并发的训练进程，端口分别是默认的7788和新增的7789。为了支持这两个并发的训练进程，需要在本地PC上同时开启两个单机版数字冰壶服务器。\n", "\n", "将“数字冰壶单机版”目录整体在本地PC硬盘中复制粘贴，得到“数字冰壶单机版 - 副本”目录，编辑副本目录下配置文件conf/config.ini，更改HTTP端口（HttpPort）和TCP端口（TcpPort）的值，范例如下所示："]}, {"cell_type": "raw", "id": "f92a2d20-2782-4d8b-b34e-aba14da9942f", "metadata": {}, "source": ["[ROOT]\n", "HttpPort = 9009\n", "TcpPort = 7789\n", "LogLevel = \"INFO\"\n", "RobotPython = \"./resource/robot.py\""]}, {"cell_type": "markdown", "id": "591d2db5-3d47-4fb0-8325-ed69b93c0c5b", "metadata": {}, "source": ["运行副本目录下单机版数字冰壶服务器主程序curling_server.exe，手动调用浏览器访问地址localhost:9009加载副本目录下的单机版数字冰壶服务器主页。\n", "\n", "在两个单机版数字冰壶服务器的界面中进行操作，都进入【无限局制】模式。\n", "\n", "#### >> 运行多进程训练DQN模型的AI选手\n", "\n", "在当前课程页面左侧目录树区域中可以看到一个名为AIRobot.py的Python脚本，将该脚本下载到本地硬盘上。在AIRobot.py的保存目录下新建python文件（假设文件名是DQNRobot.py），将上方代码单元中的代码拷贝粘贴到DQNRobot.py中。\n", "\n", "准备工作：\n", "\n", "1. 确保本地PC机上已经自行安装好python程序的运行环境；\n", "2. 将在当前课程页面左侧目录树区域中的AIRobot.py脚本下载到本地硬盘上；\n", "3. 在AIRobot.py的保存目录下新建log子目录和model子目录，注意大小写；\n", "4. 在AIRobot.py的保存目录下新建DQNRobot.py脚本文件，将上方代码单元中的代码拷贝粘贴到新建文件中。\n", "\n", "运行DQNRobot.py，该脚本会先后启动两个并发的训练进程，分别连接到当前正在运行的两个单机版数字冰壶服务器。如下图所示。\n", "\n", "<center><img src=\"img/ParallelDQN1.png\" width=800></center>\n", "\n", "#### >> 运行基础AI选手\n", "\n", "运行“数字冰壶单机版”目录下的测试用AI选手CurlingAI/player1/CurlingAI.exe做为7788端口数字冰壶服务器上的基础AI陪练。\n", "\n", "> 也可运行python脚本AIRobt.py做为7788端口数字冰壶服务器上的基础AI陪练。\n", "\n", "测试用AI选手CurlingAI.exe默认都是连接本机的7788端口，我们需要带参数调用该程序以连接7789端口数字冰壶服务器。在资源管理器中进入“数字冰壶单机版 - 副本”目录下的CurlingAI/player1/目录中，右键点击CurlingAI.exe选择创建快捷方式，再右键点击新建的快捷方式编辑其属性，修改目标的值为`\"D:\\数字冰壶单机版 - 副本\\CurlingAI\\player1\\CurlingAI.exe\" -p 7789`。双击运行该快捷方式，将“数字冰壶单机版 - 副本”目录下的测试用AI选手CurlingAI/player1/CurlingAI.exe做为7789端口数字冰壶服务器上的基础AI陪练。\n", "\n", "> 也可带参数运行python脚本AIRobt.py做为7789端口数字冰壶服务器上的基础AI陪练。<br>`python AIRobot.py -p 7789`\n", "\n", "#### >> 在无限对战中开始训练\n", "\n", "分别在浏览器的两个单机版数字冰壶服务器界面上确认对战双方AI选手已连接，依次点击【准备】和【开始对局】，即可开始并行DQN模型的训练。\n", "\n", "> 1. 为提高训练效率，训练全程都要保证两个单机版数字冰壶服务器的WEB页面在前台运行（窗口模式且露出部分窗口在屏幕上即可）。<br>\n", "2. 因为两个进程都是50轮对局保存一次模型，最好一个进程晚开始几分钟，将保存时间错开。\n", "\n", "\n", "<center><img src=\"img/ParallelDQN2.png\" width=800></center>\n", "\n", "在模型训练的过程中，随时可以通过在数字冰壶服务器界面中点击【返回主菜单】停止训练。"]}, {"cell_type": "markdown", "id": "4b48df93-1a0a-4427-a07f-186890b59ac1", "metadata": {"tags": []}, "source": ["## 8.3 并行PPO模型的训练与部署\n", "\n", "运用相同的思路也可以得到并行PPO的代码：\n", "\n", "1. 使用SharedAdam优化器来支持参数的共享内存访问。\n", "2. 创建全局共享的Actor和Critic网络，以及相应的优化器。\n", "3. 每个Agent有自己的本地Actor和Critic网络，定期与全局网络同步。\n", "4. 使用共享内存队列(self.shared_memory)来实现经验回放缓冲区的共享。\n", "5. 在训练时，从共享内存中获取经验数据，更新本地模型，然后将梯度应用到全局模型。\n", "6. 定期将全局模型的参数同步回本地模型。\n", "\n", "这种实现方式允许多个Agent并行地与环境交互和学习，同时共享和更新一个全局模型。这可以加速学习过程，并可能提高模型的泛化能力。\n", "\n", "范例代码如下所示，具体实操流程和并行训练DQN模型的实操流程类似，不再赘述。\n", "\n", "> 下方范例代码需要拷贝到本地PC上配合单机版数字冰壶服务器运行，请【不要】尝试运行下方代码单元。"]}, {"cell_type": "raw", "id": "cb6c0329-f79b-4a0f-b75d-6559dd63cbf7", "metadata": {}, "source": ["# -*- coding: utf-8 -*-\n", "\n", "import os, time, math\n", "import numpy as np\n", "import torch\n", "import torch.nn as nn\n", "from torch.autograd import Variable\n", "import multiprocessing as mp\n", "import torch.multiprocessing as tmp\n", "from collections import deque\n", "from AIRobot import AIRobot\n", "\n", "# 常量定义\n", "BATCH_SIZE = 32\n", "GAMMA = 0.9\n", "LAMDA = 0.9\n", "EPSILON = 0.1\n", "N_STATES = 80\n", "N_ACTIONS = 3\n", "\n", "# 辅助函数\n", "def get_dist(x, y):\n", "    House_x = 2.375\n", "    House_y = 4.88\n", "    return math.sqrt((x - House_x) ** 2 + (y - House_y) ** 2)\n", "\n", "def get_infostate(position):\n", "    House_R = 1.830\n", "    Stone_R = 0.145\n", "\n", "    init = np.empty([8], dtype=float)\n", "    gote = np.empty([8], dtype=float)\n", "    both = np.empty([16], dtype=float)\n", "    for i in range(8):\n", "        init[i] = get_dist(position[4 * i], position[4 * i + 1])\n", "        both[2 * i] = init[i]\n", "        gote[i] = get_dist(position[4 * i + 2], position[4 * i + 3])\n", "        both[2 * i + 1] = gote[i]\n", "    if min(init) <= min(gote):\n", "        win = 0\n", "        d_std = min(gote)\n", "    else:\n", "        win = 1\n", "        d_std = min(init)\n", "\n", "    infostate = []\n", "    init_score = 0\n", "    for i in range(16):\n", "        x = position[2 * i]\n", "        y = position[2 * i + 1]\n", "        dist = both[i]\n", "        sn = i % 2 + 1\n", "        if (dist < d_std) and (dist < (House_R + Stone_R)) and ((i % 2) == win):\n", "            valid = 1\n", "            if win == 0:\n", "                init_score = init_score + 1\n", "            else:\n", "                init_score = init_score - 1\n", "        else:\n", "            valid = 0\n", "        if x != 0 or y != 0:\n", "            infostate.append([x, y, dist, sn, valid])\n", "    infostate = sorted(infostate, key=lambda x: x[2])\n", "\n", "    for i in range(16 - len(infostate)):\n", "        infostate.append([0, 0, 0, 0, 0])\n", "\n", "    return init_score, np.array(infostate).flatten()\n", "\n", "def LearningRate(x):\n", "    lr_start = 0.0001\n", "    lr_end = 0.0005\n", "    lr_decay = 20000\n", "    return lr_end + (lr_start - lr_end) * math.exp(-1. * x / lr_decay)\n", "\n", "def log_density(x, mu, std, logstd):\n", "    var = std.pow(2)\n", "    log_density = -(x - mu).pow(2) / (2 * var) - 0.5 * math.log(2 * math.pi) - logstd\n", "    return log_density.sum(1, keepdim=True)\n", "\n", "def get_gae(rewards, masks, values):\n", "    rewards = torch.Tensor(rewards)\n", "    masks = torch.Tensor(masks)\n", "    returns = torch.zeros_like(rewards)\n", "    advants = torch.zeros_like(rewards)\n", "    running_returns = 0\n", "    previous_value = 0\n", "    running_advants = 0\n", "\n", "    for t in reversed(range(0, len(rewards))):\n", "        running_returns = rewards[t] + GAMMA * running_returns * masks[t]\n", "        running_tderror = rewards[t] + GAMMA * previous_value * masks[t] - values.data[t]\n", "        running_advants = running_tderror + GAMMA * LAMDA * running_advants * masks[t]\n", "\n", "        returns[t] = running_returns\n", "        previous_value = values.data[t]\n", "        advants[t] = running_advants\n", "    advants = (advants - advants.mean()) / advants.std()\n", "    return returns, advants\n", "\n", "def surrogate_loss(actor, advants, states, old_policy, actions, index):\n", "    mu, std, logstd = actor(torch.Tensor(states))\n", "    new_policy = log_density(actions, mu, std, logstd)\n", "    old_policy = old_policy[index]\n", "    ratio = torch.exp(new_policy - old_policy)\n", "    surrogate = ratio * advants\n", "    return surrogate, ratio\n", "\n", "def train_model(actor, critic, memory, actor_optim, critic_optim):\n", "    memory = np.array(memory, dtype=object)\n", "    states = np.vstack(memory[:, 0])\n", "    actions = list(memory[:, 1])\n", "    rewards = list(memory[:, 2])\n", "    masks = list(memory[:, 3])\n", "    values = critic(torch.Tensor(states))\n", "\n", "    returns, advants = get_gae(rewards, masks, values)\n", "    mu, std, logstd = actor(torch.Tensor(states))\n", "    old_policy = log_density(torch.Tensor(np.array(actions)), mu, std, logstd)\n", "    old_values = critic(torch.Tensor(states))\n", "    criterion = torch.nn.MS<PERSON><PERSON>()\n", "    n = len(states)\n", "    arr = np.arange(n)\n", "\n", "    for _ in range(10):\n", "        np.random.shuffle(arr)\n", "        for i in range(n // BATCH_SIZE):\n", "            batch_index = arr[BATCH_SIZE * i: BATCH_SIZE * (i + 1)]\n", "            batch_index = torch.LongTensor(batch_index)\n", "            inputs = torch.Tensor(states)[batch_index]\n", "            returns_samples = returns.unsqueeze(1)[batch_index]\n", "            advants_samples = advants.unsqueeze(1)[batch_index]\n", "            actions_samples = torch.Tensor(np.array(actions))[batch_index]\n", "            oldvalue_samples = old_values[batch_index].detach()\n", "\n", "            loss, ratio = surrogate_loss(actor, advants_samples, inputs,\n", "                                         old_policy.detach(), actions_samples,\n", "                                         batch_index)\n", "            values = critic(inputs)\n", "            clipped_values = oldvalue_samples + torch.clamp(values - oldvalue_samples, -EPSILON, EPSILON)\n", "            critic_loss1 = criterion(clipped_values, returns_samples)\n", "            critic_loss2 = criterion(values, returns_samples)\n", "            critic_loss = torch.max(critic_loss1, critic_loss2).mean()\n", "\n", "            clipped_ratio = torch.clamp(ratio, 1.0 - EPSILON, 1.0 + EPSILON)\n", "            clipped_loss = clipped_ratio * advants_samples\n", "            actor_loss = -torch.min(loss, clipped_loss).mean()\n", "\n", "            loss = actor_loss + critic_loss\n", "\n", "            critic_optim.zero_grad()\n", "            critic_loss.backward(retain_graph=True)\n", "            critic_optim.step()\n", "\n", "            actor_optim.zero_grad()\n", "            actor_loss.backward()\n", "            actor_optim.step()\n", "\n", "    return 0, loss.item()\n", "\n", "class Actor(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self):\n", "        super(Actor, self).__init__()\n", "        self.fc1 = nn.Linear(N_STATES, 128)\n", "        self.fc2 = nn.<PERSON>ar(128, 64)\n", "        self.fc3 = nn.<PERSON><PERSON>(64, 10)\n", "        self.out = nn.Linear(10, N_ACTIONS)\n", "        self.out.weight.data.mul_(0.1)\n", "\n", "    def forward(self, x):\n", "        x = torch.tanh(self.fc1(x))\n", "        x = torch.tanh(self.fc2(x))\n", "        x = torch.tanh(self.fc3(x))\n", "        mu = self.out(x)\n", "        logstd = torch.zeros_like(mu)\n", "        std = torch.exp(logstd)\n", "        return mu, std, logstd\n", "\n", "    def choose_action(self, state):\n", "        x = torch.FloatTensor(state)\n", "        mu, std, _ = self.forward(x)\n", "        action = torch.normal(mu, std).data.numpy()\n", "        action[0] = np.clip(action[0], 2.4, 6)\n", "        action[1] = np.clip(action[1], -2, 2)\n", "        action[2] = np.clip(action[2], -3.14, 3.14)\n", "        return action\n", "\n", "class Critic(nn.<PERSON><PERSON>):\n", "    def __init__(self):\n", "        super(Critic, self).__init__()\n", "        self.fc1 = nn.Linear(N_STATES, 128)\n", "        self.fc2 = nn.<PERSON>ar(128, 64)\n", "        self.fc3 = nn.<PERSON><PERSON>(64, 10)\n", "        self.out = nn.<PERSON><PERSON>(10, 1)\n", "        self.out.weight.data.mul_(0.1)\n", "\n", "    def forward(self, x):\n", "        x = torch.tanh(self.fc1(x))\n", "        x = torch.tanh(self.fc2(x))\n", "        x = torch.tanh(self.fc3(x))\n", "        return self.out(x)\n", "\n", "class SharedAdam(torch.optim.Adam):\n", "    def __init__(self, params, lr=1e-3, betas=(0.9, 0.99), eps=1e-8, weight_decay=0):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(params, lr=lr, betas=betas, eps=eps, weight_decay=weight_decay)\n", "        for group in self.param_groups:\n", "            for p in group['params']:\n", "                state = self.state[p]\n", "                state['step'] = 0\n", "                state['exp_avg'] = torch.zeros_like(p.data)\n", "                state['exp_avg_sq'] = torch.zeros_like(p.data)\n", "                state['exp_avg'].share_memory_()\n", "                state['exp_avg_sq'].share_memory_()\n", "\n", "class ParallelPPO:\n", "    def __init__(self, ports):\n", "        self.ports = ports\n", "        self.ctx = mp.get_context('spawn')\n", "        self.shared_memory = self.ctx.Queue(maxsize=10000)\n", "\n", "        self.global_actor_init = Actor()\n", "        self.global_actor_init.share_memory()\n", "        self.global_critic_init = Critic()\n", "        self.global_critic_init.share_memory()\n", "\n", "        self.global_actor_dote = Actor()\n", "        self.global_actor_dote.share_memory()\n", "        self.global_critic_dote = Critic()\n", "        self.global_critic_dote.share_memory()\n", "\n", "        self.optimizer_actor_init = SharedAdam(self.global_actor_init.parameters(), lr=LearningRate(0))\n", "        self.optimizer_critic_init = SharedAdam(self.global_critic_init.parameters(), lr=LearningRate(0))\n", "        self.optimizer_actor_dote = SharedAdam(self.global_actor_dote.parameters(), lr=LearningRate(0))\n", "        self.optimizer_critic_dote = SharedAdam(self.global_critic_dote.parameters(), lr=LearningRate(0))\n", "\n", "        self.init_actor_file = 'model/PPO_init_actor_parallel.pth'\n", "        self.init_critic_file = 'model/PPO_init_critic_parallel.pth'\n", "        self.dote_actor_file = 'model/PPO_dote_actor_parallel.pth'\n", "        self.dote_critic_file = 'model/PPO_dote_critic_parallel.pth'\n", "\n", "        # 加载已有的模型（如果存在）\n", "        if os.path.exists(self.init_actor_file):\n", "            self.global_actor_init.load_state_dict(torch.load(self.init_actor_file))\n", "        if os.path.exists(self.init_critic_file):\n", "            self.global_critic_init.load_state_dict(torch.load(self.init_critic_file))\n", "        if os.path.exists(self.dote_actor_file):\n", "            self.global_actor_dote.load_state_dict(torch.load(self.dote_actor_file))\n", "        if os.path.exists(self.dote_critic_file):\n", "            self.global_critic_dote.load_state_dict(torch.load(self.dote_critic_file))\n", "        # 日志文件\n", "        self.log_file_name = 'log/PPO_' + time.strftime(\"%y%m%d_%H%M%S\") + '.log'\n", "\n", "    def train(self):\n", "        processes = []\n", "        for i in range(len(self.ports)):\n", "            p = self.ctx.Process(target=self.agent_process, args=(i, self.ports[i]))\n", "            p.start()\n", "            processes.append(p)\n", "        for p in processes:\n", "            p.join()\n", "\n", "    def agent_process(self, agent_id, port):\n", "        agent = ParallelPPORobot(agent_id, port, self.shared_memory, self.log_file_name,\n", "                                 self.global_actor_init, self.global_critic_init,\n", "                                 self.global_actor_dote, self.global_critic_dote,\n", "                                 self.optimizer_actor_init, self.optimizer_critic_init,\n", "                                 self.optimizer_actor_dote, self.optimizer_critic_dote,\n", "                                 self.init_actor_file, self.init_critic_file,\n", "                                 self.dote_actor_file, self.dote_critic_file)\n", "        agent.run()\n", "\n", "class ParallelPPORobot(AIRobot):\n", "    def __init__(self, agent_id, port, shared_memory, log_file_name,\n", "                 global_actor_init, global_critic_init,\n", "                 global_actor_dote, global_critic_dote,\n", "                 optimizer_actor_init, optimizer_critic_init,\n", "                 optimizer_actor_dote, optimizer_critic_dote,\n", "                 init_actor_file, init_critic_file,\n", "                 dote_actor_file, dote_critic_file):\n", "        super().__init__(key=\"local\", name=f\"Agent_{agent_id}\", host=\"127.0.0.1\", port=port)\n", "\n", "        self.agent_id = agent_id\n", "        self.shared_memory = shared_memory\n", "        self.global_actor_init = global_actor_init\n", "        self.global_critic_init = global_critic_init\n", "        self.global_actor_dote = global_actor_dote\n", "        self.global_critic_dote = global_critic_dote\n", "        self.optimizer_actor_init = optimizer_actor_init\n", "        self.optimizer_critic_init = optimizer_critic_init\n", "        self.optimizer_actor_dote = optimizer_actor_dote\n", "        self.optimizer_critic_dote = optimizer_critic_dote\n", "        self.init_actor_file = init_actor_file\n", "        self.init_critic_file = init_critic_file\n", "        self.dote_actor_file = dote_actor_file\n", "        self.dote_critic_file = dote_critic_file\n", "        self.log_file_name = log_file_name\n", "\n", "        self.init_actor = Actor()\n", "        self.init_critic = Critic()\n", "        self.dote_actor = Actor()\n", "        self.dote_critic = Critic()\n", "        self.sync_with_global()\n", "\n", "        self.memory = deque()\n", "        self.round_max = 10000\n", "        self.round_num = 0\n", "\n", "    def sync_with_global(self):\n", "        self.init_actor.load_state_dict(self.global_actor_init.state_dict())\n", "        self.init_critic.load_state_dict(self.global_critic_init.state_dict())\n", "        self.dote_actor.load_state_dict(self.global_actor_dote.state_dict())\n", "        self.dote_critic.load_state_dict(self.global_critic_dote.state_dict())\n", "\n", "    def get_reward(self, this_score):\n", "        House_R = 1.830\n", "        Stone_R = 0.145\n", "        reward = this_score - self.last_score\n", "        if (reward == 0):\n", "            x = self.position[2 * self.shot_num]\n", "            y = self.position[2 * self.shot_num + 1]\n", "            dist = get_dist(x, y)\n", "            if dist < (House_R + Stone_R):\n", "                reward = 1 - dist / (House_R + Stone_R)\n", "        return reward\n", "\n", "    def recv_setstate(self, msg_list):\n", "        # 当前完成投掷数\n", "        self.shot_num = int(msg_list[0])\n", "        # 总对局数\n", "        self.round_total = int(msg_list[2])\n", "\n", "        # 达到最大局数则退出训练\n", "        if self.round_num == self.round_max:\n", "            self.on_line = False\n", "            return\n", "\n", "        # 每一局开始时将历史比分清零\n", "        if (self.shot_num == 0):\n", "            self.last_score = 0\n", "            # 根据先后手设定当前选手第一壶是当局比赛的第几壶并选取模型\n", "            if self.player_is_init:\n", "                self.first_shot = 0\n", "                self.actor = self.init_actor\n", "                self.critic = self.init_critic\n", "            else:\n", "                self.first_shot = 1\n", "                self.actor = self.dote_actor\n", "                self.critic = self.dote_critic\n", "        this_score = 0\n", "\n", "        # 当前选手第1壶投出前\n", "        if self.shot_num == self.first_shot:\n", "            init_score, self.s1 = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s1)  # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "        # 当前选手第1壶投出后\n", "        if self.shot_num == self.first_shot + 1:\n", "            init_score, _ = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            this_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)  # 获取动作奖励\n", "            self.memory.append([self.s1, self.action, reward, 1])  # 保存经验数据\n", "        # 当前选手第2壶投出前\n", "        if self.shot_num == self.first_shot + 2:\n", "            init_score, self.s2 = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s2)  # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "        # 当前选手第2壶投出后\n", "        if self.shot_num == self.first_shot + 3:\n", "            init_score, _ = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            this_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)  # 获取动作奖励\n", "            self.memory.append([self.s2, self.action, reward, 1])  # 保存经验数据\n", "        # 当前选手第3壶投出前\n", "        if self.shot_num == self.first_shot + 4:\n", "            init_score, self.s3 = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s3)  # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "        # 当前选手第3壶投出后\n", "        if self.shot_num == self.first_shot + 5:\n", "            init_score, _ = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            this_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)  # 获取动作奖励\n", "            self.memory.append([self.s3, self.action, reward, 1])  # 保存经验数据\n", "        # 当前选手第4壶投出前\n", "        if self.shot_num == self.first_shot + 6:\n", "            init_score, self.s4 = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s4)  # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "        # 当前选手第4壶投出后\n", "        if self.shot_num == self.first_shot + 7:\n", "            init_score, _ = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            this_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)  # 获取动作奖励\n", "            self.memory.append([self.s4, self.action, reward, 1])  # 保存经验数据\n", "        # 当前选手第5壶投出前\n", "        if self.shot_num == self.first_shot + 8:\n", "            init_score, self.s5 = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s5)  # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "        # 当前选手第5壶投出后\n", "        if self.shot_num == self.first_shot + 9:\n", "            init_score, _ = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            this_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)  # 获取动作奖励\n", "            self.memory.append([self.s5, self.action, reward, 1])  # 保存经验数据\n", "        # 当前选手第6壶投出前\n", "        if self.shot_num == self.first_shot + 10:\n", "            init_score, self.s6 = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s6)  # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "        # 当前选手第6壶投出后\n", "        if self.shot_num == self.first_shot + 11:\n", "            init_score, _ = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            this_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)  # 获取动作奖励\n", "            self.memory.append([self.s6, self.action, reward, 1])  # 保存经验数据\n", "        # 当前选手第7壶投出前\n", "        if self.shot_num == self.first_shot + 12:\n", "            init_score, self.s7 = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s7)  # 根据状态获取对应的动作参数列表\n", "            self.last_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "        # 当前选手第7壶投出后\n", "        if self.shot_num == self.first_shot + 13:\n", "            init_score, _ = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            this_score = (1 - 2 * self.first_shot) * init_score  # 先手为正/后手为负\n", "            reward = self.get_reward(this_score)  # 获取动作奖励\n", "            self.memory.append([self.s7, self.action, reward, 1])  # 保存经验数据\n", "        # 当前选手第8壶投出前\n", "        if self.shot_num == self.first_shot + 14:\n", "            init_score, self.s8 = get_infostate(self.position)  # 获取当前得分和状态描述\n", "            self.action = self.actor.choose_action(self.s8)  # 根据状态获取对应的动作参数列表\n", "\n", "        if self.shot_num == 16:\n", "            if self.score > 0:\n", "                reward = 10 * self.score  # 获取动作奖励\n", "            else:\n", "                reward = 0\n", "            self.memory.append([self.s8, self.action, reward, 0])  # 保存经验数据\n", "\n", "            self.round_num += 1\n", "            if (self.round_max > 0) and (self.round_num % 12 == 0):\n", "                # 将本地经验数据添加到共享内存\n", "                while len(self.memory) > 0:\n", "                    self.shared_memory.put(self.memory.popleft())\n", "\n", "                # 训练模型\n", "                if self.player_is_init:\n", "                    actor, critic = self.init_actor, self.init_critic\n", "                    global_actor, global_critic = self.global_actor_init, self.global_critic_init\n", "                    optimizer_actor, optimizer_critic = self.optimizer_actor_init, self.optimizer_critic_init\n", "                    actor_file, critic_file = self.init_actor_file, self.init_critic_file\n", "                else:\n", "                    actor, critic = self.dote_actor, self.dote_critic\n", "                    global_actor, global_critic = self.global_actor_dote, self.global_critic_dote\n", "                    optimizer_actor, optimizer_critic = self.optimizer_actor_dote, self.optimizer_critic_dote\n", "                    actor_file, critic_file = self.dote_actor_file, self.dote_critic_file\n", "\n", "                actor.train(), critic.train()\n", "                memory = [self.shared_memory.get() for _ in range(min(self.shared_memory.qsize(), 1000))]\n", "                _, loss = train_model(actor, critic, memory, optimizer_actor, optimizer_critic)\n", "\n", "                # 同步本地模型到全局模型\n", "                for param, global_param in zip(actor.parameters(), global_actor.parameters()):\n", "                    global_param._grad = param.grad\n", "                optimizer_actor.step()\n", "                for param, global_param in zip(critic.parameters(), global_critic.parameters()):\n", "                    global_param._grad = param.grad\n", "                optimizer_critic.step()\n", "\n", "                # 保存模型\n", "                torch.save(global_actor.state_dict(), actor_file)\n", "                torch.save(global_critic.state_dict(), critic_file)\n", "                print(f'============= Checkpoint Saved (Agent {self.agent_id}) =============')\n", "\n", "                # 从全局模型同步回本地模型\n", "                self.sync_with_global()\n", "\n", "            log_file = open(self.log_file_name, 'a+')\n", "            log_file.write(f\"score {self.score} {self.round_num}\\n\")\n", "            if self.round_num % 12 == 0:\n", "                log_file.write(f\"loss {float(loss)} {self.round_num}\\n\")\n", "            log_file.close()\n", "\n", "    def get_bestshot(self):\n", "        return \"BESTSHOT \" + str(self.action)[1:-1].replace(',', '')\n", "\n", "    def run(self):\n", "        self.recv_forever()\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    ports = [7788, 7789]  # 为每个agent指定一个端口\n", "\n", "    parallel_ppo = ParallelPPO(ports=ports)\n", "    parallel_ppo.train()"]}, {"cell_type": "markdown", "id": "436a62a7-d56c-439e-8b4c-07db8e115a43", "metadata": {}, "source": ["## 8.4 并行模型训练方法的范例代码优化\n", "\n", "前面两个并行模型训练方法的示例代码中都存在参数覆盖问题，下面提供几种解决方法，同学可以自行修改代码进行实验：\n", "\n", "1. 使用锁机制：这是最直接的方法，但可能会降低并行效率。"]}, {"cell_type": "raw", "id": "e142bbe9-9290-4091-a613-3b9f6ddc74b0", "metadata": {}, "source": ["#范例代码的一部分，无法单独运行，因此请【不要】尝试运行本单元。\n", "\n", "import threading\n", "\n", "class ParallelDQN:\n", "    def __init__(self):\n", "        ...\n", "        self.update_lock = threading.Lock()\n", "\n", "class ParallelDQNRobot(DQNRobot):\n", "    def learn(self):\n", "        ...\n", "        with self.update_lock:\n", "            for local_param, global_param in zip(local_dqn.eval_net.parameters(),\n", "                                                 global_dqn.eval_net.parameters()):\n", "                if global_param.grad is not None:\n", "                    global_param._grad = local_param.grad\n", "            optimizer.step()\n", "        ..."]}, {"cell_type": "markdown", "id": "398e29ac", "metadata": {}, "source": ["2. 梯度累积：在本地累积梯度，然后定期更新全局模型。"]}, {"cell_type": "raw", "id": "ee1fa369-eab9-422f-a1cf-74c6b6cf941f", "metadata": {}, "source": ["#范例代码的一部分，无法单独运行，因此请【不要】尝试运行本单元。\n", "\n", "class ParallelDQNRobot(DQNRobot):\n", "    def __init__(self):\n", "        ...\n", "        self.grad_accumulation_steps = 10\n", "        self.step_counter = 0\n", "\n", "    def learn(self):\n", "        ...\n", "        loss.backward()\n", "        self.step_counter += 1\n", "\n", "        if self.step_counter % self.grad_accumulation_steps == 0:\n", "            with self.update_lock:\n", "                for local_param, global_param in zip(local_dqn.eval_net.parameters(),\n", "                                                     global_dqn.eval_net.parameters()):\n", "                    if global_param.grad is None:\n", "                        global_param.grad = local_param.grad.clone()\n", "                    else:\n", "                        global_param.grad += local_param.grad\n", "                optimizer.step()\n", "                optimizer.zero_grad()\n", "        ..."]}, {"cell_type": "markdown", "id": "88ffe005", "metadata": {}, "source": ["3. 参数平均：定期收集所有本地模型的参数，计算平均值作为新的全局模型。"]}, {"cell_type": "raw", "id": "f6c3795f-ca26-4ddf-829e-4529edd8cb10", "metadata": {}, "source": ["#范例代码的一部分，无法单独运行，因此请【不要】尝试运行本单元。\n", "\n", "class ParallelDQN:\n", "    def average_models(self):\n", "        with self.update_lock:\n", "            avg_state_dict = {name: param.clone() for name, param in self.global_dqn.state_dict().items()}\n", "            for agent in self.agents:\n", "                agent_state_dict = agent.local_dqn.state_dict()\n", "                for name, param in avg_state_dict.items():\n", "                    avg_state_dict[name] += agent_state_dict[name]\n", "            for name in avg_state_dict:\n", "                avg_state_dict[name] /= (len(self.agents) + 1)\n", "            self.global_dqn.load_state_dict(avg_state_dict)\n", "            for agent in self.agents:\n", "                agent.local_dqn.load_state_dict(self.global_dqn.state_dict())"]}, {"cell_type": "markdown", "id": "ddf59264", "metadata": {}, "source": ["4. 异步参数服务器：实现一个中央参数服务器来管理更新。"]}, {"cell_type": "raw", "id": "7d19eaf6-a1c0-4417-8882-72e0ada1b23b", "metadata": {}, "source": ["#范例代码的一部分，无法单独运行，因此请【不要】尝试运行本单元。\n", "\n", "class ParameterServer:\n", "    def __init__(self, model):\n", "        self.model = model\n", "        self.optimizer = torch.optim.Adam(self.model.parameters())\n", "        self.lock = threading.Lock()\n", "\n", "    def update(self, gradients):\n", "        with self.lock:\n", "            for param, grad in zip(self.model.parameters(), gradients):\n", "                if param.grad is None:\n", "                    param.grad = grad\n", "                else:\n", "                    param.grad += grad\n", "            self.optimizer.step()\n", "            self.optimizer.zero_grad()\n", "\n", "class ParallelDQNRobot(DQNRobot):\n", "    def learn(self):\n", "        ...\n", "        loss.backward()\n", "        gradients = [param.grad.clone() for param in self.local_dqn.parameters()]\n", "        self.parameter_server.update(gradients)\n", "        self.sync_with_global()\n", "        ..."]}, {"cell_type": "markdown", "id": "b5f1c7b7", "metadata": {}, "source": ["5. 使用分布式数据并行：利用PyTorch的DistributedDataParallel来自动处理梯度同步和更新。"]}, {"cell_type": "raw", "id": "6ee35c05-02e3-4389-b66d-7b54953e5f98", "metadata": {}, "source": ["#范例代码的一部分，无法单独运行，因此请【不要】尝试运行本单元。\n", "\n", "import torch.distributed as dist\n", "from torch.nn.parallel import DistributedDataParallel as DDP\n", "\n", "class ParallelDQN:\n", "    def __init__(self):\n", "        ...\n", "        self.model = DDP(self.model)\n", "\n", "class ParallelDQNRobot(DQNRobot):\n", "    def learn(self):\n", "        ...\n", "        loss.backward()\n", "        optimizer.step()\n", "        ..."]}, {"cell_type": "markdown", "id": "cc446c53", "metadata": {}, "source": ["6. 弹性平均SGD：允许本地模型在一定程度上偏离全局模型"]}, {"cell_type": "raw", "id": "05633a37-1dd4-4c2a-800e-3c6699a0dc24", "metadata": {}, "source": ["#范例代码的一部分，无法单独运行，因此请【不要】尝试运行本单元。\n", "\n", "class ElasticAveragingSGD(torch.optim.Optimizer):\n", "    def __init__(self, params, lr=0.01, rho=0.1, weight_decay=0):\n", "        defaults = dict(lr=lr, rho=rho, weight_decay=weight_decay)\n", "        super(ElasticAveragingSGD, self).__init__(params, defaults)\n", "        \n", "    def step(self, closure=None):\n", "        loss = None\n", "        if closure is not None:\n", "            loss = closure()\n", "\n", "        for group in self.param_groups:\n", "            for p in group['params']:\n", "                if p.grad is None:\n", "                    continue\n", "                d_p = p.grad.data\n", "                if group['weight_decay'] != 0:\n", "                    d_p.add_(group['weight_decay'], p.data)\n", "                p.data.add_(-group['lr'], d_p)\n", "                p.data.add_(-group['rho'], p.data - group['params_server'])\n", "\n", "        return loss"]}, {"cell_type": "markdown", "id": "69d56015-4c94-4361-b9f1-fca4c354e91a", "metadata": {}, "source": ["## 小结\n", "\n", "本课讲解了并行DQN模型训练和并行PPO模型训练的原理，并分别给出了范例代码，进一步还讲解了如何结合单机版数字冰壶服务器在本地PC机上实现并行DQN模型训练和并行PPO模型训练。\n", "\n", "强化学习模型都需要上万局才能看到训练效果，并行模型训练方式可以在本地PC上启动数个进程（进程数量取决于本地PC机的CPU核数和内存容量），可以大大提高训练效率。\n", "\n", "另外需要注意的是，在本教程里提供的各种强化学习算法的范例代码，都仅演示了基本的功能，还有大量需要优化的细节，需要大家自行研究。"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.14"}}, "nbformat": 4, "nbformat_minor": 5}