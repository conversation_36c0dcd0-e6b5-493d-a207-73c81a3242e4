{"cells": [{"cell_type": "markdown", "id": "c18fe3fa-9723-4076-9a59-9924aa90fb5d", "metadata": {"tags": []}, "source": ["# 第九课 蒙特卡洛树搜索算法\n", "\n", "## 9.1 蒙特卡洛数搜索算法简介\n", "\n", "## 9.1.1 马尔可夫过程的求解\n", "\n", "在第五课中讲到过，冰壶比赛具有典型的马尔科夫性质，因此可以使用马尔科夫决策过程在数字冰壶虚拟仿真平台中模拟冰壶对战中智能体可实现的策略与回报。并且给出了冰壶的马尔科夫决策过程建模。在本课中我们就来讲解如何用蒙特卡洛树搜索算法来近似求解这个马尔科夫决策过程。\n", "\n", "马尔科夫过程一般有解析求解法、动态规划求解法和蒙特卡洛求解法。通过解析法求解，在离散有穷状态空间内状态价值是唯一可计算的，但在计算过程中涉及到矩阵逆计算，当状态空间过大时计算将会特别耗时，因此该方法实际应用较少。动态规划作为一种常用的求解马尔科夫决策过程的方法，该方法通过将问题分解成一系列子问题后通过递归的方式求解得到全局最优解，解决了解析解难以解决的复杂状态空间的问题，在具体实现中使用贝尔曼方程来递归地计算值函数。 \n", "\n", "通常来说无论是使用解析解求解还是用动态规划求解都要求建模者对任务有着充分的了解，需要有一定该相关领域的知识储备，除此之外还需要许多的\n", "调研和分析处理的工作，具有较大的成本。蒙特卡洛方法作为一种无模型的强化学习方法，用其进行求解则可以避免上述问题。\n", "\n", "### 9.1.2 蒙特卡洛数搜索树算法的历史\n", "\n", "对于一般的博弈问题，通常采用博弈树搜索的方法，但是该方法具有较大的局限性——博弈树的深度不可过深，这主要是由于博弈树需要处理的节点数与搜索所需要的时间将会随着博弈树的深度的加深呈现指数型增长。并且博弈树搜索方法通常需要对所有博弈状态进行探索，而在不完备信息博弈中存在许多隐藏信息，这使得传统的博弈树搜索方法难以实现。\n", "\n", "2006年法国计算机科学家Rémi Coulom提出蒙特卡洛树搜索算法(MCTS)，该算法将蒙特卡洛法与博弈树搜索结合，是一种启发式搜索算法。基于该算法Rémi Coulom开发了“Crazy Stone”的围棋程序，在2008年该围棋程序在被让8子的情况下战胜了职业棋手青叶熏四段，这是计算机程序首次在让9子之内战胜职业棋手。随着深度学习浪潮的推进，MCTS与人工神经网络结合，进一步发挥了巨大的作用，典型的例子是2016年的AlphaGo，以4:1的比分战胜了韩国的9段棋手李世石。\n", "\n", "对于复杂的问题，蒙特卡洛树搜索能在保证所求解的近似最优性的同时，还能降低问题的规模。蒙特卡洛方法是一种通过采样经验平均值来近似估计随机变量期望的方法。例如在时刻$s$时博弈状态的期望状态价值为$V_π(S)$，该值一般很难通过解析解或者动态规划的方法直接得到，但可以用蒙特卡洛方法不断进行实际采样从而得到一系列的总收益$G_1(s),G_2(s),...,G_n(s)$，若定义$V(s)$为采样总收益的均值则有公式 (9-1) ，并根据大数定律，当 $n→\\infty$时，采样的总收益均值将趋近于期望值，即$V(s)→V_π(s)$。\n", "\n", "$$V(s)=\\frac{(G_1(s)+G_2(s)+...+G_n(s))}{n}\\tag{9-1}$$\n", "\n", "蒙特卡洛树搜索算法(MCTS)通过模拟和树搜索的结合来找到近似的最优解，其重点在于搜索树的构建。MCTS使用一颗搜索树来表示可能的决策路径。每个节点代表一个游戏状态或规划问题的状态，而树的边代表执行一个行动的结果。而MCTS算法的核心思想就在于对蒙塔卡洛树进行多次探索，在探索中更新节点的价值及访问次数，最终选择最优的节点。\n", "\n", "### 9.1.3 蒙特卡洛数搜索树的构建\n", "\n", "蒙特卡洛树搜索是一种基于树结构的蒙特卡洛方法，该方法不需训练策略或者价值网络，而是在动作搜索空间中进行启发式搜索，基于反馈寻找出树结构最优路径。蒙特卡洛树包含多个节点，每个节点对应一局冰壶对弈的某个比赛状态，此外每个节点存储的信息包括该节点在多次模拟过程被选择的次数以及该节点的价值。该算法的核心思想是对蒙塔卡洛树进行多次探索，在探索中更新节点的价值及访问次数，最终选择最优的节点。\n", "\n", "蒙特卡洛树搜索算法包括四个主要步骤：选择、扩展、模拟、回传。\n", "\n", "#### 选择\n", "\n", "这里的选择并非是选取最终要执行的动作，而是在每次搜索中选择某个节点的子节点从而进行更新价值与访问次数，传统蒙特卡洛树搜索中选择函数为置信度上限函数（Upper Confidence bound apply to Tree，UCT），公式如下，最终在子节点中选择使UCT函数最大的节点：\n", "\n", "$$UCT(v_i, v)=\\frac{Q(v_i)}{N(v_i)}+c\\frac{log(N(v))}{N(v_i)}\\tag{9-2}$$\n", "\n", "式中的$v$代表父节点，$v_i$代表子节点，$Q$指该节点的总价值（随着访问次数更新），$N$指该节点的访问次数。UCT公式右侧的第一项表示该节点的平均价值（总收益/总次数=平均每次的收益），代表子节点的胜率估计，为利用项（exploitation component）。UCT公式右侧第二项为父节点与子节点的访问次数比值，该部分倾向于选择访问次数较少的子节点，为探索项（exploration component）。而公式中的参数c为平衡探索与利用的系数。\n", "\n", "#### 扩展\n", "\n", "扩展指的是将未被访问的动作节点扩展到蒙特卡洛树上，若一个节点下的全部动作均被访问过，则称该节点为完全扩展。常见的扩展有两种方法，一种是在动作空间中将未被访问过的动作节点随着搜索逐次将其扩展到蒙特卡洛树上，第二种方法是将动作空间中所有未被访问的动作节点一次性扩展到蒙特卡洛树上。通常第二种方法扩展节点过程伴随着选择此动作节点的概率，AlphaGo在蒙特卡洛树搜索中使用此方法进行扩展。\n", "\n", "#### 模拟与回传\n", "\n", "模拟与回传通常一起使用来更新扩展的新节点（第一种扩展方法）或被选择的节点的总价值与访问次数。模拟的具体执行过程由待评价节点的状态开始，在数字冰壶仿真环境中使用随机策略（rollout）进行对弈直到本局对弈结束，得到最终奖励。回传指的是将模拟过程最终的奖励累加到待更新节点的总价值，并使其访问次数加一，但模拟步骤中间过程的随机节点不进行更新。\n", "\n", "通过不断地重复进行上述四个步骤，蒙特卡洛树搜索会逐渐构建出一个搜索树，根据搜索树地统计信息来指导搜索过程，最终找到最优解或最优动作，对于数字冰壶而言就是找到最优的冰壶投掷策略。蒙特卡洛树搜索的具体执行流程图如下图所示。\n", "\n", "<center><img src=\"img/montecarlo1.png\" width=800></center>\n", "\n", "### 9.1.4 引入策略价值网络的蒙特卡洛搜索树算法\n", " \n", "传统的蒙特卡洛树搜索每次动作采样均选择随机策略，所以需要进行多次采样才能得到准确的价值。对于数字冰壶策略决策，需要建立最大深度为16，每个节点的子节点个数最大为2048的搜索树，在此树上进行搜索耗费大量时间。\n", "\n", "在蒙特卡洛树搜索算法中扩展和模拟是消耗时间的主要步骤，逐个动作扩展以及采用rollout策略对每个扩展动作进行评估会大大超出冰壶击打的规定时间，而且采用此方法生成数据训练策略价值网络速度较慢。\n", "\n", "为减小搜索树的深度，可以引入价值网络（Value Network）。传统蒙特卡洛树搜索方法为得到某个节点的价值，在模拟时需要模拟到对弈的最后一步，如果引入价值网络，并且将待评估节点对应的状态输入到价值网络，那么可以直接得到该节点对应的价值，无需进行耗时的模拟过程。\n", "\n", "为减少搜索在扩展步骤消耗的时间，还可以引入策略网络（Policy Network）。传统蒙特卡洛树方法在扩展时，需要随着搜索次数逐一将动作扩展到搜索树上，如果引入策略网络，并且将某一节点对应的状态输入到策略网络得到该状态下执行不同动作的概率分布，可以直接将伴随概率分布的所有节点扩展到该子节点下。\n", "\n", "结合蒙特卡洛强化学习和价值策略网络，的确在某种程度上可以减少对于完整动作空间的遍历。然而，这并不是意味着完全不需要遍历动作空间，而是通过一些策略来更有效地探索和利用动作空间。以下是如何结合两者的一个简要概述：\n", "\n", "#### 结合蒙特卡洛和价值策略网络的优点\n", "\n", "1. **初始策略训练**：\n", "    - 使用策略网络生成一系列动作，得到多个完整的序列（轨迹）。\n", "    - 使用蒙特卡洛方法，根据这些序列中的回报值来更新策略网络和价值网络。\n", "<br><br>\n", "\n", "2. **策略评估与改进**：\n", "    - 策略网络生成动作，而价值网络评估这些动作的价值。通过这个过程，模型在每一步中有了更好的判断以执行更优的动作。\n", "    - 尽管没有遍历整个动作空间，策略网络会倾向于选择高概率的、最优的动作。\n", "<br><br>\n", "  \n", "3. **减少动作空间遍历**：\n", "    - 策略网络的引入极大地减少了动作空间的搜索，因为网络依据策略概率选择动作而非遍历所有可能的动作。\n", "    - 即使策略网络给出的动作并非最佳的，连续迭代可以不断调整，逐渐逼近最优策略。\n", "<br><br>\n", "\n", "4. **经验回放（Experience Replay）**：\n", "    - 通过将蒙特卡洛片段存储在经验库中，你可以在后续的训练过程中多次使用这些片段来更新网络，从而提高数据利用率并减少对新数据的遍历需求。\n", "\n", "通过将蒙特卡洛强化学习和价值策略网络相结合，可以减少对于动作空间的完整遍历，更多地依赖于策略网络来进行有针对性的动作选择。价值网络能提供状态的评估，加速模型收敛。这种结合方法被广泛应用于一些复杂的决策场景，如围棋、国际象棋等，效果显著。例如，AlphaGo使用了蒙特卡洛树搜索（MCTS）与深度神经网络的结合来提高其水平。\n", "\n", "下图展示了蒙特卡洛树搜索结合策略价值网络的具体方法。选择与回传步骤不变，图中$policy=f(s)_policyhead$指在状态$s$下的策略输出，$value=f(s)_valuehead$指在状态$s$下的价值输出。\n", "\n", "<center><img src=\"img/montecarlo2.png\" width=800></center>\n", "\n", "因为结合策略网络的扩展步骤使用了动作的先验概率，则UCT选择函数也有所改变，具体形式如下式所示：\n", " \n", "$$UCT_p(v_i, v)=\\frac{Q(v_i)}{N(v_i)}+c \\cdot p_i \\cdot \\frac{log(N(v))}{N(v_i)}\\tag{9-3}$$\n", "\n", "式中的$p_i$为子节点的动作概率，剩余部分与UCT函数相同。\n"]}, {"cell_type": "markdown", "id": "8a26ecd7-0835-44dd-9b69-7028b23eb48f", "metadata": {}, "source": ["## 9.2 蒙特卡洛搜索树算法在数字冰壶比赛中的实现\n", "\n", "本节课使用类似AlphaGo方法将神经网络和蒙特卡洛树搜索结合。\n", "\n", "训练一个策略价值网络：这是AlphaGo算法的核心创新之一。网络同时输出：\n", "1. 策略：表示在当前状态下采取各种动作的概率。\n", "2. 价值：估计当前状态下当前玩家获胜的概率。\n", "\n", "使用策略网络来指导MCTS的搜索，使搜索更加高效。使用价值网络来评估叶节点，取代传统MCTS中的随机rollout。进一步还可以改进超参数（如MCTS的模拟次数、神经网络的结构等）以及优化训练过程。从而达到更好的结果。\n", "\n", "### 9.2.1 网络搭建\n", "\n", "#### >> 导入算法实现所需要的模块："]}, {"cell_type": "code", "execution_count": null, "id": "5ece6089-cf54-4499-8177-a2ef7ef019bb", "metadata": {"vscode": {"languageId": "raw"}}, "outputs": [], "source": ["import os\n", "import time\n", "\n", "import numpy as np\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch import softmax, optim\n", "from collections import deque\n", "import random\n", "import math"]}, {"cell_type": "markdown", "id": "229b6cf7-a234-4b4d-b1df-4ab4648fe17d", "metadata": {"tags": []}, "source": ["#### >> 网络输入设置\n", "\n", "如前所述，根据场上的冰壶与营垒圆心的距离由近至远进行排序，每个冰壶包含五个信息：x坐标、y坐标、离营垒圆心的距离、投掷顺序、是否为有效得分壶，共80个特征作为网络输入。\n", "\n", "这种描述方式仅供参考，对于环境的描述是强化学习的重要环节，不同的环境描述会影响到算法训练模型的效率与性能。"]}, {"cell_type": "code", "execution_count": null, "id": "404d6281-7a35-4a6c-8ef9-7d375ed11932", "metadata": {"vscode": {"languageId": "raw"}}, "outputs": [], "source": ["# 计算某一冰壶距离营垒圆心的距离\n", "def get_dist(x, y):\n", "    House_x = 2.375\n", "    House_y = 4.88\n", "    return math.sqrt((x - House_x) ** 2 + (y - House_y) ** 2)\n", "\n", "\n", "# 根据冰壶位置坐标列表获取得分情况并生成信息状态数组\n", "def get_infostate(position):\n", "    House_R = 1.830\n", "    Stone_R = 0.145\n", "\n", "    init = np.empty([8], dtype=float)\n", "    gote = np.empty([8], dtype=float)\n", "    both = np.empty([16], dtype=float)\n", "    # 计算双方冰壶到营垒圆心的距离\n", "    for i in range(8):\n", "        init[i] = get_dist(position[4 * i], position[4 * i + 1])\n", "        both[2 * i] = init[i]\n", "        gote[i] = get_dist(position[4 * i + 2], position[4 * i + 3])\n", "        both[2 * i + 1] = gote[i]\n", "    # 找到距离圆心较远一方距离圆心最近的壶\n", "    if min(init) <= min(gote):\n", "        win = 0  # 先手得分\n", "        d_std = min(gote)\n", "    else:\n", "        win = 1  # 后手得分\n", "        d_std = min(init)\n", "\n", "    infostate = []  # 状态数组\n", "    init_score = 0  # 先手得分\n", "    # 16个冰壶依次处理\n", "    for i in range(16):\n", "        x = position[2 * i]  # x坐标\n", "        y = position[2 * i + 1]  # y坐标\n", "        dist = both[i]  # 到营垒圆心的距离\n", "        sn = i % 2 + 1  # 投掷顺序\n", "        if (dist < d_std) and (dist < (House_R + Stone_R)) and ((i % 2) == win):\n", "            valid = 1  # 是有效得分壶\n", "            # 如果是先手得分\n", "            if win == 0:\n", "                init_score = init_score + 1\n", "            # 如果是后手得分\n", "            else:\n", "                init_score = init_score - 1\n", "        else:\n", "            valid = 0  # 不是有效得分壶\n", "        # 仅添加有效壶\n", "        if x != 0 or y != 0:\n", "            infostate.append([x, y, dist, sn, valid])\n", "    # 按dist升序排列\n", "    infostate = sorted(infostate, key=lambda x: x[2])\n", "\n", "    # 无效壶补0\n", "    for i in range(16 - len(infostate)):\n", "        infostate.append([0, 0, 0, 0, 0])\n", "\n", "    # 返回先手得分和转为一维的状态数组\n", "    return init_score, np.array(infostate).flatten()"]}, {"cell_type": "markdown", "id": "551e8117-e253-4919-b276-6654bf33cc56", "metadata": {}, "source": ["#### >> 网络输出设置\n", "\n", "数字冰壶比赛中的动作是AI选手投壶时给出的初速度v0（0≤v0≤6）、横向偏移h0（-2.23≤h0≤2.23）和初始角速度ω0（-3.14≤ω0≤3.14）。\n", "\n", "投壶初始速度的取值范围是0-6，而实际上在得分区中没有壶的情况下，能够投出得分壶的初始速度范围大概在2.8-3.2之间，而初速度在2.4-2.8之间的壶大概率是停留在防守区。因此考虑将初始速度在(2.4,2.7)之间以0.1为步长进行离散、在(2.8,3.2)之间以0.05为步长进行离散用于执行保护战术，另外再给出4、5和6三个速度值用于执行击飞战术。\n", "\n", "当投壶初始速度为3（球停在7区）时，能够投出得分壶的横向偏移范围大概在(-2,2)之间，因此考虑将初始速度在(-2,2)之间以十分之一为步长进行离散，而初始角速度则以值域的十分之一为步长进行离散。\n", "\n", "如上所述，最终将投掷动作离散化为1600种不同的组合，作为网络输出。"]}, {"cell_type": "code", "execution_count": null, "id": "1d860141-7199-403f-8339-42113cf4e010", "metadata": {"vscode": {"languageId": "raw"}}, "outputs": [], "source": ["# 冰壶游戏状态类\n", "class CurlingGameState:\n", "    def __init__(self):\n", "        self.position = [0] * 32  # 16个冰壶的x,y坐标\n", "        self.shot_num = 0  # 当前投掷次数\n", "        self.player_is_init  = 0  # 当前玩家（0为先手，1为后手）\n", "        self.score = 0  # 当前比分\n", "        self.action_space = self.generate_action_space()  # 生成动作空间\n", "\n", "    # 生成离散化的动作空间\n", "    def generate_action_space(self):\n", "        # 低速：在(2.4,2.7)之间以0.1为步长进行离散\n", "        slow = np.arange(2.4, 2.7, 0.1)\n", "        # 中速：在(2.8,3.2)之间以0.05为步长进行离散\n", "        normal = np.arange(2.8, 3.2, 0.05)\n", "        # 高速\n", "        fast = np.array([4, 5, 6])\n", "        # 将低速、中速、高速三个数组连接起来\n", "        speed = np.concatenate((slow, normal, fast))\n", "        # 横向偏移在(-2,2)之间以0.4为步长进行离散\n", "        deviation = np.arange(-2, 2, 0.4)\n", "        # 角速度在(-3.14, 3.14)之间以0.628为步长进行离散\n", "        angspeed = np.arange(-3.14, 3.14, 0.628)\n", "\n", "        action_space = []\n", "        for v in speed:\n", "            for h in deviation:\n", "                for w in angspeed:\n", "                    action_space.append((v, h, w))\n", "        return action_space\n", "\n", "    # 更新游戏状态\n", "    def update_state(self, new_state):\n", "        self.position = new_state['position']\n", "        self.shot_num = new_state['shot_num']\n", "        self.player_is_init  = new_state['player_is_init']\n", "        self.score = new_state['score']\n", "\n", "    # 检查游戏是否结束\n", "    def game_end(self):\n", "        return self.shot_num == 16\n", "\n", "    # 获取胜者\n", "    def get_winner(self):\n", "        if self.score > 0:\n", "            return 1  # 先手胜\n", "        elif self.score < 0:\n", "            return -1  # 后手胜\n", "        else:\n", "            return 0  # 平局\n", "\n", "    # 获取可用动作\n", "    def available_moves(self):\n", "        return self.action_space\n", "\n", "    def copy(self):\n", "        # 创建一个新的 CurlingGameState 实例\n", "        new_state = CurlingGameState()\n", "        # 复制所有属性\n", "        new_state.position = self.position.copy()\n", "        new_state.shot_num = self.shot_num\n", "        new_state.player_is_init = self.player_is_init\n", "        new_state.score = self.score\n", "        return new_state"]}, {"cell_type": "markdown", "id": "b2b9a84b-64ae-45df-9e20-33b0eaa8432e", "metadata": {}, "source": ["#### >> 网络结构设置\n", "\n", "搭建网络就要用到pytorch专门为神经网络设计的模块化接口torch.nn，该接口构建于autograd之上，可以用来定义和运行神经网络。基于该接口定义自己的网络要用到nn.Module类，该类中包含网络各层的定义及forward方法。\n", "\n", "如下所示范例代码搭建了一个比较简单的三层神经网络，每一层都是线性层（全连接层），实现将80维的输入张量映射为256维张量再经Relu函数激活，继而映射为256维张量，再经Relu函数激活，在策略网络输出层映射为1600维的输出张量，而在价值网络输出层映射为1维张量。"]}, {"cell_type": "code", "execution_count": null, "id": "381b5ca5-ac39-4e34-8913-1943dbb830c0", "metadata": {"vscode": {"languageId": "raw"}}, "outputs": [], "source": ["# 策略价值网络\n", "class PolicyValueNet(nn.Module):\n", "    def __init__(self, input_size=80, action_size=1600):\n", "        super(PolicyValueNet, self).__init__()\n", "        self.fc1 = nn.Linear(input_size, 256)         # 定义全连接层1\n", "        self.fc1.weight.data.normal_(0, 0.1)          # 按(0, 0.1)的正态分布初始化权重\n", "        self.fc2 = nn.Linear(256, 256)                # 定义全连接层2\n", "        self.fc2.weight.data.normal_(0, 0.1)          # 按(0, 0.1)的正态分布初始化权重\n", "        self.fc_policy = nn.Linear(256, action_size)  # 定义策略网络输出层\n", "        self.fc_policy.weight.data.normal_(0, 0.1)    # 按(0, 0.1)的正态分布初始化权重\n", "        self.fc_value = nn.Linear(256, 1)             # 定义价值网络输出层\n", "        self.fc_value.weight.data.normal_(0, 0.1)     # 按(0, 0.1)的正态分布初始化权重\n", "\n", "    def forward(self, x):\n", "        #print(\"  >> PolicyValueNet_forward\")\n", "        x = F.relu(self.fc1(x))                       # 输入张量经全连接层1传递后经relu函数激活\n", "        x = F.relu(self.fc2(x))                       # 经全连接层2传递后经relu函数激活\n", "        policy_logits = self.fc_policy(x)             # 经策略网络输出层传递得到策略输出\n", "        value = self.fc_value(x)                      # 经价值网络输出层传递得到价值输出\n", "        #print(\"<< PolicyValueNet_forward\")\n", "        return policy_logits, value"]}, {"cell_type": "markdown", "id": "dd393f80-f8fb-4ef1-9d8b-255f08dad429", "metadata": {}, "source": ["### 9.2.2 MTCS模型搭建\n", "\n", "#### >> 模型训练超参数设置\n", "\n", "范例代码如下所示，需要注意的是代码中的n_playout设置的越大，每次投壶就越慢。"]}, {"cell_type": "code", "execution_count": null, "id": "0d089151-c885-41ed-bc5b-87e171b2c4b1", "metadata": {}, "outputs": [], "source": ["c_puct = 5          # 探索常数\n", "n_playout = 10      # 每次移动的模拟次数\n", "temperature = 1e-3  # 温度参数，控制探索程度"]}, {"cell_type": "markdown", "id": "a0984f96-e496-48ef-a8ac-9f78a263ee35", "metadata": {}, "source": ["#### >>模型搭建"]}, {"cell_type": "code", "execution_count": null, "id": "8f65ed44-c64b-480c-bcf0-fa3ab49e51a9", "metadata": {"vscode": {"languageId": "raw"}}, "outputs": [], "source": ["# 蒙特卡洛树搜索类\n", "class MCTS:\n", "    def __init__(self, policy_value_fn, c_puct=c_puct, n_playout=n_playout):\n", "        self.policy_value_fn = policy_value_fn  # 策略价值函数\n", "        self.c_puct = c_puct  # 探索常数\n", "        self.n_playout = n_playout  # 每次移动的模拟次数\n", "        self.Q = {}  # 存储动作价值\n", "        self.N = {}  # 存储访问次数\n", "        self.P = {}  # 存储先验概率\n", "\n", "    def get_action_probs(self, state, temperature=temperature):\n", "        \"\"\"\n", "        执行多次 playout，并返回可用动作及其对应的概率。\n", "        :param state: 当前游戏状态\n", "        :param temperature: 温度参数，控制探索程度\n", "        :return: 动作列表和对应的概率分布\n", "        \"\"\"\n", "        for _ in range(self.n_playout):\n", "            self._playout(state)\n", "        s = self._get_state_key(state)\n", "        counts = np.array([self.N.get((s, a), 0) for a in state.available_moves()])\n", "\n", "        if temperature == 0:  # 如果温度为0，选择访问次数最多的动作\n", "            bestAction = np.argmax(counts)\n", "            probs = np.zeros_like(counts)\n", "            probs[bestAction] = 1\n", "            return [state.available_moves()[bestAction]], probs\n", "\n", "        else:  # 否则，使用 softmax 函数计算概率分布\n", "            counts = counts.astype(float)\n", "            if temperature < 1e-3:\n", "                temperature = 1e-3  # 设置一个最小温度值\n", "\n", "            counts = np.log(counts + 1e-10) / temperature\n", "            counts_max = np.max(counts)\n", "            probs = np.exp(counts - counts_max)\n", "            probs /= np.sum(probs)\n", "            return state.available_moves(), probs\n", "\n", "    def _playout(self, state):\n", "        \"\"\"\n", "        从当前状态开始执行一次 playout。\n", "        :param state: 当前游戏状态\n", "        :return: 叶子节点的评估值\n", "        \"\"\"\n", "        s = self._get_state_key(state)\n", "        if s not in self.P:  # 如果是新状态，使用策略网络进行评估\n", "            action_probs, leaf_value = self.policy_value_fn(state)\n", "            self.P[s] = dict(zip(state.available_moves(), action_probs))\n", "            return -leaf_value\n", "\n", "        if s not in self.N:\n", "            self.N[s] = 0\n", "\n", "        # 选择最佳动作（UCB公式）\n", "        best_score = -float('inf')\n", "        best_action = None\n", "        for action, prob in self.P[s].items():\n", "            if (s, action) in self.Q:\n", "                u = self.Q[(s, action)] + self.c_puct * prob * np.sqrt(self.N[s]) / (1 + self.N[(s, action)])\n", "            else:\n", "                u = self.c_puct * prob * np.sqrt(self.N[s] + 1e-8)\n", "            if u > best_score:\n", "                best_score = u\n", "                best_action = action\n", "\n", "        # 评估选择的动作\n", "        value = self._evaluate(state, best_action)\n", "\n", "        # 更新统计信息\n", "        if (s, best_action) in self.Q:\n", "            self.Q[(s, best_action)] = (self.N[(s, best_action)] * self.Q[(s, best_action)] + value) / (self.N[(s, best_action)] + 1)\n", "            self.N[(s, best_action)] += 1\n", "        else:\n", "            self.Q[(s, best_action)] = value\n", "            self.N[(s, best_action)] = 1\n", "            \n", "        self.N[s] = self.N.get(s, 0) + 1\n", "        return -value\n", "\n", "    def _evaluate(self, state, action):\n", "        \"\"\"\n", "        使用策略价值网络评估状态\n", "        \"\"\"\n", "        # 确保 state 是 CurlingGameState 对象\n", "        if not isinstance(state, CurlingGameState):\n", "            raise TypeError(\"state must be a CurlingGameState object\")\n", "\n", "        # 将状态转换为网络输入格式\n", "        state_input = self._state_to_input(state, action)\n", "\n", "        # 使用策略价值网络进行评估\n", "        with torch.no_grad():\n", "            policy_logits, value = self.policy_value_fn(state)\n", "\n", "        # 返回价值估计\n", "        return value\n", "\n", "    def _state_to_input(self, state, action):\n", "        \"\"\"\n", "        将游戏状态和动作转换为网络输入格式\n", "        \"\"\"\n", "        # 确保 state 是 CurlingGameState 对象\n", "        if not isinstance(state, CurlingGameState):\n", "            raise TypeError(\"state must be a CurlingGameState object\")\n", "\n", "        # 使用 get_infostate 函数获取状态表示\n", "        _, state_info = get_infostate(state.position)\n", "        state_vector = torch.FloatTensor(state_info)\n", "        action_vector = torch.FloatTensor(action)\n", "        return torch.cat([state_vector, action_vector]).unsqueeze(0)\n", "\n", "    def _get_state_key(self, state):\n", "        # 使用元组而不是列表，因为元组是可哈希的\n", "        return tuple(state.position + [state.shot_num, int(state.player_is_init), state.score])\n", "\n", "\n", "# 核回归类\n", "class KernelRegression:\n", "    def __init__(self, kernel_width=0.1):\n", "        self.kernel_width = kernel_width\n", "        self.X = []\n", "        self.y = []\n", "\n", "    def add_data(self, x, y):\n", "        self.X.append(x)\n", "        self.y.append(y)\n", "\n", "    def predict(self, x):\n", "        weights = np.exp(-np.sum((np.array(self.X) - x) ** 2, axis=1) / (2 * self.kernel_width ** 2))\n", "        return np.sum(weights * np.array(self.y)) / np.sum(weights)"]}, {"cell_type": "markdown", "id": "e9035954-825b-491a-b28c-2a5c71271abe", "metadata": {}, "source": ["### 9.2.3 模型训练/部署\n", "\n", "#### >> 启动数字冰壶比赛服务器\n", "\n", "首先点击页面左上角Jupyter菜单中的[Run]菜单项，点击该该菜单项的[Start Curling Server]子菜单项，即可启动一个数字冰壶比赛服务器。然后点击数字冰壶比赛服务器界面中的【无限对局】按钮进入该模式。\n", "\n", "#### >> 运行训练/部署MTCS模型的AI选手\n", "\n", "下方给出的范例代码中创建了AIRobot类库的子类MCTSRobot，并重写了类的__init__()函数、recv_setstate()函数和get_bestshot()函数，并新增了policy_value_fn()函数、get_action()函数和train_step()函数。\n", "\n", "根据数字冰壶服务器界面中给出的连接信息修改下方代码中的连接密钥，再运行这段代码，即可启动一个应用MCTS算法进行投壶的AI选手。"]}, {"cell_type": "code", "execution_count": null, "id": "14acf378-7d03-42d6-be8b-0d3a64ac04c6", "metadata": {"vscode": {"languageId": "raw"}}, "outputs": [], "source": ["from AIRobot import AIRobot\n", "\n", "class MCTSRobot(AIRobot):\n", "    def __init__(self, key, name, host, port, round_max=10000):\n", "        # 初始化父类 AIRobot\n", "        super().__init__(key, name, host, port)\n", "\n", "        # 初始化策略价值网络\n", "        self.net = PolicyValueNet()\n", "        self.model_file = 'model/MCTS_model.pth'\n", "        # 如果存在已保存的模型，则加载它\n", "        if os.path.exists(self.model_file):\n", "            print(\"加载模型文件 %s\" % (self.model_file))\n", "            self.net.load_state_dict(torch.load(self.model_file))\n", "\n", "        # 初始化 MCTS\n", "        self.mcts = MCTS(self.policy_value_fn)\n", "        # 初始化优化器\n", "        self.optimizer = optim.<PERSON>(self.net.parameters(), lr=0.002, weight_decay=1e-4)\n", "\n", "        # 设置最大训练轮数\n", "        self.round_max = round_max\n", "        # 设置日志文件名\n", "        self.log_file_name = f'log/MCTS_{time.strftime(\"%y%m%d_%H%M%S\")}.log'\n", "        self.score = 0\n", "\n", "    def policy_value_fn(self, game_state):\n", "        # 确保 game_state 是 CurlingGameState 对象\n", "        if not isinstance(game_state, CurlingGameState):\n", "            raise TypeError(\"game_state must be a CurlingGameState object\")\n", "\n", "        # 将游戏状态转换为神经网络输入\n", "        state_tensor = torch.FloatTensor(get_infostate(game_state.position)[1]).unsqueeze(0)\n", "        with torch.no_grad():\n", "            policy_logits, value = self.net(state_tensor)\n", "        # 将策略logits转换为概率分布\n", "        policy = nn.functional.softmax(policy_logits, dim=1).squeeze(0).numpy()\n", "        return policy, value.item()\n", "\n", "    def get_action(self, state, temperature=1e-3):\n", "        # 确保 state 是 CurlingGameState 对象\n", "        if not isinstance(state, CurlingGameState):\n", "            raise TypeError(\"state must be a CurlingGameState object\")\n", "\n", "        # 使用 MCTS 获取动作概率\n", "        actions, probs = self.mcts.get_action_probs(state, temperature)\n", "\n", "        # 如果温度接近于0，选择最佳动作\n", "        if temperature < 1e-3:\n", "            best_action = actions[np.argmax(probs)]\n", "        else:\n", "            # 否则，根据概率随机选择动作\n", "            # 创建一个表示动作索引的数组\n", "            action_indices = np.arange(len(actions))\n", "            # 使用概率选择一个动作的索引\n", "            selected_index = np.random.choice(action_indices, p=probs)\n", "            # 从动作列表中获取选定的动作\n", "            best_action = actions[selected_index]\n", "        return best_action\n", "\n", "    def train_step(self, state, mcts_probs, winner):\n", "        # 确保 state 是 CurlingGameState 对象\n", "        if not isinstance(state, CurlingGameState):\n", "            raise TypeError(\"state must be a CurlingGameState object\")\n", "\n", "        # 准备输入数据\n", "        _, state_info = get_infostate(state.position)\n", "        state_tensor = torch.FloatTensor(state_info).unsqueeze(0)\n", "        mcts_probs = torch.FloatTensor(mcts_probs).unsqueeze(0)\n", "        winner = torch.FloatTensor([winner]).unsqueeze(0)\n", "\n", "        # 前向传播\n", "        policy_logits, value = self.net(state_tensor)\n", "\n", "        # 计算损失\n", "        value_loss = nn.functional.mse_loss(value, winner)\n", "        policy_loss = -torch.mean(torch.sum(mcts_probs * nn.functional.log_softmax(policy_logits, dim=1), dim=1))\n", "        loss = value_loss + policy_loss\n", "\n", "        # 反向传播和优化\n", "        self.optimizer.zero_grad()\n", "        loss.backward()\n", "        self.optimizer.step()\n", "\n", "        return loss.item()\n", "\n", "    def recv_setstate(self, msg_list):\n", "        #当前完成投掷数\n", "        self.shot_num = int(msg_list[0])\n", "        #总对局数\n", "        self.round_total = int(msg_list[2])\n", "\n", "        # 如果达到最大轮数，结束训练\n", "        if self.round_num == self.round_max:\n", "            self.on_line = False\n", "            return\n", "\n", "        # 每局开始时初始化数据存储\n", "        if self.shot_num == 0:\n", "            self.states, self.mcts_probs= [], []\n", "            self.last_score = 0\n", "            # 根据先后手选取模型并设定当前选手第一壶是当局比赛的第几壶\n", "            if self.player_is_init:\n", "                self.first_shot = 0\n", "            else:\n", "                self.first_shot = 1\n", "\n", "        # 创建新的游戏状态\n", "        state = CurlingGameState()\n", "        state.update_state({\n", "            'position': self.position,\n", "            'shot_num': self.shot_num,\n", "            'player_is_init': self.player_is_init,\n", "            'score': self.score\n", "        })\n", "\n", "        # 对每个投掷进行处理\n", "        if self.shot_num < 16:\n", "            # 创建当前游戏状态\n", "            state = CurlingGameState()\n", "            state.position = self.position\n", "            state.shot_num = self.shot_num\n", "            state.player_is_init = self.player_is_init\n", "\n", "            # 设置温度参数\n", "            temperature = 1.0 if self.shot_num < 4 else 1e-3\n", "            # 获取动作概率\n", "            acts, act_probs = self.mcts.get_action_probs(state, temperature)\n", "\n", "            # 存储状态、概率和玩家信息\n", "            self.states.append(state)\n", "            self.mcts_probs.append(act_probs)\n", "\n", "            # 选择动作\n", "            action = self.get_action(state, temperature)\n", "            self.action = action  # 存储动作以供 get_bestshot 方法使用\n", "\n", "        # 一局结束后进行训练\n", "        if self.shot_num == 16:\n", "            for i in range(len(self.states)):\n", "                # 训练网络\n", "                loss = self.train_step(self.states[i], self.mcts_probs[i], self.score)\n", "\n", "            # 更新轮数和记录日志\n", "            self.round_num += 1\n", "            log_file = open(self.log_file_name, 'a+')\n", "            log_file.write(f\"score {self.score} {self.round_num}\\n\")\n", "            log_file.write(f\"loss {loss} {self.round_num}\\n\")\n", "            log_file.close()\n", "\n", "            # 每12轮保存一次模型\n", "            if self.round_num % 2 == 0:\n", "                torch.save(self.net.state_dict(), self.model_file)\n", "                print('============= Checkpoint Saved =============')\n", "\n", "    def get_bestshot(self):\n", "        \"\"\"\n", "        计算并返回最佳的投掷动作。\n", "        这个方法在收到服务器的 \"GO\" 指令时被调用。\n", "        :return: 格式化的 BESTSHOT 消息\n", "        \"\"\"\n", "        # 创建当前游戏状态\n", "        state = CurlingGameState()\n", "        state.position = self.position\n", "        state.shot_num = self.shot_num\n", "        state.player_is_init = self.player_is_init\n", "        state.score = self.score\n", "\n", "        # 使用 MCTS 获取动作概率\n", "        actions, probs = self.mcts.get_action_probs(state, temperature=1e-3)\n", "\n", "        # 选择概率最大的动作\n", "        best_action = actions[np.argmax(probs)]\n", "\n", "        # 将最佳动作转换为 BESTSHOT 消息格式\n", "        return f\"BESTSHOT {best_action[0]} {best_action[1]} {best_action[2]}\"\n", "\n", "#连接密钥：参照数字冰壶服务器界面中给出的连接信息填写，注意这个参数每次新启动服务器都会改变。\n", "key = \"lidandan_af84ff22-c7d1-449c-806e-51af3add1107\"\n", "\n", "myrobot = MCTSRobot(key, name=\"MCTSRobot\", host=\"curling-server-7788.jupyterhub.svc.cluster.local\", port=7788)\n", "myrobot.recv_forever()"]}, {"cell_type": "markdown", "id": "61e3640b-f634-4e7c-97d9-f059b231b243", "metadata": {}, "source": ["#### >> 运行基础AI选手\n", "\n", "训练强化学习算法需要一个对手，我们可以在控制台中运行AIRobot.py脚本启动CurlingAI选手，尝试训练一个PPO模型打败这个简单逻辑的基础AI，在数字冰壶比赛服务器界面可以看到＜Player2已连接＞。\n", "\n", "> 注意在运行脚本前要<b>根据数字冰壶服务器界面中提供的连接信息修改变量key的赋值</b>。\n", "\n", "#### >> 在无限对战中开始训练\n", "\n", "在数字冰壶服务器界面中点击【准备】按钮，再点击【开始对局】按钮，即可开始MCTS模型的训练/部署。\n", "\n", "在模型训练的过程中，随时可以通过在数字冰壶服务器界面中点击【返回主菜单】停止训练。"]}, {"cell_type": "markdown", "id": "eca05e73-b43a-4446-8ab2-4c9070d706f5", "metadata": {}, "source": ["### 9.2.4 训练过程曲线的绘制\n", "\n", "读取日志文件中的数据，绘制训练过程中的比分变化曲线和loss值变化曲线。"]}, {"cell_type": "code", "execution_count": null, "id": "30637eb4-f6f3-446c-b556-8e37926f90be", "metadata": {}, "outputs": [], "source": ["#导入matplotlib函数库\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "#定义两个曲线的坐标数组\n", "score_x, score_y = [], []\n", "loss_x, loss_y = [], []\n", "\n", "#读取日志文件\n", "log_file = open(myrobot.log_file_name, 'r')\n", "\n", "for line in log_file.readlines():\n", "    var_name, var_value, round_num = line.split(' ')\n", "    #存储比分曲线数据\n", "    if var_name == 'score':\n", "        score_x.append(int(round_num))\n", "        score_y.append(int(var_value))\n", "    #存储loss曲线数据\n", "    if var_name == 'loss':\n", "        loss_x.append(int(round_num))\n", "        loss_y.append(float(var_value))\n", "\n", "#分两个子图以散点图的方式绘制比分曲线和loss值曲线\n", "fig, axes = plt.subplots(2,1)\n", "axes[0].scatter(np.array(score_x),np.array(score_y),s=5)\n", "axes[1].scatter(np.array(loss_x),np.array(loss_y),s=5)\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "id": "91cadc70-4f4e-4e79-aea8-40088ba4ee05", "metadata": {"tags": []}, "source": ["## 9.3 自我对弈与并行MCTS模型训练\n", "\n", "前面给出的MCTS模型训练/部署的范例代码和DQN模型、PPO模型的训练方法类似，都是和基础AI（或者任何其他AI程序）对战完成模型训练。其实MCTS更倾向于通过自我对弈让两个AI对战并共同更新模型，使用生成的数据训练神经网络，改进策略和价值估计。重复这个过程，AI的能力会不断提升。\n", "\n", "自我对弈训练MCTS模型的范例代码如下所示，运行时会开启两个加载MCTS模型的AI选手，它俩分先后手同时连接一个数字冰壶服务器，在对战中共享经验库，共同更新模型。\n", "\n", "> 下方范例代码需要拷贝到本地PC上配合单机版数字冰壶服务器运行，请【不要】尝试运行下方代码单元。"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# -*- coding: utf-8 -*-\n", "\n", "import os, time, random, math\n", "import numpy as np\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch import softmax, optim\n", "from collections import deque\n", "import multiprocessing as mp\n", "from AIRobot import AIRobot\n", "\n", "\n", "# 计算某一冰壶距离营垒圆心的距离\n", "def get_dist(x, y):\n", "    House_x = 2.375\n", "    House_y = 4.88\n", "    return math.sqrt((x - House_x) ** 2 + (y - House_y) ** 2)\n", "\n", "\n", "# 根据冰壶位置坐标列表获取得分情况并生成信息状态数组\n", "def get_infostate(position):\n", "    House_R = 1.830\n", "    Stone_R = 0.145\n", "\n", "    init = np.empty([8], dtype=float)\n", "    gote = np.empty([8], dtype=float)\n", "    both = np.empty([16], dtype=float)\n", "    # 计算双方冰壶到营垒圆心的距离\n", "    for i in range(8):\n", "        init[i] = get_dist(position[4 * i], position[4 * i + 1])\n", "        both[2 * i] = init[i]\n", "        gote[i] = get_dist(position[4 * i + 2], position[4 * i + 3])\n", "        both[2 * i + 1] = gote[i]\n", "    # 找到距离圆心较远一方距离圆心最近的壶\n", "    if min(init) <= min(gote):\n", "        win = 0  # 先手得分\n", "        d_std = min(gote)\n", "    else:\n", "        win = 1  # 后手得分\n", "        d_std = min(init)\n", "\n", "    infostate = []  # 状态数组\n", "    init_score = 0  # 先手得分\n", "    # 16个冰壶依次处理\n", "    for i in range(16):\n", "        x = position[2 * i]  # x坐标\n", "        y = position[2 * i + 1]  # y坐标\n", "        dist = both[i]  # 到营垒圆心的距离\n", "        sn = i % 2 + 1  # 投掷顺序\n", "        if (dist < d_std) and (dist < (House_R + Stone_R)) and ((i % 2) == win):\n", "            valid = 1  # 是有效得分壶\n", "            # 如果是先手得分\n", "            if win == 0:\n", "                init_score = init_score + 1\n", "            # 如果是后手得分\n", "            else:\n", "                init_score = init_score - 1\n", "        else:\n", "            valid = 0  # 不是有效得分壶\n", "        # 仅添加有效壶\n", "        if x != 0 or y != 0:\n", "            infostate.append([x, y, dist, sn, valid])\n", "    # 按dist升序排列\n", "    infostate = sorted(infostate, key=lambda x: x[2])\n", "\n", "    # 无效壶补0\n", "    for i in range(16 - len(infostate)):\n", "        infostate.append([0, 0, 0, 0, 0])\n", "\n", "    # 返回先手得分和转为一维的状态数组\n", "    return init_score, np.array(infostate).flatten()\n", "    \n", "# 冰壶游戏状态类\n", "class CurlingGameState:\n", "    def __init__(self):\n", "        self.position = [0] * 32  # 16个冰壶的x,y坐标\n", "        self.shot_num = 0  # 当前投掷次数\n", "        self.player_is_init  = 0  # 当前玩家（0为先手，1为后手）\n", "        self.score = 0  # 当前比分\n", "        self.action_space = self.generate_action_space()  # 生成动作空间\n", "\n", "    # 生成离散化的动作空间\n", "    def generate_action_space(self):\n", "        # 低速：在(2.4,2.7)之间以0.1为步长进行离散\n", "        slow = np.arange(2.4, 2.7, 0.1)\n", "        # 中速：在(2.8,3.2)之间以0.05为步长进行离散\n", "        normal = np.arange(2.8, 3.2, 0.05)\n", "        # 高速\n", "        fast = np.array([4, 5, 6])\n", "        # 将低速、中速、高速三个数组连接起来\n", "        speed = np.concatenate((slow, normal, fast))\n", "        # 横向偏移在(-2,2)之间以0.4为步长进行离散\n", "        deviation = np.arange(-2, 2, 0.4)\n", "        # 角速度在(-3.14, 3.14)之间以0.628为步长进行离散\n", "        angspeed = np.arange(-3.14, 3.14, 0.628)\n", "\n", "        action_space = []\n", "        for v in speed:\n", "            for h in deviation:\n", "                for w in angspeed:\n", "                    action_space.append((v, h, w))\n", "        return action_space\n", "\n", "    # 更新游戏状态\n", "    def update_state(self, new_state):\n", "        self.position = new_state['position']\n", "        self.shot_num = new_state['shot_num']\n", "        self.player_is_init  = new_state['player_is_init']\n", "        self.score = new_state['score']\n", "\n", "    # 检查游戏是否结束\n", "    def game_end(self):\n", "        return self.shot_num == 16\n", "\n", "    # 获取胜者\n", "    def get_winner(self):\n", "        if self.score > 0:\n", "            return 1  # 先手胜\n", "        elif self.score < 0:\n", "            return -1  # 后手胜\n", "        else:\n", "            return 0  # 平局\n", "\n", "    # 获取可用动作\n", "    def available_moves(self):\n", "        return self.action_space\n", "\n", "    def copy(self):\n", "        # 创建一个新的 CurlingGameState 实例\n", "        new_state = CurlingGameState()\n", "        # 复制所有属性\n", "        new_state.position = self.position.copy()\n", "        new_state.shot_num = self.shot_num\n", "        new_state.player_is_init = self.player_is_init\n", "        new_state.score = self.score\n", "        return new_state\n", "        \n", "# 策略价值网络\n", "class PolicyValueNet(nn.Module):\n", "    def __init__(self, input_size=80, action_size=1600):\n", "        super(PolicyValueNet, self).__init__()\n", "        self.fc1 = nn.Linear(input_size, 256)         # 定义全连接层1\n", "        self.fc1.weight.data.normal_(0, 0.1)          # 按(0, 0.1)的正态分布初始化权重\n", "        self.fc2 = nn.Linear(256, 256)                # 定义全连接层2\n", "        self.fc2.weight.data.normal_(0, 0.1)          # 按(0, 0.1)的正态分布初始化权重\n", "        self.fc_policy = nn.Linear(256, action_size)  # 定义策略网络输出层\n", "        self.fc_policy.weight.data.normal_(0, 0.1)    # 按(0, 0.1)的正态分布初始化权重\n", "        self.fc_value = nn.Linear(256, 1)             # 定义价值网络输出层\n", "        self.fc_value.weight.data.normal_(0, 0.1)     # 按(0, 0.1)的正态分布初始化权重\n", "\n", "    def forward(self, x):\n", "        x = F.relu(self.fc1(x))                       # 输入张量经全连接层1传递后经relu函数激活\n", "        x = F.relu(self.fc2(x))                       # 经全连接层2传递后经relu函数激活\n", "        policy_logits = self.fc_policy(x)             # 经策略网络输出层传递得到策略输出\n", "        value = self.fc_value(x)                      # 经价值网络输出层传递得到价值输出\n", "        return policy_logits, value\n", "        \n", "c_puct = 5          # 探索常数\n", "n_playout = 10      # 每次移动的模拟次数\n", "temperature = 1e-3  # 温度参数，控制探索程度\n", "\n", "# 蒙特卡洛树搜索类\n", "class MCTS:\n", "    def __init__(self, policy_value_fn, c_puct=c_puct, n_playout=n_playout):\n", "        self.policy_value_fn = policy_value_fn  # 策略价值函数\n", "        self.c_puct = c_puct  # 探索常数\n", "        self.n_playout = n_playout  # 每次移动的模拟次数\n", "        self.Q = {}  # 存储动作价值\n", "        self.N = {}  # 存储访问次数\n", "        self.P = {}  # 存储先验概率\n", "\n", "    def get_action_probs(self, state, temperature=temperature):\n", "        \"\"\"\n", "        执行多次 playout，并返回可用动作及其对应的概率。\n", "        :param state: 当前游戏状态\n", "        :param temperature: 温度参数，控制探索程度\n", "        :return: 动作列表和对应的概率分布\n", "        \"\"\"\n", "        for _ in range(self.n_playout):\n", "            self._playout(state)\n", "\n", "        s = self._get_state_key(state)\n", "        counts = np.array([self.N.get((s, a), 0) for a in state.available_moves()])\n", "\n", "        if temperature == 0:  # 如果温度为0，选择访问次数最多的动作\n", "            bestAction = np.argmax(counts)\n", "            probs = np.zeros_like(counts)\n", "            probs[bestAction] = 1\n", "            return [state.available_moves()[bestAction]], probs\n", "\n", "        else:  # 否则，使用 softmax 函数计算概率分布\n", "            counts = counts.astype(float)\n", "            if temperature < 1e-3:\n", "                temperature = 1e-3  # 设置一个最小温度值\n", "\n", "            counts = np.log(counts + 1e-10) / temperature\n", "            counts_max = np.max(counts)\n", "            probs = np.exp(counts - counts_max)\n", "            probs /= np.sum(probs)\n", "\n", "            return state.available_moves(), probs\n", "\n", "    def _playout(self, state):\n", "        \"\"\"\n", "        从当前状态开始执行一次 playout。\n", "        :param state: 当前游戏状态\n", "        :return: 叶子节点的评估值\n", "        \"\"\"\n", "        s = self._get_state_key(state)\n", "        if s not in self.P:  # 如果是新状态，使用策略网络进行评估\n", "            action_probs, leaf_value = self.policy_value_fn(state)\n", "            self.P[s] = dict(zip(state.available_moves(), action_probs))\n", "            return -leaf_value\n", "\n", "        if s not in self.N:\n", "            self.N[s] = 0\n", "\n", "        # 选择最佳动作（UCB公式）\n", "        best_score = -float('inf')\n", "        best_action = None\n", "        for action, prob in self.P[s].items():\n", "            if (s, action) in self.Q:\n", "                u = self.Q[(s, action)] + self.c_puct * prob * np.sqrt(self.N[s]) / (1 + self.N[(s, action)])\n", "            else:\n", "                u = self.c_puct * prob * np.sqrt(self.N[s] + 1e-8)\n", "            if u > best_score:\n", "                best_score = u\n", "                best_action = action\n", "\n", "        # 评估选择的动作\n", "        value = self._evaluate(state, best_action)\n", "\n", "        # 更新统计信息\n", "        if (s, best_action) in self.Q:\n", "            self.Q[(s, best_action)] = (self.N[(s, best_action)] * self.Q[(s, best_action)] + value) / (self.N[(s, best_action)] + 1)\n", "            self.N[(s, best_action)] += 1\n", "        else:\n", "            self.Q[(s, best_action)] = value\n", "            self.N[(s, best_action)] = 1\n", "\n", "        self.N[s] = self.N.get(s, 0) + 1\n", "        return -value\n", "\n", "    def _evaluate(self, state, action):\n", "        \"\"\"\n", "        使用策略价值网络评估状态\n", "        \"\"\"\n", "        # 确保 state 是 CurlingGameState 对象\n", "        if not isinstance(state, CurlingGameState):\n", "            raise TypeError(\"state must be a CurlingGameState object\")\n", "\n", "        # 将状态转换为网络输入格式\n", "        state_input = self._state_to_input(state, action)\n", "\n", "        # 使用策略价值网络进行评估\n", "        with torch.no_grad():\n", "            policy_logits, value = self.policy_value_fn(state)\n", "\n", "        # 返回价值估计\n", "        return value\n", "\n", "    def _state_to_input(self, state, action):\n", "        \"\"\"\n", "        将游戏状态和动作转换为网络输入格式\n", "        \"\"\"\n", "        # 确保 state 是 CurlingGameState 对象\n", "        if not isinstance(state, CurlingGameState):\n", "            raise TypeError(\"state must be a CurlingGameState object\")\n", "\n", "        # 使用 get_infostate 函数获取状态表示\n", "        _, state_info = get_infostate(state.position)\n", "        state_vector = torch.FloatTensor(state_info)\n", "        action_vector = torch.FloatTensor(action)\n", "        return torch.cat([state_vector, action_vector]).unsqueeze(0)\n", "\n", "    def _get_state_key(self, state):\n", "        # 使用元组而不是列表，因为元组是可哈希的\n", "        return tuple(state.position + [state.shot_num, int(state.player_is_init), state.score])\n", "\n", "\n", "# 核回归类\n", "class KernelRegression:\n", "    def __init__(self, kernel_width=0.1):\n", "        self.kernel_width = kernel_width\n", "        self.X = []\n", "        self.y = []\n", "\n", "    def add_data(self, x, y):\n", "        self.X.append(x)\n", "        self.y.append(y)\n", "\n", "    def predict(self, x):\n", "        weights = np.exp(-np.sum((np.array(self.X) - x) ** 2, axis=1) / (2 * self.kernel_width ** 2))\n", "        return np.sum(weights * np.array(self.y)) / np.sum(weights)\n", "        \n", "class SharedReplayBuffer:\n", "    def __init__(self, capacity):\n", "        self.buffer = mp.Queue(maxsize=capacity)\n", "\n", "    def push(self, experience):\n", "        if self.buffer.full():\n", "            self.buffer.get() # 如果满了,移除最早的经验\n", "        self.buffer.put(experience)\n", "\n", "    def sample(self, batch_size):\n", "        #if self.buffer.qsize() < batch_size:\n", "        #    return list(self.buffer.queue) # 如果经验不足,返回所有可用经验\n", "        #return random.sample(list(self.buffer.queue), batch_size)\n", "        batch = [self.buffer.get() for _ in range(self.buffer.qsize())]\n", "        if len(batch) < batch_size:\n", "            return batch\n", "        return random.sample(batch, batch_size)\n", "    \n", "class SharedModel:\n", "    def __init__(self):\n", "        self.net = PolicyValueNet()\n", "        self.net.share_memory() # 使模型参数在多个进程间共享\n", "        self.optimizer = optim.<PERSON>(self.net.parameters(), lr=0.002, weight_decay=1e-4)\n", "        self.lock = mp.Lock() # 用于同步更新\n", "        self.replay_buffer = SharedReplayBuffer(capacity=10000)\n", "\n", "    def get_parameters(self):\n", "        return self.net.state_dict()\n", "\n", "    def set_parameters(self, parameters):\n", "        self.net.load_state_dict(parameters)\n", "\n", "    def update(self, state, mcts_probs, winner):\n", "        # 将经验存储到共享经验库\n", "        self.replay_buffer.push((state, mcts_probs, winner))\n", "        \n", "        loss = -1\n", "        # 从经验库中采样进行训练\n", "        with self.lock:\n", "            experiences = self.replay_buffer.sample(batch_size=32)\n", "            for exp in experiences:\n", "                loss = self.train_step(*exp)\n", "        return loss\n", "\n", "    def train_step(self, state, mcts_probs, winner):\n", "        # 准备输入数据\n", "        _, state_info = get_infostate(state.position)\n", "        state_tensor = torch.FloatTensor(state_info).unsqueeze(0)\n", "        mcts_probs = torch.FloatTensor(mcts_probs).unsqueeze(0)\n", "        winner = torch.FloatTensor([winner]).unsqueeze(0)\n", "\n", "        # 前向传播\n", "        policy_logits, value = self.net(state_tensor)\n", "\n", "        # 计算损失\n", "        value_loss = nn.functional.mse_loss(value, winner)\n", "        policy_loss = -torch.mean(torch.sum(mcts_probs * nn.functional.log_softmax(policy_logits, dim=1), dim=1))\n", "        loss = value_loss + policy_loss\n", "\n", "        # 反向传播和优化\n", "        self.optimizer.zero_grad()\n", "        loss.backward()\n", "        self.optimizer.step()\n", "\n", "        return loss.item()\n", "       \n", "class AlphaZeroCurling(AIRobot):\n", "    def __init__(self, key, name, host, port, shared_model, \n", "                 model_file_name, log_file_name, round_max=10000):\n", "        super().__init__(key, name, host, port)\n", "        self.shared_model = shared_model\n", "        self.mcts = MCTS(self.policy_value_fn)\n", "        self.round_max = round_max\n", "        self.model_file_name = model_file_name\n", "        self.log_file_name = log_file_name\n", "        self.states = []\n", "        self.mcts_probs = []\n", "        self.score = 0\n", "\n", "    def policy_value_fn(self, game_state):\n", "        state_tensor = torch.FloatTensor(get_infostate(game_state.position)[1]).unsqueeze(0)\n", "        with torch.no_grad():\n", "            policy_logits, value = self.shared_model.net(state_tensor)\n", "        policy = nn.functional.softmax(policy_logits, dim=1).squeeze(0).numpy()\n", "        return policy, value.item()\n", "\n", "    def get_action(self, state, temperature=1e-3):\n", "        # 确保 state 是 CurlingGameState 对象\n", "        if not isinstance(state, CurlingGameState):\n", "            raise TypeError(\"state must be a CurlingGameState object\")\n", "\n", "        # 使用 MCTS 获取动作概率\n", "        actions, probs = self.mcts.get_action_probs(state, temperature)\n", "\n", "        # 如果温度接近于0，选择最佳动作\n", "        if temperature < 1e-3:\n", "            best_action = actions[np.argmax(probs)]\n", "        else:\n", "            # 否则，根据概率随机选择动作\n", "            # 创建一个表示动作索引的数组\n", "            action_indices = np.arange(len(actions))\n", "            # 使用概率选择一个动作的索引\n", "            selected_index = np.random.choice(action_indices, p=probs)\n", "            # 从动作列表中获取选定的动作\n", "            best_action = actions[selected_index]\n", "        return best_action\n", "\n", "    def recv_setstate(self, msg_list):\n", "        #当前完成投掷数\n", "        self.shot_num = int(msg_list[0])\n", "        #总对局数\n", "        self.round_total = int(msg_list[2])\n", "\n", "        # 如果达到最大轮数，结束训练\n", "        if self.round_num == self.round_max:\n", "            self.on_line = False\n", "            return\n", "\n", "        # 每局开始时初始化数据存储\n", "        if self.shot_num == 0:\n", "            self.states, self.mcts_probs= [], []\n", "            self.last_score = 0\n", "            # 根据先后手选取模型并设定当前选手第一壶是当局比赛的第几壶\n", "            if self.player_is_init:\n", "                self.first_shot = 0\n", "            else:\n", "                self.first_shot = 1\n", "\n", "        # 创建新的游戏状态\n", "        state = CurlingGameState()\n", "        state.update_state({\n", "            'position': self.position,\n", "            'shot_num': self.shot_num,\n", "            'player_is_init': self.player_is_init,\n", "            'score': self.score\n", "        })\n", "\n", "        # 对每个投掷进行处理\n", "        if self.shot_num < 16:\n", "            # 创建当前游戏状态\n", "            state = CurlingGameState()\n", "            state.position = self.position\n", "            state.shot_num = self.shot_num\n", "            state.player_is_init = self.player_is_init\n", "\n", "            # 设置温度参数\n", "            temperature = 1.0 if self.shot_num < 4 else 1e-3\n", "            # 获取动作概率\n", "            acts, act_probs = self.mcts.get_action_probs(state, temperature)\n", "\n", "            # 存储状态、概率和玩家信息\n", "            self.states.append(state)\n", "            self.mcts_probs.append(act_probs)\n", "\n", "            # 选择动作\n", "            action = self.get_action(state, temperature)\n", "            self.action = action  # 存储动作以供 get_bestshot 方法使用\n", "\n", "        # 一局结束后进行训练\n", "        if self.shot_num == 16:\n", "            for i in range(len(self.states)):\n", "                # 使用共享模型进行训练\n", "                loss = self.shared_model.update(self.states[i], self.mcts_probs[i], self.score)\n", "            \n", "            # 更新轮数和记录日志\n", "            self.round_num += 1\n", "            if loss > 0:\n", "                with open(self.log_file_name, 'a+') as log_file:\n", "                    log_file.write(f\"score {self.score} {self.round_num}\\n\")\n", "                    log_file.write(f\"loss {loss} {self.round_num}\\n\")\n", "\n", "            # 每12轮保存一次模型(自我对弈训练中为了避免重复保存仅先手方保存模型)\n", "            if self.player_is_init and (self.round_num % 12 == 0):\n", "                torch.save(self.shared_model.get_parameters(), self.model_file_name)\n", "                print('============= Checkpoint Saved =============')\n", "\n", "    def get_bestshot(self):\n", "        \"\"\"\n", "        计算并返回最佳的投掷动作。\n", "        这个方法在收到服务器的 \"GO\" 指令时被调用。\n", "        :return: 格式化的 BESTSHOT 消息\n", "        \"\"\"\n", "        # 创建当前游戏状态\n", "        state = CurlingGameState()\n", "        state.position = self.position\n", "        state.shot_num = self.shot_num\n", "        state.player_is_init = self.player_is_init\n", "        state.score = self.score\n", "\n", "        # 使用 MCTS 获取动作概率\n", "        actions, probs = self.mcts.get_action_probs(state, temperature=1e-3)\n", "\n", "        # 选择概率最大的动作\n", "        best_action = actions[np.argmax(probs)]\n", "\n", "        # 将最佳动作转换为 BESTSHOT 消息格式\n", "        return f\"BESTSHOT {best_action[0]} {best_action[1]} {best_action[2]}\"\n", "        \n", "#连接密钥：参照数字冰壶服务器界面中给出的连接信息填写，注意这个参数每次新启动服务器都会改变。\n", "key = \"lidandan_44d1365b-f7b7-4536-9457-c8892c72d5d7\"\n", "\n", "#创建一个函数来启动单个 AI 进程：\n", "def run_ai(key, name, host, port, shared_model, model_file_name, log_file_name):\n", "    ai = AlphaZeroCurling(key, name, host, port, shared_model, model_file_name, log_file_name)\n", "    ai.recv_forever()\n", "\n", "#修改主函数以启动两个 AI 进程：\n", "if __name__ == \"__main__\":\n", "    shared_model = SharedModel()\n", "\n", "    # 如果存在已保存的模型，则加载它\n", "    model_file_name = 'model/MCTS_model.pth'\n", "    if os.path.exists(model_file_name):\n", "        shared_model.set_parameters(torch.load(model_file_name))\n", "        print(f\"Model file {model_file_name} loaded!\")\n", "    # 日志文件\n", "    log_file_name = 'log/MCTS_' + time.strftime(\"%y%m%d_%H%M%S\") + '.log'\n", "\n", "    # 创建两个进程，分别代表两个 AI\n", "    p1 = mp.Process(target=run_ai, \n", "                    args=(key, \"MCTSRobot1\", \"127.0.0.1\", 7788, shared_model, model_file_name, log_file_name))\n", "    time.sleep(1)\n", "    p2 = mp.Process(target=run_ai, \n", "                    args=(key, \"MCTSRobot2\", \"127.0.0.1\", 7788, shared_model, model_file_name, log_file_name))\n", "\n", "    # 启动进程\n", "    p1.start()\n", "    p2.start()\n", "\n", "    # 等待进程结束\n", "    p1.join()\n", "    p2.join() "], "id": "12f823878c11e03f"}, {"cell_type": "markdown", "id": "fe9cf947", "metadata": {}, "source": ["自我对弈训练MCTS模型的过程中已经用到了共享经验库，所以只要在本地PC部署多个单机版数字冰壶服务器，同时运行多个如本节课程给出的自我对弈范例，即可实现并行MCTS模型训练。具体操作可以参考《08.并行DQN以及并行PPO》。"]}, {"cell_type": "markdown", "id": "b3fe02cc-c6fb-4ca8-b793-f968810858d5", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["## 小结\n", "\n", "本课介绍了蒙特卡洛树搜索算法的原理及主要步骤，重点讲解了引入策略价值网络的蒙特卡洛搜索树算法，并给出了如何在数字冰壶比赛中应用蒙特卡洛搜索树算法训练/部署模型的范例代码。进一步还介绍了通过自我对弈训练蒙特卡洛树模型的方法，并给出了范例代码。\n", "\n", "蒙特卡洛树搜索算法的主要优点是它可以在没有先验知识的情况下，有效地搜索大规模的决策空间，过不断模拟对局来估计每个动作的价值，从而选择最优的动作。它在许多博弈问题中表现出色，并且可以灵活地适应不同的问题和策略，广泛应用于复杂的游戏和规划问题中。希望经由大家的耐心调优与长期训练，这个算法也能在数字冰壶比赛中取得优异的对战成绩。"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.14"}}, "nbformat": 4, "nbformat_minor": 5}