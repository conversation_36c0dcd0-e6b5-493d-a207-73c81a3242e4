{"cells": [{"cell_type": "markdown", "id": "c18fe3fa-9723-4076-9a59-9924aa90fb5d", "metadata": {}, "source": ["# 第十课 数字冰壶比赛之等你来战\n", "\n", "经过前面九课的学习，我们已经可以编写自己的AI选手了。不管是基于策略逻辑，还是基于强化学习模型，这个AI选手都可以和课程平台启动的数字冰壶服务器按照约定协议进行通讯、处理接收到的消息、基于策略逻辑或者模型推理生成投壶命令，最终和对手完成一场数字冰壶比赛。\n", "\n", "这些编写好的AI选手，还需要再做一些后期的工作，才能在官网进行提交。"]}, {"cell_type": "markdown", "id": "12d25b46-87f5-4f1a-af04-1b73063d5e4c", "metadata": {}, "source": ["## 10.1 为AI选手提供参数调用支持\n", "\n", "在第二课中讲到，课程平台启动的数字冰壶服务器主机固定为“curling-server-7788.jupyterhub.svc.cluster.local”，端口固定为7788。但在实际的对战过程中，为了同时进行多场比赛，就需要同时启动多个服务器，每个服务器有着不同的主机或者不同的端口。因此提交到官网的AI选手需要支持带参数调用。\n", "\n", "对于具体参数官网规定，<b>服务器主机通过“-H”或者“--host”传入，端口通过“-p”或者“--port”传入。</b>\n", "\n", "下面以AIRobot.py为例，说明如何为AI选手提供参数调用支持。\n", "\n", "首先，代码中AIRobot类的初始化函数要支持host和port参数。如下所示范例代码中导入了argparse模块，这是Python内置的用于命令项选项与参数解析的模块。还实现了从传入的参数中解析出服务器主机和端口，并且在初始化myRobot类的时候传递这些参数。\n", "\n", "> <b>注意参赛的脚本一定要在初始化AI选手的代码中将name设定为参赛团队名称。</b>"]}, {"cell_type": "raw", "id": "78f1528d-95ad-41f6-8764-7ddb147d5c95", "metadata": {}, "source": ["#范例代码的一部分，无法单独运行，因此请【不要】尝试运行本单元。\n", "\n", "import argparse\n", "\n", "#连接密钥：参照数字冰壶服务器界面中给出的连接信息填写，注意这个参数每次新启动服务器都会改变。\n", "key = \"lidandan_e7be356b-24d6-47e0-9659-7fceee8c8112\"\n", "#服务器主机：参照数字冰壶服务器界面中给出的连接信息填写，通常无需修改。\n", "host = 'curling-server-7788.jupyterhub.svc.cluster.local'\n", "#连接端口：参照数字冰壶服务器界面中给出的连接信息填写，通常无需修改。\n", "port = 7788\n", "\n", "#从调用参数中读取主机地址和端口，默认值为host和port变量当前的值。\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument('-H','--host', help='host', default=host, required=False)\n", "parser.add_argument('-p','--port', help='tcp server port', default=str(port), required=False)\n", "args, _ = parser.parse_known_args()\n", "\n", "#……中间的功能代码……\n", "\n", "if __name__ == '__main__':\n", "    #初始化AIRobot对象\n", "    airobot = AIRobot(key, name=\"CurlingAI\", host=args.host, port=int(args.port))\n", "    #AIRobot对象开始接收并处理消息\n", "    airobot.recv_forever()"]}, {"cell_type": "markdown", "id": "bc92dc1b-e001-40f8-a38d-99bc73ef18d8", "metadata": {}, "source": ["请参考以上范例对在课程平台中编写的AI选手进行修改，提供参数调用支持。"]}, {"cell_type": "markdown", "id": "1442cbac-9e33-49d7-8654-76912998e87d", "metadata": {}, "source": ["## 10.2 将AI选手全部相关代码拷贝到课程平台根目录\n", "\n", "在当前课程页面左侧的文件浏览器中，可以看到课程文件是在课程平台的readme目录下，如果想要提交AI选手，就需要<b>将该AI选手所有相关文件都拷贝到课程平台根目录</b>。\n", "\n", "比如说如果想要提交AIRobot.py这个AI选手，就需要在文件浏览器里右键点击该文件选择“Copy”，再在文件浏览器中切换到根目录，右键点击空白处选择“Paste”，即可将AIRobot.py这个文件拷贝到课程平台根目录下。如下图所示。\n", "\n", "<center><img src=\"img/CopyPaste.png\"></center>\n", "\n", "但如果想提交的是还有其他文件调用关系的AI选手，比如说需要载入模型的强化学习AI选手，就需要将调用的模型文件和日志文件等都一起拷贝到课程平台根目录下。\n", "\n", "> 注意如果模型文件或日志文件和AI选手主文件的路径关系有变化，还要在主文件代码中更改相关的文件路径。"]}, {"cell_type": "markdown", "id": "589f3f7d-6425-4d07-bb44-283bda8865a5", "metadata": {}, "source": ["## 10.3 在官网个人中心提交/上传AI选手\n", "\n", "当且仅当AI选手全部相关代码拷贝到课程平台根目录后，在官网【个人中心】页面的【比赛策略】栏目下，点击【提交AI程序】，即可在弹出的窗口中通过下拉框选择AI选手的主文件。如下图所示。\n", "\n", "<center><img src=\"img/CommitAI.png\"></center>\n", "\n", "点击【确定】按钮，即会自动将课程平台根目录下<b>除了readme目录之外</b>的所有文件打包为zip文件并上传到对战服务器，成为对战备选的AI选手。\n", "\n", "> 如果模型较大的话，打包提交时间也会较长，请耐心等待。\n", "\n", "【提交策略】仅支持在课程平台使用Python语言编写的AI选手。对于在其他机器（比如说参赛团队成员的个人电脑）上使用Python语言编写的AI选手，可以上传到课程平台根目录，测试可用后，通过【提交策略】的方式提交。\n", "\n", "> 在个人电脑上使用Python语言编写AI选手时，需要注意对战平台和课程平台仅支持torch、numpy和matplotlib这几个第三方运行库。\n", "\n", "<b>注意：官网的对战平台从今年起仅支持Python脚本的提交，不再支持C++语言编写的可执行文件的上传，望周知。</b>"]}, {"cell_type": "markdown", "id": "7795fb60-d3f6-429d-9980-b8e57b30cf8f", "metadata": {}, "source": ["## 10.4 在官网个人中心发起对战\n", "\n", "官网【个人中心】的【比赛策略】栏目下最多可以提交/上传两个AI选手，首次提交/上传的AI选手默认就是参赛选手，后上传的AI选手可以通过点击【设为参赛程序】设定为参赛选手。如下图所示。\n", "\n", "<center><img src=\"img/IvsI.png\"></center>\n", "\n", "提交/上传AI选手后，如上图所示，在模型表格中点击【发起对战】就会预约一场自家两个AI选手的内部比赛。如果只上传了一个AI选手，点击【发起对战】预约的比赛就是该选手的“左右互博”。\n", "\n", "在【团队列表】栏目下，还可以点击【申请对战】邀请其他团队进行比赛。\n", "\n", "<center><img src=\"img/IvsO.png\"></center>\n", "\n", "邀请其他团队比赛后，发出邀请的团队可以在【对战列表】栏目的【挑战列表】中看到该邀请信息。而被邀请的团队也可以在【对战列表】栏目的【应邀列表】中看到邀请信息，选择接受或者拒绝。接受邀请就会为双方的参赛选手预约一场比赛。\n", "\n", "在【对战记录】栏目，可以看到已预约比赛的进行情况。对于失败的比赛，可以点击【更多信息】查看失败原因，点击【查看日志】查看比赛过程中程序输出的文本。对于已完成的比赛，还可以点击【对战录像】查看比赛过程的回放录像。如下图所示。\n", "\n", "<center><img src=\"img/BattleRecord.png\"></center>\n", "\n", "类似地，在模拟赛、小组赛、半决赛和决赛之后，也可以在【赛程列表】栏目中点击【查看日志】查看比赛过程中程序输出的文本，点击【对战录像】查看比赛过程的回放录像。\n", "\n", "<center><img src=\"img/ScheduleList.png\"></center>"]}, {"cell_type": "markdown", "id": "90a8630f-fd85-4add-8c84-ad9db3c0ad4b", "metadata": {}, "source": ["## 小结\n", "\n", "本课介绍了如何为AI选手提供参数调用支持、在官网个人中心提交/上传AI选手以及如何发起对战，帮助参赛团队们完成了参加数字冰壶比赛的最后一步。\n", "\n", "这个教程陆陆续续写了两个月，今天终于写到最后一课的小结。而在看这篇课程的你，可能也从一个并不了解数字冰壶比赛的入门者，成长为了一个可以用自己的智慧与谋略指挥AI选手作战的指挥官。希望在课程学习过程能让大家有所收获，就是笔者做为一名老师最欣慰的事情啦！\n", "\n", "纸上学来终觉浅，须知此事要躬行。要想在比赛中获得更好的成绩，大家还是要多努力呀！加油~~~~\n", "\n", "｡:.ﾟヽ(｡◕‿◕｡)ﾉﾟ.:｡+ﾟ"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.14"}}, "nbformat": 4, "nbformat_minor": 5}