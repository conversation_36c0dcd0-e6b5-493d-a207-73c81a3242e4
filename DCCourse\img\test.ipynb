{"cells": [{"cell_type": "code", "execution_count": null, "id": "020d535a-5bd8-40dc-abc9-6ee6fe77b77b", "metadata": {}, "outputs": [], "source": ["import socket\n", "import time\n", "\n", "#服务器IP\n", "host = '***************'\n", "#连接端口\n", "port = 7788\n", "#根据数字冰壶服务器界面中给出的连接信息修改CONNECTKEY，注意这个数据每次启动都会改变。\n", "key = \"olweshkgdt_d2e03453-3ff7-496a-bd50-14925f184fad\"\n", "\n", "#新建Socket对象\n", "ai_sock =socket.socket()\n", "#创建Socket连接\n", "ai_sock.connect((host,port))\n", "print(\"已建立socket连接\", host, port)\n", "\n", "#通过socket对象发送消息\n", "def send_msg(sock, msg):\n", "    print(\"  >>>> \" + msg)\n", "    #将消息数据从字符串类型转换为bytes类型后发送\n", "    sock.send(msg.strip().encode())\n", "\n", "#通过socket对象接收消息并进行解析\n", "def recv_msg(sock):\n", "    #为避免TCP粘包问题，数字冰壶服务器发送给AI选手的每一条信息均以0（数值为0的字节）结尾\n", "    #这里采用了逐个字节接收后拼接的方式处理信息，多条信息之间以0为信息终结符\n", "    buffer = bytearray()\n", "    while True:\n", "        #接收1个字节\n", "        data = sock.recv(1)\n", "        #接收到空数据或者信息处终结符(0)即中断循环\n", "        if not data or data == b'\\0':\n", "            time.sleep(0.1)\n", "            break\n", "        #将当前字节拼接到缓存中\n", "        buffer.extend(data)\n", "    #将消息数据从bytes类型转换为字符串类型后去除前后空格\n", "    msg_str = buffer.decode().strip()\n", "    print(\"<<<< \" + msg_str)\n", "\n", "    #用空格将消息字符串分隔为列表\n", "    msg_list = msg_str.split(\" \")\n", "    #列表中第一个项为消息代码\n", "    msg_code = msg_list[0]\n", "    #列表中后续的项为各个参数\n", "    msg_list.pop(0)\n", "    #返回消息代码和消息参数列表\n", "    return msg_code, msg_list\n", "\n", "#通过socket对象发送连接密钥\n", "send_msg(ai_sock, \"CONNECTKEY:\" + key)\n", "#初始化先手壶和后手壶的坐标列表\n", "init_x, init_y, gote_x, gote_y = [0]*8, [0]*8, [0]*8, [0]*8\n", "\n", "while(1):\n", "    #接收消息并解析\n", "    msg_code, msg_list = recv_msg(ai_sock)\n", "    #如果消息代码是\"ISREADY\"\n", "    if msg_code == \"ISREADY\":\n", "        #发送\"READYOK\"\n", "        send_msg(ai_sock, \"READYOK\")\n", "        #发送实验模式（正式比赛时设定为0）\n", "        send_msg(ai_sock, \"EXPMODE 2\")\n", "        time.sleep(0.5)\n", "        #发送\"NAME\"和AI选手名\n", "        send_msg(ai_sock, \"NAME JupyterAI\")\n", "        break\n", "\n", "#########################请在下方补全代码实现指定功能######################\n", "\n", "P = [[2.375, 4.111],[2.375, 4.88],[1.606, 4.88],[3.144, 4.88],\n", "     [2.375, 5.649],[1.1552, 6.0998],[3.5948, 6.0998],[2.375, 6.605]]\n", "sum_diff_x = 0\n", "sum_diff_y = 0\n", "\n", "for m in range(8):\n", "    while(1):\n", "        #接收消息并解析\n", "        msg_code, msg_list = recv_msg(ai_sock)\n", "        #如果消息代码是\"GO\"\n", "        if msg_code == \"GO\":\n", "            print(\"============先手方第\" + str(m+1) + \"壶============\")\n", "            v0 = 3.6 - 0.123*P[m][1]\n", "            h0 = P[m][0] - 2.375\n", "            #发送投壶消息：初速度为v0；横向偏移为h0，初始角速度为0\n", "            send_msg(ai_sock, \"BESTSHOT \" + str(v0) + \" \" + str(h0) + \" 0\")\n", "        if msg_code==\"POSITION\":\n", "            \n", "            for n in range(8):\n", "                init_x[n], init_y[n] = float(msg_list[n*4]), float(msg_list[n*4+1])\n", "                gote_x[n], gote_y[n] = float(msg_list[n*4+2]), float(msg_list[n*4+3])\n", "            if (init_x[m]>0.0001) and (init_y[m]>0.0001):\n", "                diff_x = init_x[m]-P[m][0]\n", "                diff_y = init_y[m]-P[m][1]\n", "                print(\"第\" + str(m+1) + \"壶相比预设坐标偏差为(%.4f, %.4f)\" % (diff_x, diff_y))\n", "                sum_diff_x = sum_diff_x + diff_x\n", "                sum_diff_y = sum_diff_y + diff_y\n", "                break\n", "\n", "print(\"平均坐标偏差为(%.4f, %.4f)\" % (sum_diff_x/8.0, sum_diff_y/8.0))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.14"}}, "nbformat": 4, "nbformat_minor": 5}