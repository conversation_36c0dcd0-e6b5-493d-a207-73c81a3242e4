import socket
import time
import random
import math
import argparse
from util import *

center_x = 2.375  # 大本营中心位置坐标
center_y = 4.88
House_R = 1.830  # 大本营半径
Stone_R = 0.145  # 冰壶半径
s_R = 0.61

def pudian(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point):
    x = -0.2
    if not find_obs(point_list, x + circle_x, 12, x + circle_x, 6, 2, 2):
        return list_to_str([2.95, x, 0.05])
def shot_enemy(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point):
    if enemy_list:
        for enemy in enemy_list:
            if get_distance_circle(enemy[0], enemy[1]) < 1.83 + stone_r - s_R:
                feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                if feasibility:
                    shot = list_to_str([math.sqrt(2.65*enemy[1])-0.12, enemy[0] - 2.375, 0])
                    return shot
    return None

def center_line(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point):
    x_list = []
    for i in (0.1, -0.1, 0.2, -0.2, 0.3, -0.3, 0.4, -0.4, 0.5, -0.5, 0.6, -0.6):
        x_list.append(center_x + i)
    for x_crash in x_list:
        feasibility, obs_list = check_path_straight(point_list, x_crash, 4.88)
        if feasibility:
            print('three/four区域，直打至中心')
            shot = list_to_str([2.95, x_crash - center_x, 0])
            print(shot)
            return shot
    return None
def empty_center(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point):
    print('如果大本营中无球，处理')
    print('再考虑内碰')
    safe_fit_enemy, danger_fit_enemy = crash_others(point_list, point_list)
    if safe_fit_enemy:
        return list_to_str([safe_fit_enemy[0][2], safe_fit_enemy[0][1], safe_fit_enemy[0][3]])
    if danger_fit_enemy:
        return list_to_str([danger_fit_enemy[0][2], danger_fit_enemy[0][1], danger_fit_enemy[0][3]])
    print('考虑外碰')
    safe_fit_ally, danger_fit_ally = crash_us(point_list, ally_list)
    if danger_fit_ally:
        return list_to_str([danger_fit_ally[0][2], danger_fit_ally[0][1], danger_fit_ally[0][3]])
    print('优先考虑插空')
    interval_point = find_interval(point_list, ally_list, enemy_list)
    if interval_point:
        extra = 0
        for enemy_temp in enemy_list:
            if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(enemy_temp) == 'top-red':
                if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                    extra = 1
                else:
                    extra = 0
        return list_to_str([3 + extra, interval_point[0] - 2.375, 0])
    else:
        return list_to_str([3, 0, 0])

def advance_enemy(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point):
    print('enemy 领先', advantage_score)
    for enemy in enemy_list:
        shot = None
        if enemy == enemy_list[0]:
            print('这是对最靠近中心的球的处理，坐标为', enemy)
            color_type = analyse_by_color(enemy)
            print(color_type)
            if color_type in ['top-red', 'bottom-red']:
                our_in_red = our_curling_in_red(ally_list)
                if not our_in_red:#红区没有我们的球
                    count = count_enemy_in_red(enemy_list)
                    #敌方红区只有一个球
                    if count == 1:
                        print('中心只有一个球， 优先找双碰球')
                        double = double_kill(point_list, enemy_list, enemy)
                        if double:
                            shot = list_to_str([double[0][2], double[0][1], double[0][3]])
                            if shot:
                                return shot
                            
                        print('传击应对')
                        sorted_fit_shot = double_shot_width(point_list, enemy_list, enemy)
                        if sorted_fit_shot:
                            print('sorted_fit_shot', sorted_fit_shot)
                            return list_to_str(
                                [sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]])
                        
                        print('失败，直打')
                        feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                        if feasibility:
                            print('直打')
                            shot = list_to_str([8, enemy[0] - 2.375, 0])
                            if shot:
                                return shot
                        
                        print('失败，外碰')
                        ally_list_sort = sorted(ally_list, key=lambda ally_sort: abs(
                            cal_angle(ally_sort[0], ally_sort[1], enemy[0], enemy[1])))
                        for point_crashed in ally_list_sort:
                            type4 = analyse_point(point_crashed)
                            if type4 in ['three', 'four', 'five', 'six']:
                                if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                                 enemy[1])) < 55 and 0.545 < point_crashed[0] < 4.205:
                                    left_or_right = 1 if point_crashed[0] > 2.375 else -1
                                    shot_offset, v, w = detect_path(point_list, point_crashed,
                                                                    (enemy[0], enemy[1]), 'out',
                                                                    left_or_right)
                                    if shot_offset:
                                        print('外碰执行')
                                        print('target point:', point_crashed)
                                        return list_to_str([v + 1, shot_offset, w])

                        print('失败，外撞')
                        shot_offset, v, w = hit_enemy_point(point_list, enemy)
                        if shot_offset:
                            return list_to_str([v, shot_offset, w])
                        
                        print('失败， 插空')
                        interval_point = find_interval(point_list, ally_list, enemy_list)
                        if interval_point:
                            extra = 0
                            for enemy_temp in enemy_list:
                                if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                                        enemy_temp) == 'top-red':
                                    if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                                        extra = 1
                                    else:
                                        extra = 0
                            return list_to_str([3 + extra, interval_point[0] - 2.375, 0])
                    #敌方红区只有两个球    
                    elif count == 2:
                        enemy_dis = get_distance(enemy_list[0][0], enemy_list[0][1], enemy_list[1][0],
                                                 enemy_list[1][1])
                        if enemy_dis < 4 * stone_r:
                            near, center_coord = near_analyse(enemy_list[0], enemy_list[1])
                            y_coord = max(enemy_list[0][1], enemy_list[1][1])
                            if near in ['even', 'slant']:
                                print('两个球挨得很近')
                                feasibility, obs_list = check_path_straight(point_list, center_coord,
                                                                            y_coord + stone_r)
                                if feasibility:
                                    print('两个球挨得很近, 打中间')
                                    shot = list_to_str([8, enemy[0] - 2.375, 0])
                                    if shot:
                                        return shot
                        else:
                            print('两个球的居里比较远')
                            far = far_analyse(enemy_list[0], enemy_list[1])
                            double_enemy = [enemy_list[0], enemy_list[1]]
                            sorted_double_enemy = sorted(double_enemy, key=lambda message: abs(message[1]))
                            if far == 'vertical':
                                print('vertical情况')
                                print('能不能用对面的球把对面打出去')
                                sorted_fit_shot = double_shot(point_list, enemy_list, enemy)
                                if sorted_fit_shot:
                                    print('sorted_fit_shot', sorted_fit_shot)
                                    return list_to_str([sorted_fit_shot[0][2], sorted_fit_shot[0][1],
                                                        sorted_fit_shot[0][3]])
                                print('外双碰检测')
                                shot_offset, v, w = detect_path(point_list, sorted_double_enemy[1],
                                                                sorted_double_enemy[0], 'out',
                                                                1, True)
                                if shot_offset:
                                    print('外双碰执行')
                                    print('target point:',
                                          (sorted_double_enemy[0], sorted_double_enemy[1]))
                                    return list_to_str([v + 2, shot_offset, w])
                                else:
                                    pass
                            if far == 'slant':
                                print('slant情况,检测双碰')
                                double = double_kill(point_list, [sorted_double_enemy[1]],
                                                     sorted_double_enemy[0])
                                if double:
                                    shot = list_to_str([double[0][2], double[0][1], double[0][3]])
                                    if shot:
                                        return shot
                            else:
                                print('even情况,处理最靠近中心的重复一个球')
                                print('优先找双碰球')
                                double = double_kill(point_list, enemy_list, enemy)
                                if double:
                                    shot = list_to_str([double[0][2], double[0][1], double[0][3]])
                                else:
                                    pass
                                if shot:
                                    return shot
                                print('双碰失败，直碰')
                                feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                                if feasibility:
                                    print('直打')
                                    shot = list_to_str([8, enemy[0] - 2.375, 0])
                                if shot:
                                    return shot
                                print('直打不行，外碰')
                                ally_list_sort = sorted(ally_list, key=lambda ally_sort: abs(
                                    cal_angle(ally_sort[0], ally_sort[1], enemy[0], enemy[1])))
                                for point_crashed in ally_list_sort:
                                    type4 = analyse_point(point_crashed)
                                    if type4 in ['three', 'four', 'five', 'six']:
                                        if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                                         enemy[1])) < 55 and 0.545 < point_crashed[
                                            0] < 4.205:
                                            left_or_right = 1 if point_crashed[0] > 2.375 else -1
                                            shot_offset, v, w = detect_path(point_list, point_crashed,
                                                                            (enemy[0], enemy[1]),
                                                                            'out',
                                                                            left_or_right)
                                            if shot_offset:
                                                print('外碰执行')
                                                print('target point:', point_crashed)
                                                return list_to_str([v + 1, shot_offset, w])
                                print('外碰不行，外撞')
                                shot_offset, v, w = hit_enemy_point(point_list, enemy)
                                if shot_offset:
                                    shot = list_to_str([v, shot_offset, w])
                                if shot:
                                    return shot
                                print('再处理次靠近中心的')
                                print('优先找双碰球')
                                double = double_kill(point_list, enemy_list, enemy_list[1])
                                if double:
                                    shot = list_to_str([double[0][2], double[0][1], double[0][3]])
                                    if shot:
                                        return shot
                                print('双碰失败，直碰')
                                feasibility, obs_list = check_path_straight(point_list, enemy_list[1][0],
                                                                            enemy_list[1][1])
                                if feasibility:
                                    print('直打')
                                    shot = list_to_str([8, enemy_list[1][0] - 2.375, 0])
                                if shot:
                                    return shot
                                print('直打不行，外碰')
                                ally_list_sort = sorted(ally_list, key=lambda ally_sort: abs(
                                    cal_angle(ally_sort[0], ally_sort[1],
                                              enemy_list[1][0],
                                              enemy_list[1][1])))
                                for point_crashed in ally_list_sort:
                                    type4 = analyse_point(point_crashed)
                                    if type4 in ['three', 'four', 'five', 'six']:
                                        if abs(cal_angle(point_crashed[0], point_crashed[1],
                                                         enemy_list[1][0],
                                                         enemy_list[1][1])) < 55 and 0.545 < point_crashed[
                                            0] < 4.205:
                                            left_or_right = 1 if point_crashed[0] > 2.375 else -1
                                            shot_offset, v, w = detect_path(point_list, point_crashed,
                                                                            (enemy_list[1][0],
                                                                             enemy_list[1][1]),
                                                                            'out',
                                                                            left_or_right)
                                            if shot_offset:
                                                print('外碰执行')
                                                print('target point:', point_crashed)
                                                return list_to_str([v + 1, shot_offset, w])

                                print('外碰不行，外撞')
                                shot_offset, v, w = hit_enemy_point(point_list, enemy_list[1])
                                if shot_offset:
                                    shot = list_to_str([v, shot_offset, w])
                                if shot:
                                    return shot
                                print('外撞不行， 插空')
                                interval_point = find_interval(point_list, ally_list, enemy_list)
                                if interval_point:
                                    extra = 0
                                    for enemy_temp in enemy_list:
                                        if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                                                enemy_temp) == 'top-red':
                                            if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                                                extra = 1
                                            else:
                                                extra = 0
                                    return list_to_str([3 + extra, interval_point[0] - 2.375, 0])
                else:
                    print('中间有我们的球')
                    print('传击应对')
                    sorted_fit_shot = double_shot_width(point_list, enemy_list, enemy)
                    if sorted_fit_shot:
                        print('sorted_fit_shot', sorted_fit_shot)
                        return list_to_str(
                            [sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]])
                    print('打最靠近中新的球， 优先找双碰球')
                    double = double_kill(point_list, enemy_list, enemy)
                    if double:
                        shot = list_to_str([double[0][2], double[0][1], double[0][3]])
                    else:
                        pass
                    if shot:
                        return shot
                    print('双碰失败，直碰')
                    feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                    if feasibility:
                        print('直打')
                        shot = list_to_str([8, enemy[0] - 2.375, 0])
                    if shot:
                        return shot
                    print('内碰打对面')
                    for enemy_crashed in enemy_list:
                        if enemy_crashed == enemy:
                            continue
                        type_enemy = analyse_point(enemy_crashed)
                        if type_enemy in ['three', 'four', 'five', 'six']:
                            angle = cal_angle(enemy_crashed[0], enemy_crashed[1], enemy[0], enemy[1])
                            if 10 < abs(angle) < 70:
                                print('区间合适，判断是否可行1')
                                left_or_right = 1 if enemy_crashed[0] > enemy[0] else -1
                                shot_offset, v, w = detect_path(point_list, enemy_crashed, enemy,
                                                                'in',
                                                                left_or_right)
                                if shot_offset:
                                    return list_to_str([v + 3, shot_offset, w])
                                else:
                                    continue
                            else:
                                continue
                        else:
                            pass
                    print('直打不行，外碰')
                    ally_list_sort = sorted(ally_list, key=lambda ally_sort: abs(
                        cal_angle(ally_sort[0], ally_sort[1], enemy[0], enemy[1])))
                    for point_crashed in ally_list_sort:
                        type4 = analyse_point(point_crashed)
                        if type4 in ['three', 'four', 'five', 'six']:
                            if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                             enemy[1])) < 55 and 0.565 < point_crashed[0] < 4.185:
                                left_or_right = 1 if point_crashed[0] > 2.375 else -1
                                shot_offset, v, w = detect_path(point_list, point_crashed,
                                                                (enemy[0], enemy[1]), 'out',
                                                                left_or_right)
                                if shot_offset:
                                    print('外碰执行')
                                    print('target point:', point_crashed)
                                    return list_to_str([v + 3, shot_offset, w])
                                else:
                                    pass
                            else:
                                continue
                        else:
                            continue
                    print('外碰不行，外撞')
                    shot_offset, v, w = hit_enemy_point(point_list, enemy)
                    if shot_offset:
                        shot = list_to_str([v, shot_offset, w])
                    if shot:
                        return shot
            if color_type in ['top-white']:
                print('能不能用对面的球把对面打出去')
                sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
                if sorted_fit_shot:
                    print('sorted_fit_shot', sorted_fit_shot)
                    return list_to_str(
                        [sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]])
                else:
                    pass
                print('上白区，优先直打')
                feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                if feasibility:
                    shot = list_to_str([8, enemy[0] - 2.375, 0])
                    return shot
                print('上白区直达不行，看看双碰')
                double = double_kill(point_list, enemy_list, enemy)
                if double:
                    shot = list_to_str([double[0][2], double[0][1], double[0][3]])
                else:
                    pass
                if shot:
                    return shot
                print('上白区双碰不行看看外碰')
                for point_crashed in ally_list:
                    type4 = analyse_point(point_crashed)
                    if type4 in ['three', 'four', 'five', 'six']:
                        if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                         enemy[1])) < 55 and 0.545 < point_crashed[0] < 4.205:
                            left_or_right = 1 if point_crashed[0] > 2.375 else -1
                            shot_offset, v, w = detect_path(point_list, point_crashed,
                                                            (enemy[0], enemy[1]), 'out',
                                                            left_or_right)
                            if shot_offset:
                                print('外碰执行')
                                print('target point:', point_crashed)
                                return list_to_str([v + 1, shot_offset, w])
                            else:
                                pass
                        else:
                            continue
                    else:
                        continue
                print('上白区外碰不行，外撞')
                shot_offset, v, w = hit_enemy_point(point_list, enemy)
                if shot_offset:
                    shot = list_to_str([v, shot_offset, w])
                if shot:
                    return shot
                print('上白区外撞不行， 插空')
                interval_point = find_interval(point_list, ally_list, enemy_list)
                if interval_point:
                    extra = 0
                    for enemy_temp in enemy_list:
                        if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                                enemy_temp) == 'top-red':
                            if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                                extra = 1
                            else:
                                extra = 0
                    return list_to_str([3 + extra, interval_point[0] - 2.375, 0])
                else:
                    pass
            if color_type in ['bottom-white']:
                print('下白区优先看能不能内碰')
                type_enemy = analyse_point(enemy)
                if type_enemy == 'three' or type_enemy == 'four':
                    for point_target1 in point_center:
                        angle = cal_angle(enemy[0], enemy[1], point_target1[0], point_target1[1])
                        if 10 < abs(angle) < 70:
                            print('区间合适，判断是否可行1')
                            left_or_right = 1 if enemy[0] > point_target1[0] else -1
                            shot_offset, v, w = detect_path(point_list, enemy, point_target1,
                                                            'in',
                                                            left_or_right)
                            if shot_offset:
                                return list_to_str([v, shot_offset, w])
                            else:
                                continue
                        else:
                            continue
                else:
                    pass
                print('能不能用对面的球把对面打出去')
                sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
                if sorted_fit_shot:
                    print('sorted_fit_shot', sorted_fit_shot)
                    return list_to_str(
                        [sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]])
                else:
                    pass
                print('下白区内碰不行看看能不能直碰出去')
                feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                if feasibility:
                    shot = list_to_str([8, enemy[0] - 2.375, 0])
                    return shot
                print('能不能外碰')
                for point_crashed in ally_list:
                    type4 = analyse_point(point_crashed)
                    if type4 in ['three', 'four', 'five', 'six']:
                        if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                         enemy[1])) < 55 and 0.545 < point_crashed[0] < 4.205:
                            left_or_right = 1 if point_crashed[0] > 2.375 else -1
                            shot_offset, v, w = detect_path(point_list, point_crashed,
                                                            (enemy[0], enemy[1]), 'out',
                                                            left_or_right)
                            if shot_offset:
                                print('外碰执行')
                                print('target point:', point_crashed)
                                return list_to_str([v + 1, shot_offset, w])
                            else:
                                pass
                        else:
                            continue
                    else:
                        continue
            if color_type in ['top-blue']:
                print('上蓝， 优先双碰')
                double = double_kill(point_list, enemy_list, enemy)
                if double:
                    shot = list_to_str([double[0][2], double[0][1], double[0][3]])
                else:
                    pass
                if shot:
                    return shot
                print('上蓝， 双碰不行， 直达')
                feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                if feasibility:
                    shot = list_to_str([8, enemy[0] - 2.375, 0])
                    return shot
            if color_type in ['bottom-blue']:
                print('下蓝， 优先内碰')
                type_enemy = analyse_point(enemy)
                if type_enemy in ['three', 'four', 'five', 'six']:
                    for point_target1 in point_center:
                        angle = cal_angle(enemy[0], enemy[1], point_target1[0], point_target1[1])
                        if 10 < abs(angle) < 70:
                            print('区间合适，判断是否可行1')
                            left_or_right = 1 if enemy[0] > point_target1[0] else -1
                            shot_offset, v, w = detect_path(point_list, enemy, point_target1,
                                                            'in',
                                                            left_or_right)
                            if shot_offset:
                                return list_to_str([v, shot_offset, w])
                            else:
                                continue
                        else:
                            continue
                else:
                    pass
                print('下蓝， 内碰不行， 不管')
            else:
                pass
        if len(enemy_list) == 2:
            if enemy == enemy_list[1]:
                print('次靠近中心的球的处理')
                color_type = analyse_by_color(enemy)
                print(analyse_by_color)
                if color_type in ['top-red', 'bottom-red']:
                    print('红区')
                    print('能不能用对面的球把对面打出去')
                    sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
                    if sorted_fit_shot:
                        print('sorted_fit_shot', sorted_fit_shot)
                        return list_to_str(
                            [sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]])
                    print('双碰')
                    double = double_kill(point_list, enemy_list, enemy)
                    if double:
                        shot = list_to_str([double[0][2], double[0][1], double[0][3]])
                    else:
                        pass
                    if shot:
                        return shot
                    print('直打')
                    feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                    if feasibility:
                        print('直打')
                        shot = list_to_str([8, enemy[0] - 2.375, 0])
                    if shot:
                        return shot
                    print('外碰')
                    for point_crashed in ally_list:
                        type4 = analyse_point(point_crashed)
                        if type4 in ['three', 'four', 'five', 'six']:
                            if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                             enemy[1])) < 55 and 0.545 < point_crashed[0] < 4.205:
                                left_or_right = 1 if point_crashed[0] > 2.375 else -1
                                shot_offset, v, w = detect_path(point_list, point_crashed,
                                                                (enemy[0], enemy[1]), 'out',
                                                                left_or_right)
                                if shot_offset:
                                    print('外碰执行')
                                    print('target point:', point_crashed)
                                    return list_to_str([v + 1, shot_offset, w])
                                else:
                                    pass
                            else:
                                continue
                        else:
                            continue
                    print('外碰不行，外撞')
                    shot_offset, v, w = hit_enemy_point(point_list, enemy)
                    if shot_offset:
                        shot = list_to_str([v, shot_offset, w])
                    if shot:
                        return shot
                if color_type in ['top-white']:
                    print('上白区')
                    print('双碰')
                    double = double_kill(point_list, enemy_list, enemy)
                    if double:
                        shot = list_to_str([double[0][2], double[0][1], double[0][3]])
                    else:
                        pass
                    if shot:
                        return shot
                    print('能不能用对面的球把对面打出去')
                    sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
                    if sorted_fit_shot:
                        print('sorted_fit_shot', sorted_fit_shot)
                        return list_to_str(
                            [sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]])
                    else:
                        pass
                    print('直碰')
                    feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                    if feasibility:
                        print('直打')
                        shot = list_to_str([8, enemy[0] - 2.375, 0])
                    if shot:
                        return shot
                if color_type in ['bottom-white']:
                    print('下白区')
                    print('内碰')
                    type_enemy = analyse_point(enemy)
                    if type_enemy == 'three' or type_enemy == 'four':
                        for point_target1 in point_center:
                            angle = cal_angle(enemy[0], enemy[1], point_target1[0], point_target1[1])
                            if 10 < abs(angle) < 70:
                                print('区间合适，判断是否可行1')
                                left_or_right = 1 if enemy[0] > point_target1[0] else -1
                                shot_offset, v, w = detect_path(point_list, enemy, point_target1,
                                                                'in',
                                                                left_or_right)
                                if shot_offset:
                                    return list_to_str([v, shot_offset, w])
                                else:
                                    continue
                            else:
                                continue
                    else:
                        pass
                    print('能不能用对面的球把对面打出去')
                    sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
                    if sorted_fit_shot:
                        print('sorted_fit_shot', sorted_fit_shot)
                        return list_to_str(
                            [sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]])
                    else:
                        pass
                if color_type in ['top-blue']:
                    print('上蓝区')
                    print('双碰')
                    double = double_kill(point_list, enemy_list, enemy)
                    if double:
                        shot = list_to_str([double[0][2], double[0][1], double[0][3]])
                    else:
                        pass
                    if shot:
                        return shot
                if color_type in ['bottom-blue']:
                    print('下蓝区')
                    print('内碰')
                    type_enemy = analyse_point(enemy)
                    if type_enemy in ['three', 'four', 'five', 'six']:
                        for point_target1 in point_center:
                            angle = cal_angle(enemy[0], enemy[1], point_target1[0], point_target1[1])
                            if 10 < abs(angle) < 70:
                                print('区间合适，判断是否可行1')
                                left_or_right = 1 if enemy[0] > point_target1[0] else -1
                                shot_offset, v, w = detect_path(point_list, enemy, point_target1,
                                                                'in',
                                                                left_or_right)
                                if shot_offset:
                                    return list_to_str([v, shot_offset, w])
                                else:
                                    continue
                            else:
                                continue
            else:
                pass
        else:
            print('其余处理')
            color_type = analyse_by_color(enemy)
            print(analyse_by_color)
            if color_type in ['top-red', 'bottom-red']:
                print('红区')
                print('双碰')
                double = double_kill(point_list, enemy_list, enemy)
                if double:
                    shot = list_to_str([double[0][2], double[0][1], double[0][3]])
                else:
                    pass
                if shot:
                    return shot
                else:
                    return list_to_str([6, enemy_list[0][0] - 2.375, 0])
            else:
                pass
    # 下面是打自己的球的部分
    for ally_point in ally_list:
        print('对敌方球无计可使，看看自家球')
        print('内')
        safe_fit_enemy, danger_fit_enemy = crash_others(point_list, point_list)
        if safe_fit_enemy:
            return list_to_str([safe_fit_enemy[0][2], safe_fit_enemy[0][1], safe_fit_enemy[0][3]])
        if danger_fit_enemy:
            return list_to_str([danger_fit_enemy[0][2], danger_fit_enemy[0][1], danger_fit_enemy[0][3]])
        print('判断是否可以外碰,不理想情况')
        for point_target1 in point_center:
            if abs(cal_angle(ally_point[0], ally_point[1], point_target1[0],
                             point_target1[1])) < 55 and 0.545 < ally_point[0] < 4.205 and \
                    point_target1[1] < ally_point[1]:
                print('区间合适，判断外碰是否可行1')
                if 50 < abs(cal_angle(ally_point[0], ally_point[1], point_target1[0],
                                      point_target1[1])) < 55:
                    extra = 1.5
                else:
                    extra = 0
                left_or_right = 1 if ally_point[0] > 2.375 else -1
                if get_distance(ally_point[0], ally_point[1], 2.375, 4.88) < 0.6:
                    print('已经在中心了不用管')
                    continue
                else:
                    shot_offset, v, w = detect_path(point_list, ally_point,
                                                    point_target1, 'out',
                                                    left_or_right)
                    if shot_offset:
                        print('enemy中的外碰执行')
                        print('target point:', point_target1)
                        return list_to_str([v + extra, shot_offset, w])
                    else:
                        continue
            else:
                pass
    print('看看能不能插空')
    interval_point = find_interval(point_list, ally_list, enemy_list)
    if interval_point:
        extra = 0
        for enemy_temp in enemy_list:
            if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                    enemy_temp) == 'top-red':
                if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                    extra = 1
                else:
                    extra = 0
        return list_to_str([3 + extra, interval_point[0] - 2.375, 0])
    else:
        pass

def advance_ally(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point):
    print('ally 领先', advantage_score)
    if advantage_score > 2:
        print('中线防守')
        shot = defend_center(point_list, ally_list, min_point,enemy_list)
        print(shot)
        return shot
    else:
        print('center')
        print('内')
        safe_fit_enemy, danger_fit_enemy = crash_others(point_list, point_list)
        if safe_fit_enemy:
            return list_to_str([safe_fit_enemy[0][2], safe_fit_enemy[0][1], safe_fit_enemy[0][3]])
        if danger_fit_enemy:
            return list_to_str([danger_fit_enemy[0][2], danger_fit_enemy[0][1], danger_fit_enemy[0][3]])
        print('外')
        safe_fit_ally, danger_fit_ally = crash_us(point_list, ally_list)
        if safe_fit_ally:
            return list_to_str([safe_fit_ally[0][2], safe_fit_ally[0][1], safe_fit_ally[0][3]])
        if danger_fit_ally:
            return list_to_str([danger_fit_ally[0][2], danger_fit_ally[0][1], danger_fit_ally[0][3]])
        print('直')
        interval_point = find_interval(point_list, ally_list, enemy_list)
        if interval_point:
            extra = 0
            for enemy_temp in enemy_list:
                if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                        enemy_temp) == 'top-red':
                    if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                        extra = 1
                    else:
                        extra = 0
            return list_to_str([3 + extra, interval_point[0] - 2.375, 0])
        else:
            pass
        print('看看能不能把对面的撞出去')
        shot_offset, v, w = hit_enemy(point_list, enemy_list)
        if shot_offset:
            return list_to_str([v, shot_offset, w])
        else:
            print('中线放个球把')
            # shot = defense_center_two(point_list, ally_list, enemy_list)
            # if shot:
            #     return shot
            print('优先清场')
            for enemy in enemy_list:
                if get_distance_circle(enemy[0], enemy[1]) < 1.83 + stone_r:
                    feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                    benefit = detect_crash_influence(ally_list, enemy[0], enemy[1])
                    print('feasibility', feasibility)
                    print('benefit', benefit)
                    if feasibility and benefit:
                        print('直打')
                        return list_to_str([8, enemy[0] - 2.375, 0])
                    else:
                        pass
                else:
                    pass
            print('再次防守')
            return defend_center(point_list, ally_list, min_point,enemy_list)