# -*- coding: utf-8 -*-
import socket
import time
import random
import math
import argparse
from util import *
from nomalstrategy import *

# 初始化
center_x = 2.375  # 大本营中心位置坐标
center_y = 4.88
House_R = 1.830  # 大本营半径
Stone_R = 0.145  # 冰壶半径
s_R = 0.61 

# pyinstaller -F CurlingAI.py

def strategy(state_list, shotnum, turn):
    sorted_res = []

    if shotnum == 1:
        state_list = [0] * 32
    print(state_list)
    # ==============
    advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point = organize_position(turn,state_list)

    print('enemy_list:', enemy_list)
    print(shotnum)

    # ==============
    if shotnum < 6:
        if shotnum == 1:#先手一投
            shot = pudian(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
            if shot:
                print('铺垫')
                print(shot)
                return shot

        if shotnum == 2:#后手一投
            # 大本营的白圈内有球就打
            shot = None
            shot = shot_enemy(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
            if shot:
                print('打出对手的球')
                print(shot)
                return shot
            
            x = -1.1
            shot = pudian(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
            if shot:
                print('左侧铺垫')
                print(shot)
                return shot
            
            shot = center_line(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
            if shot:
                return shot
            
            print('随机前五壶')
            return random_push()

        if shotnum == 3:#先手二投
            shot = None
            shot = shot_enemy(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
            if shot:
                print('打出对手的球')
                print(shot)
                return shot
            x = 1.1
            if not find_obs(point_list, x + circle_x, 12, x + circle_x, 6, 2, 2):
                shot = str('BESTSHOT {} {} {}'.format(2.75, x, 0.05))
                print(shot[9:])
                print('右侧铺垫')
                return shot
            if shot is None:
               shot = center_line(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
               if shot:
                   return shot
            if shot is None:
                print('随机前五壶')
                return random_push()
        if shotnum == 4:#后手二投
            # 中线有球就贴
            shot = None
            for enemy in enemy_list:
                if (2.375 - 0.61 < enemy[0] < 2.375 + 0.61) and enemy[1] > 4.88 + 1.83:
                    feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                    v = move(enemy[1]) - 0.15 if move(enemy[1]) - 0.15 > 2.5 else 2.5
                    offset = -stone_r if enemy[0] > 2.375 else stone_r
                    if feasibility:
                        shot = list_to_str([v, enemy[0] + offset - 2.375, 0])
                        if shot:
                            print('贴近对手的中线球')
                            print(shot)
                            return shot
                        
            # 大本营有球就打
            shot = shot_enemy(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
            if shot:
                print('打出对手的球')
                print(shot)
                return shot
            x = 1.1
            if not find_obs(point_list, x + circle_x, 12, x + circle_x, 6, 2, 2):
                shot = str('BESTSHOT {} {} {}'.format(2.75, x, 0.05))
                print(shot[9:])
                print('右侧铺垫')
                return shot
            if shot is None:
                shot = center_line(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
                if shot:
                   return shot
            if shot is None:
                print('随机前五壶')
                return random_push()
        if shotnum == 5:#先手三投
            shot = None
            # 中线有球就贴
            for enemy in enemy_list:
                if (2.375 - 0.61 < enemy[0] < 2.375 + 0.61) and enemy[1] > 4.88 + 1.83:
                    feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                    v = move(enemy[1]) - 0.15 if move(enemy[1]) - 0.15 > 2.5 else 2.5
                    offset = -stone_r if enemy[0] > 2.375 else stone_r
                    if feasibility:
                        shot = list_to_str([v, enemy[0] + offset - 2.375, 0])
                        if shot:
                            print('贴近对手的中线球')
                            print(shot)
                            return shot
            # 大本营有球就打
            shot = shot_enemy(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
            if shot:
                print('打出对手的球')
                print(shot)
                return shot

            x_list = []
            for i in (0, 0.1, -0.1, 0.2, -0.2, 0.3, -0.3, 0.4, -0.4):
                x_list.append(center_x + i)
            for x_crash in x_list:
                feasibility, obs_list = check_path_straight(point_list, x_crash, 6.71)
                if feasibility:
                    shot = list_to_str([random.uniform(2.7, 2.95), x_crash - center_x, 0])
                    return shot
                else:
                    continue
    if shotnum < 12:
        if advantage_team == 'enemy':
            shot = advance_enemy(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
            if shot:
                return shot
        if advantage_team == 'ally':
            shot = advance_ally(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
            if shot:
                return shot
        else:
            return empty_center(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
    else:
        if shotnum == 15:
            print('我方先手最后一球')
            safe = False
            if advantage_team == 'ally':
                print('现在我方占优势，防守')
                defense = defend_center(point_list, ally_list, min_point,enemy_list)
                return defense
            if advantage_team == 'enemy':
                print('现在敌人占优势，我们要减小劣势或者打出绝胜球')
                ally_score = 0
                for  point_crashed in ally_list:
                    dis = get_distance(point_crashed[0], point_crashed[1], center_x, center_y)
                    if is_in_house(point_crashed[0], point_crashed[1]) :
                        ally_score += 1
                if advantage_score <= 2 and ally_score >= 1:
                    print('对面优势小，尝试抢分')
                    for point_crashed in ally_list:
                        type4 = analyse_point(point_crashed)
                        if type4 in ['three', 'four', 'five', 'six']:
                            if (abs(cal_angle(point_crashed[0],point_crashed[1],min_point[0],min_point[1])) < 55 and 0.545 < point_crashed[0] < 4.205and get_distance_circle(min_point[0], min_point[1]) < 0.6):
                                left_or_right = 1 if point_crashed[0] > 2.375 else -1
                                shot_offset, v, w = detect_path(point_list, point_crashed,min_point, 'out',left_or_right)
                                if shot_offset:
                                    print('外碰执行')
                                    print('target point:', point_crashed)
                                    return list_to_str([v + 2.5, shot_offset, w])

                    print('再看看能否传进去一个球，内碰')
                    safe_fit_enemy, danger_fit_enemy = crash_others(point_list, enemy_list)
                    if safe_fit_enemy:
                        return list_to_str([safe_fit_enemy[0][2], safe_fit_enemy[0][1], safe_fit_enemy[0][3]])
                    if danger_fit_enemy:
                        return list_to_str([danger_fit_enemy[0][2], danger_fit_enemy[0][1], danger_fit_enemy[0][3]])
                    print('看看能否传进去一个球，外碰')
                    for ally in ally_list:
                        left_or_right = 1 if ally[0] > 2.375 else -1
                        type_3 = analyse_point(ally)
                        if type_3 in ['three', 'four', 'five', 'six']:
                            if abs(cal_angle(ally[0], ally[1], 2.375, 4.88)) < 55 and 0.545 < ally[0] < 4.205:
                                if 50 < abs(cal_angle(ally[0], ally[1], 2.375, 4.88)) < 55:
                                    extra = 1.5
                                else:
                                    extra = 0
                                shot_offset, v, w = detect_path(point_list, ally, (2.375, 4.88), 'out', left_or_right)
                                if shot_offset:
                                    print('target point:', (2.375, 4.88))
                                    return list_to_str([v + extra, shot_offset, w])
                    
                    # 外碰对面的
                    if get_distance_circle(min_point[0], min_point[1]) < 0.6:
                        for ally in ally_list:
                            left_or_right = 1 if ally[0] > 2.375 else -1
                            type_3 = analyse_point(ally)
                            if type_3 in ['three', 'four', 'five', 'six']:
                                if abs(cal_angle(ally[0], ally[1], min_point[0], min_point[1])) < 55 and 0.645 < ally[0] < 4.105:
                                    if 50 < abs(cal_angle(ally[0], ally[1], min_point[0], min_point[1])) < 55:
                                        extra = 1.5
                                    else:
                                        extra = 0
                                    shot_offset, v, w = detect_path(point_list, ally, min_point, 'out', left_or_right)
                                    after = find_obs_after(ally_list, ally[0], ally[1], min_point[0], min_point[1])
                                    if shot_offset and after == []:
                                        print('target point:', min_point)
                                        return list_to_str([v + extra + 2, shot_offset, w])
                else:
                    print('敌方优势巨大，尝试清台')
                    print('优先找双碰球')
                    for enemy in enemy_list:
                        if not is_in_house(enemy[0], enemy[1]): continue
                        double = double_kill(point_list, enemy_list, enemy)
                        if double:
                            shot = list_to_str([double[0][2], double[0][1], double[0][3]])
                            if shot:
                                return shot

                    print('先看能不能撞出去')
                    feasibility1, obs_list = check_path_straight(point_list, min_point[0], min_point[1], factor=0.2)
                    if feasibility1:
                        return list_to_str([4, min_point[0] - 2.375, -0.05])
                    else:
                         print('最靠近中心的直着打不出去')                                       
                    print('看看能不能斜碰出去')                    
                    shot_offset, v, w = hit_enemy(point_list, enemy_list)                    
                    if shot_offset:                    
                         return list_to_str([v, shot_offset, w])
                    print('看看能不能插空')
                    interval_point = find_interval(point_list, ally_list, enemy_list)
                    if interval_point:
                        extra = 0
                        for enemy_temp in enemy_list:
                            if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                                                        enemy_temp) == 'top-red':
                                if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                                    extra = 1
                                else:
                                    extra = 0
                        return list_to_str([3 + extra, interval_point[0] - 2.375, 0])
                                
        if shotnum == 16:
            print('我方最后一球，扩大或者减少劣势')
            if advantage_team == 'ally':
                print('考虑扩大优势, 但是要确定会不会给对面做嫁衣4')
                print('首先做预测判断')
                # ======================
                predict_interval = []
                hit = False
                interval_point = find_interval(point_list, ally_list, enemy_list)
                extra = 0
                if interval_point:
                    for enemy_temp in enemy_list:
                        if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                                enemy_temp) == 'top-red':
                            if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                                extra = 1
                            else:
                                extra = 0
                    predict_interval = [interval_point[0], 4.88]
                feasibility, obs_list = check_path_straight(point_list, enemy_list[0][0], 3.05)
                if feasibility:
                    hit = True
                print('predict结果：', predict_interval, hit)
                if hit and predict_interval:
                    # 说明两种选择都行
                    status = predict(point_list, ally_list, enemy_list, predict_interval)
                    print('predict结果：', status)
                    if status:
                        if status == 'first':
                            return list_to_str([3 + extra, interval_point[0] - 2.375, 0])
                        if status == 'second':
                            return list_to_str([5, enemy_list[0] - 2.375, 0])
                        else:
                            pass
                    else:
                        pass
                # ======================
                print('看看能不能插空')
                interval_point = find_interval(point_list, ally_list, enemy_list)
                if interval_point:
                    extra = 0
                    for enemy_temp in enemy_list:
                        if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                                enemy_temp) == 'top-red':
                            if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                                extra = 1
                            else:
                                extra = 0
                    return list_to_str([3 + extra, interval_point[0] - 2.375, 0])
                else:
                    pass
                print('看能不能内碰')
                for enemy in enemy_list:
                    type_enemy = analyse_point(enemy)
                    if type_enemy == 'three' or type_enemy == 'four':
                        for point_target1 in point_center:
                            angle = cal_angle(enemy[0], enemy[1], point_target1[0], point_target1[1])
                            if 10 < abs(angle) < 70:
                                print('区间合适，判断是否可行1')
                                left_or_right = 1 if enemy[0] > point_target1[0] else -1
                                shot_offset, v, w = detect_path(point_list, enemy, point_target1,'in',left_or_right)
                                if shot_offset:
                                    return list_to_str([v, shot_offset, w])
                                else:
                                    continue
                            else:
                                continue
                    else:
                        pass
                print('内碰不合适，考虑用外碰扩大优势4')
                for ally in ally_list:
                    type_last2 = analyse_point(ally)
                    if type_last2 in ['three', 'four', 'five', 'six']:
                        for point_target1 in point_center:
                            if abs(cal_angle(ally[0], ally[1], point_target1[0], point_target1[1])) < 55 and 0.545 < \
                                    ally[0] < 4.205:
                                if 50 < abs(cal_angle(ally[0], ally[1], point_target1[0], point_target1[1])) < 55:
                                    extra = 1.5
                                else:
                                    extra = 0
                                print('区间合适，判断外碰是否可行2')
                                left_or_right = 1 if ally[0] > 2.375 else -1
                                if get_distance(ally[0], ally[1], 2.375, 4.88) < 0.6:
                                    print('已经在中心了不用管2')
                                    pass
                                else:
                                    shot_offset, v, w = detect_path(point_list, ally,
                                                                    point_target1, 'out',
                                                                    left_or_right)
                                    if shot_offset:
                                        print('center外碰执行3')
                                        print('target point:', point_target1)
                                        return list_to_str([v + extra, shot_offset, w])
                            else:
                                pass
                    else:
                        continue
                print('到这里说明内碰外碰都不行看能不能见缝插针4')
                interval_point = find_interval(point_list, ally_list, enemy_list)
                if interval_point:
                    extra = 0
                    for enemy_temp in enemy_list:
                        if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                                enemy_temp) == 'top-red':
                            if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                                extra = 1
                            else:
                                extra = 0
                    return list_to_str([3 + extra, interval_point[0] - 2.375, 0])
                else:
                    pass
                print('上面的内外碰和直插都不行，那就直打对方球')
                for enemy3 in enemy_list:
                    feasibility, obs_list = check_path_straight(point_list, enemy3[0], enemy3[1])
                    if feasibility:
                        print('赶尽杀绝')
                        return list_to_str([5, enemy3[0] - 2.375, 0])
                print('上面的都不行，随便打一个')
                return random_push()
            if advantage_team == 'enemy':
                # ======================
                predict_interval = []
                hit = False
                interval_point = find_interval(point_list, ally_list, enemy_list)
                extra = 0
                print(enemy_list)
                if interval_point:
                    for enemy_temp in enemy_list:
                        if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(enemy_temp) == 'top-red':
                            if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                                extra = 1
                            else:
                                extra = 0
                    predict_interval = [interval_point[0], 4.88]
                feasibility, obs_list = check_path_straight(point_list, enemy_list[0][0], 3.05)
                if feasibility:
                    hit = True
                print('predict结果：', predict_interval, hit)
                if hit and predict_interval:
                    # 说明两种选择都行
                    status = predict(point_list, ally_list, enemy_list, predict_interval)
                    print('predict结果：', status)
                    if status:
                        if status == 'first':
                            return list_to_str([3 + extra, interval_point[0] - 2.375, 0])
                        if status == 'second':
                            return list_to_str([5, enemy_list[0] - 2.375, 0])
                        else:
                            pass
                    else:
                        pass
                # ======================
                # 优先判断红心有没有球
                enemy_in_red = enemy_red(enemy_list)
                if not enemy_in_red:
                    print('优先往中线打球')
                    print('插空')
                    interval_point = find_interval(point_list, ally_list, enemy_list)
                    if interval_point:
                        extra = 0
                        for enemy_temp in enemy_list:
                            if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                                    enemy_temp) == 'top-red':
                                if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                                    extra = 1
                                else:
                                    extra = 0
                        return list_to_str([3 + extra, interval_point[0] - 2.375, 0])
                    else:
                        pass
                    print('外碰自己的进中心')
                    sorted_ally_list = sorted(ally_list, key=lambda point_ally: abs(cal_angle(point_ally[0], point_ally[1], min_point[0], min_point[1])))
                    for ally in sorted_ally_list:
                        type_last2 = analyse_point(ally)
                        if type_last2 in ['three', 'four', 'five', 'six']:
                            if abs(cal_angle(ally[0], ally[1], 2.375, 4.88)) < 55 and 0.545 < \
                                    ally[0] < 4.205:
                                print('区间合适，判断外碰是否可行7')
                                if 50 < abs(cal_angle(ally[0], ally[1], 2.375, 4.88)) < 55:
                                    extra = 1.5
                                else:
                                    extra = 0
                                left_or_right = 1 if ally[0] > 2.375 else -1
                                if get_distance(ally[0], ally[1], 2.375, 4.88) < 0.3:
                                    print('已经在中心了不用管7')
                                    pass
                                else:
                                    shot_offset, v, w = detect_path(point_list, ally,(2.375, 4.88), 'out',left_or_right)
                                    if shot_offset:
                                        print('center外碰执行7')
                                        print('target point:', (2.375, 4.88))
                                        return list_to_str([v + extra, shot_offset, w])
                            else:
                                pass
                        else:
                            pass
                    print('考虑内碰')
                    safe_fit_enemy, danger_fit_enemy = crash_others(point_list, enemy_list)
                    if safe_fit_enemy:
                        return list_to_str([safe_fit_enemy[0][2], safe_fit_enemy[0][1], safe_fit_enemy[0][3]])
                    if danger_fit_enemy:
                        return list_to_str([danger_fit_enemy[0][2], danger_fit_enemy[0][1], danger_fit_enemy[0][3]])
                if enemy_in_red:
                    if len(enemy_list) >= 2:
                        if get_distance_circle(enemy_list[1][0], enemy_list[1][1]) < 1.83 + stone_r:
                            problem = straight_problem(enemy_list)
                        else:
                            problem = True
                    else:
                        problem = True
                    if problem:
                        feasibility, obs_list = check_path_straight(point_list,min_point[0],min_point[1],factor = 0.0)
                        if feasibility:
                            print('打最靠近中新的球')
                            shot = list_to_str([5, min_point[0] - 2.375 + 0.03, 0])
                            return shot
                        else:
                            pass
                    else:
                        pass
                    print('考虑内碰')
                    safe_fit_enemy, danger_fit_enemy = crash_others(point_list, enemy_list)
                    if danger_fit_enemy:
                        return list_to_str([danger_fit_enemy[0][2], danger_fit_enemy[0][1], danger_fit_enemy[0][3]])
                    print('把对面最靠近中新的外碰出去')
                    sorted_ally_list = sorted(ally_list, key=lambda point_ally: abs(cal_angle(point_ally[0], point_ally[1], min_point[0], min_point[1])))
                    for point_crashed in sorted_ally_list:
                        type4 = analyse_point(point_crashed)
                        if type4 in ['three', 'four', 'five', 'six']:
                            if (abs(cal_angle(point_crashed[0], point_crashed[1], min_point[0],min_point[1])) < 55
                            and 0.545 < point_crashed[0] < 4.205
                            and get_distance_circle(min_point[0], min_point[1]) < 0.3):
                                if 50 < abs(cal_angle(point_crashed[0], point_crashed[1], min_point[0],min_point[1])) < 55:
                                    extra = 1.5
                                else:
                                    extra = 0
                                left_or_right = 1 if point_crashed[0] > 2.375 else -1
                                shot_offset, v, w = detect_path(point_list, point_crashed,min_point, 'out',left_or_right)
                                if shot_offset:
                                    print('外碰执行')
                                    print('target point:', point_crashed)
                                    return list_to_str([v + 2 + extra, shot_offset, w])
                                else:
                                    pass
                            else:
                                pass
                        else:
                            pass
                    print('外碰自己的进中心')
                    for ally in ally_list:
                        type_last2 = analyse_point(ally)
                        if type_last2 in ['three', 'four', 'five', 'six']:
                            if abs(cal_angle(ally[0], ally[1], 2.375, 4.88)) < 55 and 0.545 < \
                                    ally[0] < 4.205:
                                print('区间合适，判断外碰是否可行7')
                                if 50 < abs(cal_angle(ally[0], ally[1], 2.375, 4.88)) < 55:
                                    extra = 1.5
                                else:
                                    extra = 0
                                left_or_right = 1 if ally[0] > 2.375 else -1
                                if get_distance(ally[0], ally[1], 2.375, 4.88) < 0.3:
                                    print('已经在中心了不用管7')
                                    pass
                                else:
                                    shot_offset, v, w = detect_path(point_list, ally,(2.375, 4.88), 'out',left_or_right)
                                    if shot_offset:
                                        print('center外碰执行7')
                                        print('target point:', (2.375, 4.88))
                                        return list_to_str([v + extra, shot_offset, w])
                    print('插空')
                    interval_point = find_interval(point_list, ally_list, enemy_list)
                    if interval_point:
                        extra = 0
                        for enemy_temp in enemy_list:
                            if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                                    enemy_temp) == 'top-red':
                                if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                                    extra = 1
                                else:
                                    extra = 0
                        return list_to_str([3 + extra, interval_point[0] - 2.375, 0])
                    else:
                        pass
        print('last')
        if advantage_team == 'enemy':
            shot = advance_enemy(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
            if shot:
                return shot
        if advantage_team == 'ally':
            shot = advance_ally(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)
            if shot:
                return shot
        return empty_center(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point)

        print("已关闭socket连接")

class AIRobot:
    def __init__(self, key, name="冰冰薄荷糖", host='127.0.0.1', port=7788, show_msg=False):
        # 新建Socket对象
        self.ai_sock = socket.socket()
        # 创建Socket连接
        self.ai_sock.connect((host, port))
        print("已建立socket连接", host, port)

        # 是否显示接收/发送的消息
        self.show_msg = show_msg
        # 发送连接密钥
        self.send_msg("CONNECTKEY:" + key)

        # 设定机器人名称
        self.name = name
        # 初始化冰壶位置
        self.position = [0] * 32
        # 初始化冰壶运动信息
        self.motioninfo = [0] * 5
        # 设定起始局数
        self.round_num = 0

    # 通过socket对象发送消息
    def send_msg(self, msg):
        if self.show_msg:
            print("  >>>> " + msg)
        # 将消息数据从字符串类型转换为bytes类型后发送
        self.ai_sock.send(msg.strip().encode())

    # 通过socket对象接收消息并进行解析
    def recv_msg(self):
        # 为避免TCP粘包问题，数字冰壶服务器发送给AI选手的每一条信息均以0（数值为0的字节）结尾
        # 这里采用了逐个字节接收后拼接的方式处理信息，多条信息之间以0为信息终结符
        buffer = bytearray()
        while True:
            # 接收1个字节
            data = self.ai_sock.recv(1)
            # 接收到空数据或者信息处终结符(0)即中断循环
            if not data or data == b'\0':
                break
            # 将当前字节拼接到缓存中
            buffer.extend(data)
        # 将消息数据从bytes类型转换为字符串类型后去除前后空格
        msg_str = buffer.decode().strip()
        if self.show_msg:
            print("<<<< " + msg_str)

        # 用空格将消息字符串分隔为列表
        msg_list = msg_str.split(" ")
        # 列表中第一个项为消息代码
        msg_code = msg_list[0]
        # 列表中后续的项为各个参数
        msg_list.pop(0)
        # 返回消息代码和消息参数列表
        return msg_code, msg_list

    # 与大本营中心距离
    def get_dist(self, x, y):
        House_x = 2.375
        House_y = 4.88
        return math.sqrt((x - House_x) ** 2 + (y - House_y) ** 2)

    # 大本营内是否有壶
    def is_in_house(self, dist):
        House_R = 1.830
        Stone_R = 0.145
        if dist < (House_R + Stone_R):
            return 1
        else:
            return 0

    def recv_setstate(self, msg_list):
        # 当前完成投掷数
        self.shot_num = int(msg_list[0])
        # 当前完成对局数
        self.round_num = int(msg_list[1])
        # 总对局数
        self.round_total = int(msg_list[2])
        # 预备投掷者（0为持蓝色球者，1为持红色球者）
        self.next_shot = int(msg_list[3])

    # 接收并处理消息
    def recv_forever(self):
        # 空消息计数器归零
        retNullTime = 0
        self.on_line = True

        while self.on_line:
            # 接收消息并解析
            msg_code, msg_list = self.recv_msg()
            # 如果接到空消息则将计数器加一
            if msg_code == "":
                retNullTime = retNullTime + 1
            # 如果接到五条空消息则关闭Socket连接
            if retNullTime == 5:
                break
                # 如果消息代码是……
            if msg_code == "CONNECTNAME":
                if msg_list[0] == "Player1":
                    self.player_is_init = True
                    print("玩家1，首局先手")
                else:
                    self.player_is_init = False
                    print("玩家2，首局后手")
            if msg_code == "ISREADY":
                # 发送"READYOK"
                self.send_msg("READYOK")
                time.sleep(0.5)
                # 发送"NAME"和AI选手名
                self.send_msg("NAME " + self.name)
                print(self.name + " 准备完毕！")
            if msg_code == "NEWGAME":
                time0 = time.time()
            if msg_code == "SETSTATE":
                self.recv_setstate(msg_list)
            if msg_code == "POSITION":
                for n in range(32):
                    self.position[n] = float(msg_list[n])
            if msg_code == "GO":
                # 制定策略生成投壶信息
                print('==================shot_num↓=====================', self.shot_num + 1)
                shot_msg = strategy(self.position, self.shot_num + 1, self.player_is_init)
                print('==================shot_num↑=====================', self.shot_num + 1)
                # 发送投壶消息
                print(shot_msg)
                print(self.position)
                self.send_msg(shot_msg)
            if msg_code == "MOTIONINFO":
                for n in range(5):
                    self.motioninfo[n] = float(msg_list[n])
            # 如果消息代码是"SCORE"
            if msg_code == "SCORE":
                # time1 = time.time()
                # print("%s 第%d局耗时%.1f秒" % (time.strftime("[%Y/%m/%d %H:%M:%S]"), self.round_num + 1, time1 - time0),
                #       end=" ")
                # time0 = time1
                # 从消息参数列表中获取得分
                self.score = int(msg_list[0])
                # 得分的队伍在下一局是先手
                if self.score > 0:
                    print("我方得" + str(self.score) + "分", end=" ")
                    # 如果不是无限对战模式(固定先后手)
                    if self.round_total != (-1):
                        self.player_is_init = True
                # 失分的队伍在下一局是后手
                elif self.score < 0:
                    print("对方得" + str(self.score * -1) + "分", end=" ")
                    # 如果不是无限对战模式(固定先后手)
                    if self.round_total != (-1):
                        self.player_is_init = False
                # 平局下一局交换先后手
                else:
                    print("双方均未得分", end=" ")
                    # 如果不是无限对战模式(固定先后手)
                    if self.round_total != (-1):
                        self.player_is_init = not self.player_is_init
                if self.player_is_init:
                    print("我方下局先手")
                else:
                    print("我方下局后手")
                    # 初始化冰壶位置
                self.position = [0] * 32
                    # 初始化冰壶运动信息
                self.motioninfo = [0] * 5
                    # 设定起始局数
                self.round_num = 0
            # 如果消息代码是"GAMEOVER"
            if msg_code == "GAMEOVER":
                if msg_list[0] == "WIN":
                    print("我方获胜")
                elif msg_list[0] == "LOSE":
                    print("对方获胜")
                else:
                    print("双方平局")


        # 关闭Socket连接
        self.ai_sock.close()
        print("已关闭socket连接")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='冰糖壶芦-test')
    parser.add_argument('-H', '--host', help='tcp server host', default='127.0.0.1', required=False)
    parser.add_argument('-p', '--port', help='tcp server port', default=7788, required=False)
    args, unknown = parser.parse_known_args()
    print(args)

    # 根据数字冰壶服务器界面中给出的连接信息修改CONNECTKEY，注意这个数据每次启动都会改变。
    key = "hituavlab_4747b2fe-ac7f-46c8-90fc-807257768f85"
    # 初始化AI选手
    airobot = AIRobot(key, host=args.host, port=int(args.port))
    # 启动AI选手处理和服务器的通讯
    airobot.recv_forever()
