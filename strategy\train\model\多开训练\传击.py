def double_go_out(X_1,Y_1,X_2,Y_2,left_or_right=1):
    near_y=max(Y_2,Y_1)
    far_y=min(Y_1,Y_2)

    if Y_1 >= Y_2:
        near_x = X_1
        far_x = X_2
    else:
        near_x = X_2
        far_x =X_1

    angle ,dis = cal_angle_dis(near_x, near_y, far_x, far_y)

    if near_x <=far_x:
        left_or_right=-1
        if abs(angle)<5:
            if dis < 1.8:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.6*stone_r, far_y)
            else:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.1 * stone_r, far_y)
            return 6,x,-0.05
        elif 5<=abs(angle)<10:
            if dis < 1.8:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.6 * stone_r, far_y)
            else:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.0 * stone_r, far_y)
            return 6, x, -0.05
        elif 10<=abs(angle)<=20:
            if dis < 1.6:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.6 * stone_r, far_y)
            else:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.2 * stone_r, far_y)
            return 6, x, 0
        elif 20 < abs(angle)<25:
            if dis < 0.5:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x -1.8*stone_r, far_y-1.5*stone_r)
            elif 0.5<=dis<=1 :
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.2 * stone_r,far_y - 0.8 * stone_r)
            elif 1<=dis<=2.8 :
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.2 * stone_r,far_y - 0.85 * stone_r)
            else:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.2 * stone_r, far_y-0.4*stone_r)
        elif 25 < abs(angle)<30:
            if dis < 0.5:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.8 * stone_r,far_y - 1.5 * stone_r)
            elif 0.5 <= dis <= 1:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.2 * stone_r,far_y - 0.8 * stone_r)
            elif 1 <= dis <= 2.8:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.2 * stone_r,far_y - 0.85 * stone_r)
            else:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.1 * stone_r,far_y - 0.4 * stone_r)
        elif 30 < abs(angle) < 40:
            if dis < 0.5:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.2 * stone_r, far_y - 1.5 * stone_r)
            elif 0.5 <= dis <= 1:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.2 * stone_r, far_y - 0.8 * stone_r)
            elif 1 <= dis <= 2.4:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 0.8 * stone_r,far_y - 0.7 * stone_r)
            else:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 0.7 * stone_r,far_y - 0.5 * stone_r)
        else: pass
    else:
        left_or_right =1
        if abs(angle) < 5:
            if dis < 1.8:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.6 * stone_r, far_y)
            else:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.1 * stone_r, far_y)
            return 6, x, 0.05
        elif 5 <= abs(angle) < 10:
            if dis < 1.8:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.6 * stone_r, far_y)
            else:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.0 * stone_r, far_y)
            return 10, x, 0.05
        elif 10 <= abs(angle) <= 20:
            if dis < 1.6:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.6 * stone_r, far_y)
            else:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x - 1.2 * stone_r, far_y)
            return 6, x, 0
        elif 20 < abs(angle) < 25:
            if dis < 0.5:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.8 * stone_r,
                                                      far_y - 1.5 * stone_r)
            elif 0.5 <= dis <= 1:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.2 * stone_r,
                                                      far_y - 0.8 * stone_r)
            elif 1 <= dis <= 2.8:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.2 * stone_r,
                                                      far_y - 0.85 * stone_r)
            else:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.2 * stone_r,
                                                      far_y - 0.4 * stone_r)
        elif 25 < abs(angle) < 30:
            if dis < 0.5:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.8 * stone_r,
                                                      far_y - 1.5 * stone_r)
            elif 0.5 <= dis <= 1:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.2 * stone_r,
                                                      far_y - 0.8 * stone_r)
            elif 1 <= dis <= 2.8:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.2 * stone_r,
                                                      far_y - 0.85 * stone_r)
            else:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.1 * stone_r,
                                                      far_y - 0.4 * stone_r)
        elif 30 < abs(angle) < 40:
            if dis < 0.5:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.2 * stone_r,
                                                      far_y - 1.5 * stone_r)
            elif 0.5 <= dis <= 1:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 1.2 * stone_r,
                                                      far_y - 0.8 * stone_r)
            elif 1 <= dis <= 2.4:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 0.8 * stone_r,
                                                      far_y - 0.7 * stone_r)
            else:
                x, v, w, cx, cy = cal_delta_x_outside(near_x, near_y, left_or_right, far_x + 0.7 * stone_r,
                                                      far_y - 0.5 * stone_r)
        else:
            pass
    return 6,x,0