import socket
import time
import random
import math
import argparse
from util import *

House_x = 2.375  # 大本营中心位置坐标
House_y = 4.88
House_R = 1.830  # 大本营半径
Stone_R = 0.145  # 冰壶半径
s_R = 0.61
'''
冰壶场地：
1. 中心线：y=4.88
2. 大本营：中心坐标(2.375, 4.88)，半径1.83
3. 中线：x=2.375

冰壶策略名称：
1. 铺垫：靠近营垒的固定位置球
2. 直打：直接击打对方的球
3. 双碰：用自己的球同时击飞对方的两颗球
4. 外碰：打自己的球，然后用自己的球从外侧大力击飞对方的球
5. 外撞：用直接自己的球击飞对方的球
6. 内碰：打自己的球，然后用自己的球从内侧击开对方的球
7. 传击：打敌方的球，然后用敌方的球击飞对方的球
8. 插空：在敌方球之间的空隙中放置自己的球
9. 贴近：贴近对手的中线球
'''
# 判断是否可以铺垫球到指定位置
def pudian(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point):
    print('左侧铺垫')
    x = -0.6
    if not find_obs(point_list, x + House_x, 12, x + House_x, 6, 2, 2):
        return 2.75,x,0.05

# 判断是否可以直接击打对方的球
def shot_enemy(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point):
    print('击出敌方营内球')
    if enemy_list:
        for enemy in enemy_list:
            if get_distance_circle(enemy[0], enemy[1]) < 0.61 * 2 + stone_r - s_R:
                feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                if feasibility:
                    return math.sqrt(2.65*enemy[1])-0.12,enemy[0] - 2.375,0.05
    return None
# 沿中心线寻找合适的击打点
def center_line(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point):
    print('沿中心线寻找合适的击打点')
    x_list = []
    for i in (0.1, -0.1, 0.2, -0.2, 0.3, -0.3, 0.4, -0.4, 0.5, -0.5, 0.6, -0.6):
        x_list.append(House_x + i)
    for x_crash in x_list:
        feasibility, obs_list = check_path_straight(point_list, x_crash, 4.88)
        if feasibility:
            return 2.95,x_crash - House_x,0.05
    return None
def close_enemy(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point):
    print('贴近对手的中线球')
    for enemy in enemy_list:
        if (House_x - 0.61* 2 < enemy[0] < House_x + 0.61* 2) and enemy[1] > 4.88:
            feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
            v = move(enemy[1] - 0.15) if move(enemy[1] - 0.15) > 2.5 else 2.5

            if feasibility:
                return v, enemy[0] - House_x, 0
    return None
# 处理大本营中无球的情况，优先考虑插空或内碰
def empty_center(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point):
    print('大本营中无球，处理')
    for enemy in enemy_list:
        print('双碰')
        double = double_kill(point_list, enemy_list, enemy)
        if double:
            return double[0][2], double[0][1], double[0][3]
        print('能不能用对面的球把对面打出去')
        sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
        if sorted_fit_shot:
            print('sorted_fit_shot', sorted_fit_shot)
            return sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]
        else:
            pass
        print('外碰')
        ally_list_sort = sorted(ally_list, key=lambda ally_sort: abs(
            cal_angle(ally_sort[0], ally_sort[1], enemy[0], enemy[1])))
        for point_crashed in ally_list_sort:
            type4 = analyse_point(point_crashed)
            if type4 in ['three', 'four', 'five', 'six']:
                if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                 enemy[1])) < 55 and 0.545 < point_crashed[0] < 4.205:
                    left_or_right = 1 if point_crashed[0] > 2.375 else -1
                    shot_offset, v, w = detect_path(point_list, point_crashed,
                                                    (enemy[0], enemy[1]), 'out',
                                                    left_or_right)
                    if shot_offset:
                        return v + 1, shot_offset, w
        print('传击，敌打敌')
        sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
        if sorted_fit_shot:
            print('sorted_fit_shot', sorted_fit_shot)
            return sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]

        print('直打')
        feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
        if feasibility:
            return 8, enemy[0] - 2.375, 0.05

    print('考虑内碰')
    safe_fit_enemy, danger_fit_enemy = crash_others(point_list, enemy_list)
    if safe_fit_enemy:
        return safe_fit_enemy[0][2],safe_fit_enemy[0][1],safe_fit_enemy[0][3]
    if danger_fit_enemy:
        return danger_fit_enemy[0][2],danger_fit_enemy[0][1],danger_fit_enemy[0][3]
    safe_fit_ally, danger_fit_ally = crash_us(point_list, ally_list)
    if danger_fit_ally:
        return danger_fit_ally[0][2],danger_fit_ally[0][1],danger_fit_ally[0][3]
    print('考虑插空')
    interval_point = find_interval(point_list, ally_list, enemy_list)
    if interval_point:
        extra = 0
        for enemy_temp in enemy_list:
            if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(enemy_temp) == 'top-red':
                if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                    extra = 1
                else:
                    extra = 0
        return 3 + extra,interval_point[0] - 2.375,0.05
    else:
        return 3, 0 ,0.05

def advance_enemy(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point):
    print('enemy 领先', advantage_score)
    for enemy in enemy_list:
        shot = None
        if enemy == enemy_list[0]:
            print('这是对最靠近中心的球的处理，坐标为', enemy)
            color_type = analyse_by_color(enemy)
            print(color_type)
            if color_type in ['top-red', 'bottom-red']:
                our_in_red = our_curling_in_red(ally_list)
                if not our_in_red:#红区没有我们的球
                    count = count_enemy_in_red(enemy_list)
                    #敌方红区只有一个球
                    if count == 1:
                        print('中心只有一个球')
                        print('双碰')
                        double = double_kill(point_list, enemy_list, enemy)
                        if double:
                            return double[0][2],double[0][1],double[0][3]

                        print('能不能用对面的球把对面打出去')
                        sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
                        if sorted_fit_shot:
                            print('sorted_fit_shot', sorted_fit_shot)
                            return sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]
                        else:
                            pass

                        print('外碰')
                        ally_list_sort = sorted(ally_list, key=lambda ally_sort: abs(
                            cal_angle(ally_sort[0], ally_sort[1], enemy[0], enemy[1])))
                        for point_crashed in ally_list_sort:
                            type4 = analyse_point(point_crashed)
                            if type4 in ['three', 'four', 'five', 'six']:
                                if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                                 enemy[1])) < 55 and 0.545 < point_crashed[0] < 4.205:
                                    left_or_right = 1 if point_crashed[0] > 2.375 else -1
                                    shot_offset, v, w = detect_path(point_list, point_crashed,
                                                                    (enemy[0], enemy[1]), 'out',
                                                                    left_or_right)
                                    if shot_offset:
                                        return v+1,shot_offset,w
                        print('传击，敌打敌')
                        sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
                        if sorted_fit_shot:
                            print('sorted_fit_shot', sorted_fit_shot)
                            return sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]

                        print('直打')
                        feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                        if feasibility:
                            return 8, enemy[0] - 2.375, 0.05

                        print('外撞')
                        shot_offset, v, w = hit_enemy_point(point_list, enemy)
                        if shot_offset:
                            return v,shot_offset,w
                        print('插空')
                        interval_point = find_interval(point_list, ally_list, enemy_list)
                        if interval_point:
                            extra = 0
                            for enemy_temp in enemy_list:
                                if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                                        enemy_temp) == 'top-red':
                                    if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                                        extra = 1
                                    else:
                                        extra = 0
                            return 3+extra,interval_point[0]-2.375,0.05
                    #敌方红区只有两个球    
                    else:
                        enemy_dis = get_distance(enemy_list[0][0], enemy_list[0][1], enemy_list[1][0],
                                                 enemy_list[1][1])
                        if enemy_dis < 4 * stone_r:
                            near, center_coord = near_analyse(enemy_list[0], enemy_list[1])
                            y_coord = max(enemy_list[0][1], enemy_list[1][1])
                            if near in ['even', 'slant']:
                                print('两个球挨得很近')
                                feasibility, obs_list = check_path_straight(point_list, center_coord, y_coord + stone_r)
                                if feasibility:
                                    return 8,enemy[0]-2.375,0.05
                                    
                        else:
                            print('两个球的距离比较远')
                            far = far_analyse(enemy_list[0], enemy_list[1])
                            double_enemy = [enemy_list[0], enemy_list[1]]
                            sorted_double_enemy = sorted(double_enemy, key=lambda message: abs(message[1]))
                            if far == 'vertical':
                                print('vertical情况')
                                print('外双碰检测')
                                shot_offset, v, w = detect_path(point_list, sorted_double_enemy[1],
                                                                sorted_double_enemy[0], 'out',
                                                                1, True)
                                if shot_offset:
                                    return v+2,shot_offset,w
                                else:
                                    pass
                                print('能不能用对面的球把对面打出去')
                                sorted_fit_shot = double_shot(point_list, enemy_list, enemy)
                                if sorted_fit_shot:
                                    print('sorted_fit_shot', sorted_fit_shot)
                                    return sorted_fit_shot[0][2],sorted_fit_shot[0][1],sorted_fit_shot[0][3]

                            if far == 'slant':
                                print('slant情况,双碰')
                                double = double_kill(point_list, [sorted_double_enemy[1]],
                                                     sorted_double_enemy[0])
                                if double:
                                    return double[0][2],double[0][1],double[0][3]
                                print('能不能用对面的球把对面打出去')
                                sorted_fit_shot = double_shot(point_list, enemy_list, enemy)
                                if sorted_fit_shot:
                                    print('sorted_fit_shot', sorted_fit_shot)
                                    return sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]
                                    
                            else:
                                print('even情况,处理最靠近中心的重复一个球')
                                print('1.双碰球')
                                double = double_kill(point_list, enemy_list, enemy)
                                if double:
                                    return double[0][2],double[0][1],double[0][3]
                                else:
                                    pass

                                print('2.外碰')
                                ally_list_sort = sorted(ally_list, key=lambda ally_sort: abs(
                                    cal_angle(ally_sort[0], ally_sort[1], enemy[0], enemy[1])))
                                for point_crashed in ally_list_sort:
                                    type4 = analyse_point(point_crashed)
                                    if type4 in ['three', 'four', 'five', 'six']:
                                        if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                                         enemy[1])) < 55 and 0.545 < point_crashed[
                                            0] < 4.205:
                                            left_or_right = 1 if point_crashed[0] > 2.375 else -1
                                            shot_offset, v, w = detect_path(point_list, point_crashed,
                                                                            (enemy[0], enemy[1]),
                                                                            'out',
                                                                            left_or_right)
                                            if shot_offset:
                                                return v+1,shot_offset,w


                                print('4.直打')
                                feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                                if feasibility:
                                    return 8, enemy[0] - 2.375, 0.05

                                print('3.外撞')
                                shot_offset, v, w = hit_enemy_point(point_list, enemy)
                                if shot_offset:
                                    return v, shot_offset, w

                                print('再处理次靠近中心的')
                                print('优先找双碰球')
                                double = double_kill(point_list, enemy_list, enemy_list[1])
                                if double:
                                    return double[0][2],double[0][1],double[0][3]
                                    
                                print('直打')
                                feasibility, obs_list = check_path_straight(point_list, enemy_list[1][0],
                                                                            enemy_list[1][1])
                                if feasibility:
                                    return 8,enemy_list[1][0]-2.375,0.05
                                
                                print('外碰')
                                ally_list_sort = sorted(ally_list, key=lambda ally_sort: abs(
                                    cal_angle(ally_sort[0], ally_sort[1],
                                              enemy_list[1][0],
                                              enemy_list[1][1])))
                                for point_crashed in ally_list_sort:
                                    type4 = analyse_point(point_crashed)
                                    if type4 in ['three', 'four', 'five', 'six']:
                                        if abs(cal_angle(point_crashed[0], point_crashed[1],
                                                         enemy_list[1][0],
                                                         enemy_list[1][1])) < 55 and 0.545 < point_crashed[
                                            0] < 4.205:
                                            left_or_right = 1 if point_crashed[0] > 2.375 else -1
                                            shot_offset, v, w = detect_path(point_list, point_crashed,
                                                                            (enemy_list[1][0],
                                                                             enemy_list[1][1]),
                                                                            'out',
                                                                            left_or_right)
                                            if shot_offset:
                                                return v+1,shot_offset,w

                                print('外撞')
                                shot_offset, v, w = hit_enemy_point(point_list, enemy_list[1])
                                if shot_offset:
                                    return v,shot_offset,w
                                
                                print('插空')
                                interval_point = find_interval(point_list, ally_list, enemy_list)
                                if interval_point:
                                    extra = 0
                                    for enemy_temp in enemy_list:
                                        if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                                                enemy_temp) == 'top-red':
                                            if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                                                extra = 1
                                            else:
                                                extra = 0
                                    return 3+extra,interval_point[0]-2.375,0.05
                else:
                    print('中间有我们的球')
                    print('传击应对')
                    sorted_fit_shot = double_shot_width(point_list, enemy_list, enemy)
                    if sorted_fit_shot:
                        print('sorted_fit_shot', sorted_fit_shot)
                        return sorted_fit_shot[0][2],sorted_fit_shot[0][1],sorted_fit_shot[0][3]
                    print('打最靠近中新的球， 优先找双碰球')
                    double = double_kill(point_list, enemy_list, enemy)
                    if double:
                        return double[0][2],double[0][1],double[0][3]
                    
                    print('双碰失败，直打')
                    feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                    if feasibility:
                        return 8,enemy[0]-2.375,0.05
                    
                    print('内碰打对面')
                    for enemy_crashed in enemy_list:
                        if enemy_crashed == enemy:
                            continue
                        type_enemy = analyse_point(enemy_crashed)
                        if type_enemy in ['three', 'four', 'five', 'six']:
                            angle = cal_angle(enemy_crashed[0], enemy_crashed[1], enemy[0], enemy[1])
                            if 10 < abs(angle) < 70:
                                left_or_right = 1 if enemy_crashed[0] > enemy[0] else -1
                                shot_offset, v, w = detect_path(point_list, enemy_crashed, enemy,
                                                                'in',
                                                                left_or_right)
                                if shot_offset:
                                    return v + 3,shot_offset,w
                                else:
                                    continue
                            else:
                                continue
                        else:
                            pass
                    print('直打不行，外碰')
                    ally_list_sort = sorted(ally_list, key=lambda ally_sort: abs(
                        cal_angle(ally_sort[0], ally_sort[1], enemy[0], enemy[1])))
                    for point_crashed in ally_list_sort:
                        type4 = analyse_point(point_crashed)
                        if type4 in ['three', 'four', 'five', 'six']:
                            if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                             enemy[1])) < 55 and 0.565 < point_crashed[0] < 4.185:
                                left_or_right = 1 if point_crashed[0] > 2.375 else -1
                                shot_offset, v, w = detect_path(point_list, point_crashed,
                                                                (enemy[0], enemy[1]), 'out',
                                                                left_or_right)
                                if shot_offset:
                                    print('外碰执行')
                                    print('target point:', point_crashed)
                                    return v + 3,shot_offset,w
                                else:
                                    pass
                            else:
                                continue
                        else:
                            continue
                    print('外碰不行，外撞')
                    shot_offset, v, w = hit_enemy_point(point_list, enemy)
                    if shot_offset:
                        return v,shot_offset,w
                    
            if color_type in ['top-white']:
                print('能不能用对面的球把对面打出去')
                sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
                if sorted_fit_shot:
                    print('sorted_fit_shot', sorted_fit_shot)
                    return sorted_fit_shot[0][2],sorted_fit_shot[0][1],sorted_fit_shot[0][3]
                else:
                    pass
                print('上白区，优先直打')
                feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                if feasibility:
                    return 8,enemy[0]-2.375,0.05
                print('上白区直达不行，看看双碰')
                double = double_kill(point_list, enemy_list, enemy)
                if double:
                    return double[0][2],double[0][1],double[0][3]
                
                print('上白区双碰不行看看外碰')
                for point_crashed in ally_list:
                    type4 = analyse_point(point_crashed)
                    if type4 in ['three', 'four', 'five', 'six']:
                        if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                         enemy[1])) < 55 and 0.545 < point_crashed[0] < 4.205:
                            left_or_right = 1 if point_crashed[0] > 2.375 else -1
                            shot_offset, v, w = detect_path(point_list, point_crashed,
                                                            (enemy[0], enemy[1]), 'out',
                                                            left_or_right)
                            if shot_offset:
                                print('外碰执行')
                                print('target point:', point_crashed)
                                return v+2,shot_offset,w
                            else:
                                pass
                        else:
                            continue
                    else:
                        continue
                print('上白区外碰不行，外撞')
                shot_offset, v, w = hit_enemy_point(point_list, enemy)
                if shot_offset:
                    return v,shot_offset,w
                
                print('上白区外撞不行， 插空')
                interval_point = find_interval(point_list, ally_list, enemy_list)
                if interval_point:
                    extra = 0
                    for enemy_temp in enemy_list:
                        if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                                enemy_temp) == 'top-red':
                            if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                                extra = 1
                            else:
                                extra = 0
                    return 3+extra,interval_point[0]-2.375,w
                else:
                    pass
            if color_type in ['bottom-white']:
                print('下白区优先看能不能内碰')
                type_enemy = analyse_point(enemy)
                if type_enemy == 'three' or type_enemy == 'four':
                    for point_target1 in point_center:
                        angle = cal_angle(enemy[0], enemy[1], point_target1[0], point_target1[1])
                        if 10 < abs(angle) < 70:
                            left_or_right = 1 if enemy[0] > point_target1[0] else -1
                            shot_offset, v, w = detect_path(point_list, enemy, point_target1,
                                                            'in',
                                                            left_or_right)
                            if shot_offset:
                                return v,shot_offset,w
                            else:
                                continue
                        else:
                            continue
                else:
                    pass
                print('能不能用对面的球把对面打出去')
                sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
                if sorted_fit_shot:
                    print('sorted_fit_shot', sorted_fit_shot)
                    return sorted_fit_shot[0][2],sorted_fit_shot[0][1],sorted_fit_shot[0][3]
                else:
                    pass
                print('下白区内碰不行看看能不能直碰出去')
                feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                if feasibility:
                    return 8,enemy[0]-2.375,0.05
                    
                print('能不能外碰')
                for point_crashed in ally_list:
                    type4 = analyse_point(point_crashed)
                    if type4 in ['three', 'four', 'five', 'six']:
                        if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                         enemy[1])) < 55 and 0.545 < point_crashed[0] < 4.205:
                            left_or_right = 1 if point_crashed[0] > 2.375 else -1
                            shot_offset, v, w = detect_path(point_list, point_crashed,
                                                            (enemy[0], enemy[1]), 'out',
                                                            left_or_right)
                            if shot_offset:
                                print('外碰执行')
                                print('target point:', point_crashed)
                                return v + 1, shot_offset, w
                            else:
                                pass
                        else:
                            continue
                    else:
                        continue
            if color_type in ['top-blue']:
                print('上蓝， 优先双碰')
                double = double_kill(point_list, enemy_list, enemy)
                if double:
                    return double[0][2], double[0][1], double[0][3]
                
                print('上蓝， 双碰不行， 直达')
                feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                if feasibility:
                    return 8, enemy[0] - 2.375, 0
            if color_type in ['bottom-blue']:
                print('下蓝， 优先内碰')
                type_enemy = analyse_point(enemy)
                if type_enemy in ['three', 'four', 'five', 'six']:
                    for point_target1 in point_center:
                        angle = cal_angle(enemy[0], enemy[1], point_target1[0], point_target1[1])
                        if 10 < abs(angle) < 70:
                            left_or_right = 1 if enemy[0] > point_target1[0] else -1
                            shot_offset, v, w = detect_path(point_list, enemy, point_target1,
                                                            'in',
                                                            left_or_right)
                            if shot_offset:
                                return v, shot_offset, w
                            else:
                                continue
                        else:
                            continue
                else:
                    pass
                print('下蓝， 内碰不行， 不管')
            else:
                pass
        if len(enemy_list) == 2:
            if enemy == enemy_list[1]:
                print('次靠近中心的球的处理')
                color_type = analyse_by_color(enemy)
                print(analyse_by_color)
                if color_type in ['top-red', 'bottom-red']:
                    print('红区')
                    print('能不能用对面的球把对面打出去')
                    sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
                    if sorted_fit_shot:
                        print('sorted_fit_shot', sorted_fit_shot)
                        return sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]
                    print('双碰')
                    double = double_kill(point_list, enemy_list, enemy)
                    if double:
                        return double[0][2], double[0][1], double[0][3]
                    else:
                        pass
                    print('直打')
                    feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                    if feasibility:
                        return 8, enemy[0] - 2.375, 0

                    print('外碰')
                    for point_crashed in ally_list:
                        type4 = analyse_point(point_crashed)
                        if type4 in ['three', 'four', 'five', 'six']:
                            if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                             enemy[1])) < 55 and 0.545 < point_crashed[0] < 4.205:
                                left_or_right = 1 if point_crashed[0] > 2.375 else -1
                                shot_offset, v, w = detect_path(point_list, point_crashed,
                                                                (enemy[0], enemy[1]), 'out',
                                                                left_or_right)
                                if shot_offset:
                                    return v + 1, shot_offset, w
                                else:
                                    pass
                            else:
                                continue
                        else:
                            continue
                    print('外碰不行，外撞')
                    shot_offset, v, w = hit_enemy_point(point_list, enemy)
                    if shot_offset:
                        return v, shot_offset, w

                if color_type in ['top-white']:
                    print('上白区')
                    print('双碰')
                    double = double_kill(point_list, enemy_list, enemy)
                    if double:
                        return double[0][2], double[0][1], double[0][3]

                    print('能不能用对面的球把对面打出去')
                    sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
                    if sorted_fit_shot:
                        print('sorted_fit_shot', sorted_fit_shot)
                        return sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]
                    else:
                        pass
                    print('直碰')
                    feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                    if feasibility:
                        return 8, enemy[0] - 2.375, 0

                if color_type in ['bottom-white']:
                    print('下白区')
                    print('内碰')
                    type_enemy = analyse_point(enemy)
                    if type_enemy == 'three' or type_enemy == 'four':
                        for point_target1 in point_center:
                            angle = cal_angle(enemy[0], enemy[1], point_target1[0], point_target1[1])
                            if 10 < abs(angle) < 70:
                                left_or_right = 1 if enemy[0] > point_target1[0] else -1
                                shot_offset, v, w = detect_path(point_list, enemy, point_target1,
                                                                'in',
                                                                left_or_right)
                                if shot_offset:
                                    return v, shot_offset, w
                                else:
                                    continue
                            else:
                                continue
                    else:
                        pass
                    print('能不能用对面的球把对面打出去')
                    sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
                    if sorted_fit_shot:
                        print('sorted_fit_shot', sorted_fit_shot)
                        return sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]
                    else:
                        pass
                if color_type in ['top-blue']:
                    print('上蓝区')
                    print('双碰')
                    double = double_kill(point_list, enemy_list, enemy)
                    if double:
                        return double[0][2], double[0][1], double[0][3]

                if color_type in ['bottom-blue']:
                    print('下蓝区')
                    print('内碰')
                    type_enemy = analyse_point(enemy)
                    if type_enemy in ['three', 'four', 'five', 'six']:
                        for point_target1 in point_center:
                            angle = cal_angle(enemy[0], enemy[1], point_target1[0], point_target1[1])
                            if 10 < abs(angle) < 70:
                                left_or_right = 1 if enemy[0] > point_target1[0] else -1
                                shot_offset, v, w = detect_path(point_list, enemy, point_target1,
                                                                'in',
                                                                left_or_right)
                                if shot_offset:
                                    return v, shot_offset, w
                                else:
                                    continue
                            else:
                                continue
                    print('双碰')
                    double = double_kill(point_list, enemy_list, enemy)
                    if double:
                        return double[0][2], double[0][1], double[0][3]
                    print('能不能用对面的球把对面打出去')
                    sorted_fit_shot = double_shot_extra(point_list, enemy_list, enemy)
                    if sorted_fit_shot:
                        print('sorted_fit_shot', sorted_fit_shot)
                        return sorted_fit_shot[0][2], sorted_fit_shot[0][1], sorted_fit_shot[0][3]
                    else:
                        pass
                    print('直打')
                    feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                    if feasibility:
                        return 8, enemy[0] - 2.375, 0
            else:
                pass
        else:
            print('其余处理')
            color_type = analyse_by_color(enemy)
            if color_type in ['top-red', 'bottom-red']:
                print('红区')
                print('双碰')
                double = double_kill(point_list, enemy_list, enemy)
                if double:
                    return double[0][2], double[0][1], double[0][3]
                return 6, enemy_list[0][0] - 2.375, 0
            else:
                pass
    # 下面是打自己的球的部分
    for ally_point in ally_list:
        print('对敌方球无计可使，看看自家球')
        print('内')
        safe_fit_enemy, danger_fit_enemy = crash_others(point_list, enemy_list)
        if safe_fit_enemy:
            return safe_fit_enemy[0][2], safe_fit_enemy[0][1], safe_fit_enemy[0][3]
        if danger_fit_enemy:
            return danger_fit_enemy[0][2], danger_fit_enemy[0][1], danger_fit_enemy[0][3]


    print('插空')
    interval_point = find_interval(point_list, ally_list, enemy_list)
    if interval_point:
        extra = 0
        for enemy_temp in enemy_list:
            if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                    enemy_temp) == 'top-red':
                if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                    extra = 1
                else:
                    extra = 0
        return 3 + extra, interval_point[0] - 2.375, 0.05
    else:
        pass

def advance_ally(advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point):
    print('ally 领先', advantage_score)
    if advantage_score >= 2:
        print('1.1中线防守')
        return defend_center(point_list, ally_list, min_point,enemy_list)
    else:
        print('2.1清除敌方小偷球')
        sorted_safe_fit_enemy, sorted_danger_fit_enemy = crash_others_line(point_list, enemy_list)
        if sorted_safe_fit_enemy:
            return sorted_safe_fit_enemy[0][2], sorted_safe_fit_enemy[0][1], sorted_safe_fit_enemy[0][3]
        elif sorted_danger_fit_enemy:
            return sorted_danger_fit_enemy[0][2], sorted_danger_fit_enemy[0][1], sorted_danger_fit_enemy[0][3]


        print('2.2把对面斜撞出去')
        shot_offset, v, w = hit_enemy(point_list, enemy_list)
        if shot_offset:
            return v, shot_offset, w
        print('2.2内碰')
        safe_fit_enemy, danger_fit_enemy = crash_others(point_list, enemy_list)
        if safe_fit_enemy:
            return safe_fit_enemy[0][2], safe_fit_enemy[0][1], safe_fit_enemy[0][3]
        if danger_fit_enemy:
            return danger_fit_enemy[0][2], danger_fit_enemy[0][1], danger_fit_enemy[0][3]

        print('2.3外碰')
        safe_fit_ally, danger_fit_ally = crash_us(point_list, ally_list)
        if safe_fit_ally:
            return safe_fit_ally[0][2], safe_fit_ally[0][1], safe_fit_ally[0][3]
        if danger_fit_ally:
            return danger_fit_ally[0][2], danger_fit_ally[0][1], danger_fit_ally[0][3]

        print('2.6直打')
        for enemy in enemy_list:
            if get_distance_circle(enemy[0], enemy[1]) < 1.83 + stone_r:
                feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                benefit = detect_crash_influence(ally_list, enemy[0], enemy[1])
                print('feasibility', feasibility)
                print('benefit', benefit)
                if feasibility and benefit:
                    print('直打')
                    return 8, enemy[0] - 2.375, 0
                else:
                    pass
            else:
                pass
        print('2.4插空')
        interval_point = find_interval(point_list, ally_list, enemy_list)
        if interval_point:
            extra = 0
            for enemy_temp in enemy_list:
                if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                        enemy_temp) == 'top-red':
                    if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                        extra = 1
                    else:
                        extra = 0
            return 3 + extra, interval_point[0] - 2.375, 0.05
        else:
            pass
        print('2.7中线防守')
        return defend_center(point_list, ally_list, min_point,enemy_list)