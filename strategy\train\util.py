import random
import math

House_x = 2.375
House_y = 4.88
R = 1.83
stone_r = 0.145
shot_num = 0  # 加个下划线没报错（狗头）
a = 9 / 55.2
point_center = [
    [2.375, 4.88],
    [2.375 + 0.21, 4.88 - 0.21],
    [2.375 + 0.21, 4.88 + 0.21],
    [2.375 - 0.21, 4.88 + 0.21],
    [2.375 - 0.21, 4.88 - 0.21],
    [2.375 + 0.21, 4.88],
    [2.375 - 0.21, 4.88],
    [2.375, 4.88 - 0.21],
    [2.375, 4.88 + 0.21],
]
point_center_line = [
    [2.375, 6.2],
    [2.375, 6.3],
    [2.375, 6.4],
    [2.375, 6.5],
    [2.375, 6.6],
    [2.375, 6.7],
    [2.375, 6.8],
]
center_x = 2.375  # 大本营中心位置坐标
center_y = 4.88
House_R = 1.830  # 大本营半径
Stone_R = 0.145  # 冰壶半径
s_R = 0.61 


def get_distance_circle(x, y):
    distance = math.sqrt((x - House_x) ** 2 + (y - House_y) ** 2)
    return distance


def get_distance(x1, y1, x2, y2):
    distance = math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2)
    return distance


#   下面1/0改成了True/False
def is_in_house(x, y):
    distance = get_distance_circle(x, y)
    if distance - stone_r < R:
        return True
    else:
        return False


def cal_angle_dis(x1, y1, x2, y2):
    dis = get_distance(x1, y1, x2, y2)
    if y1 != y2:
        angle = math.atan((x2 - x1) / (y1 - y2)) * 180 / math.pi if dis > 0.1 else 90.0
        angle = -angle if y1 < y2 else angle
        # 返回x/y的正切值
        return angle, dis


def cal_angle(x1, y1, x2, y2):
    dis = get_distance(x1, y1, x2, y2)
    if y1 != y2:
        if dis > 0.1:
            # 计算反正切并转化为角度制
            return math.atan((x2 - x1) / (y1 - y2)) * 180 / math.pi
    return 90.0


# # 这个函数中的state_list按照我新加的的那个函数的形式处理了，顺便改了个名字好分辨,顺便sign也改成了true(有)/false（无）
# def stone_on_trail(x, y, goal_x, goal_y, point_list):  # 检测斜碰路径上是否有球挡着
#     sign = False  # 路径上是否有遮挡球
#     k = abs((y - goal_y) / (goal_x - x))
#     angle = math.atan(k)
#     y_1, y_2 = y + stone_r / math.cos(angle), y - stone_r / math.cos(angle)
#     goal_y_1, goal_y_2 = goal_y + stone_r / math.cos(angle), goal_y - stone_r / math.cos(angle)
#     for point in point_list:
#         # point格式[x,y]
#         if (point[0] == x and point[1] == y) or (point[0] == goal_x and point[1] == goal_y):  # 是击打球或者目标击打球，跳过
#             continue
#         elif not x <= point[0] <= goal_x:
#             continue
#         else:  # 在两球x之间的，进行判定
#             y_on_line = y + (point[0] - x) * k  # 该球x对应的两球连线的y值
#             if y_on_line >= point[1]:  # 在两球连线的上方
#                 d = abs((point[1] - (y_1 - (point[0] - x) * k)) * math.cos(angle))  # 该球圆心到上界线的距离
#                 if d > stone_r + 0.03:  # 在上界线外，能通过，跳过
#                     continue
#                 else:
#                     sign = True
#             else:
#                 d = abs((point[1] - (y_2 - (point[0] - x) * k)) * math.cos(angle))
#                 if d > stone_r + 0.03:
#                     continue
#                 else:
#                     sign = True
#     return sign
#

# 左右均可
def cal_delta_x_inside(X, Y, left_or_right, target_x=House_x,target_y=House_y):
    # 内侧击打,默认打中间X，Y是被碰的球坐标(使用时应该传场上一个球的坐标,该球应为敌方球或者我方被放弃的球)
   # print('cal_delta_x_inside的输入', X, Y, left_or_right, target_x, target_y,left_or_right)
    # 下面是计算修正值
    angle_one = math.atan((Y - target_y) / abs(X - target_x))

    angle_two = math.asin((2 * stone_r) / (get_distance(X, Y, target_x, target_y)))
    angle_three = ((math.pi / 2) - angle_one - angle_two)
    offset_x = get_distance(X, Y, target_x, target_y) * math.cos(angle_two) * math.sin(angle_three)
    offset_y = get_distance(X, Y, target_x, target_y) * math.cos(angle_two) * math.cos(angle_three)
    crash_x = target_x + offset_x * left_or_right
    crash_y = target_y + offset_y
    dis = get_distance(crash_x, crash_y, target_x + 0.0237, target_y)
    #print('dis==', dis)
    # factor=3.6+0.3+math.sin(angle_three)
    # factor = 3.8 + 0.3 * math.sin(angle_three)
    # factor=3.6+0.2*math.sin(angle_three)+0.2*dis-0.08*math.cos(angle_three)#2.7,0.5偏差较大，角度越大，dis影响越大
    factor = 3.6 + 0.3 * math.sin(angle_three) + 0.16 * dis - 0.15 * math.cos(angle_three)  # 2.7,0.5有问题，角度大距离大会飞出去
    # factor = 3.8 + 0.02 * math.sin(angle_three) + 0.15 * dis -0.2*math.cos(angle_three)
    if left_or_right == -1:
        factor += 0.1 * math.sin(angle_three) + 0.03 * dis + 0.3
    if angle_three * 180 / math.pi < 11:
        factor -= 0.05 * math.cos(angle_three) * dis / 1.83
    if angle_three < 50 and dis < 1.9 and left_or_right == 1:
        factor += 0.15 * dis
    if 60 < angle_three < 67 and dis > 1.2 and left_or_right == 1:
        factor -= 0.25 * dis
    if 5 < angle_three < 50 and dis < 1.83 and left_or_right == -1:
        factor += 0.2 * math.cos(angle_three)
    dis1 = factor * dis / math.cos(angle_three) ** 2
    y0 = Y - dis1
    v0 = move(y0)
    #print(angle_three * 180 / math.pi)
    delta = (0.0235 * (32.48 - Y) / (32.48 - y0)) * (1 + 0.6 * math.cos(angle_three) ** 2) + 0.006 * (
            (90 - angle_three) / 90) if left_or_right == 1 else (0.0237 * (32.48 - Y) / (32.48 - y0)) * (
            1 + 0.35 * math.cos(angle_three) ** 2) - 0.006 * (90 - angle_three) / 90
    if angle_three < 6 and left_or_right == -1:
        delta -= 0.01 * math.cos(angle_three)
    if angle_three > 6 and left_or_right == -1:
        delta += 0.012 * math.cos(angle_three)
  #  print(delta)
    shot_offset = target_x + offset_x * left_or_right - 2.375 + delta
  #  print("  angle1:", angle_one * 180 / math.pi, "   angle2:", angle_two * 180 / math.pi, "   angle3:",angle_three * 180 / math.pi)
    return shot_offset, v0, 0, crash_x, crash_y


# 左右均可
def cal_delta_x_outside(X, Y, left_or_right, target_x=House_x,
                        target_y=House_y, clear=False):  # 外侧击打,默认打中间X，Y是被碰的球坐标(使用时应该传场上一个球的坐标,该球应为我方铺垫球):
    # 根据三角形相似
    offset_x = (get_distance(X, Y, target_x, target_y) + (2 * stone_r)) * abs(X - target_x) / get_distance(X, Y,
                                                                                                           target_x,
                                                                                                           target_y)
    offset_y = (get_distance(X, Y, target_x, target_y) + (2 * stone_r)) * abs(Y - target_y) / get_distance(X, Y,
                                                                                                           target_x,
                                                                                                           target_y)
    crash_y = target_y + offset_y
    # shot_offset射击偏移量，已经处理好的可以直接send出去的
    # crash_x发生完全弹性碰撞前一瞬间球的x坐标
    # crash_y发生完全弹性碰撞前一瞬间球的y坐标(这两个参数是用来计算直行过程中有没有阻挡的)
    angle, dis = cal_angle_dis(X, Y, target_x, target_y)
    v, shot_offset, w = alpha_no_obst(X, Y, target_x, target_y, angle, dis, clear)
    crash_x = 2.375 + shot_offset
    return shot_offset, v, w, crash_x, crash_y


# 传的angle参数去掉了
def detect_path(point_list, state, state_goal, in_or_out,left_or_right,clear=False):
    # point_list是所有球的信息，state是传入的球的信息，state_goal是要击打的位置的信息，in_or_out是击打方式，内侧击打(-1)还是外侧击打(1)，left_or_right表示目标球在击打球的左边还是右边
    # 外侧击打
    if in_or_out == 'out':
        shot_offset, v, w, crash_x, crash_y = cal_delta_x_outside(state[0], state[1], left_or_right, state_goal[0],
                                                                  state_goal[1], clear)
        feasibility, obs = check_feasibility(point_list, crash_x, crash_y, state_goal[0], state_goal[1], state[0],
                                             state[1])
      #  print('waifeasibility', feasibility)
      #  print('waiobs', obs)
        if feasibility:
            return shot_offset, v, w
        else:
            return None, 0, 0
    # 内侧击打
    else:
        shot_offset, v, w, crash_x, crash_y = cal_delta_x_inside(state[0], state[1], left_or_right, state_goal[0],state_goal[1])
        # 检测路径是否可行(包括碰撞前后)
        feasibility, obs = check_feasibility(point_list, crash_x, crash_y, state_goal[0], state_goal[1])
      #  print('neifeasibility', feasibility)
     #   print('neiobs', obs)
        if feasibility:
            return shot_offset, v, w
        else:
            return None, 0, 0


# 检查路径是否可行
def check_feasibility(point_list, crash_x, crash_y, target_x, target_y, tipped_x=0.0, tipped_y=0.0):
    feasibility = True
    obs = []
    # 直行过程是否有障碍物
    for point in point_list:
        X, Y = point
        if Y + stone_r / 2 > crash_y:
            if (crash_x - 2 * stone_r - 0.05) < X < (crash_x + 2 * stone_r + 0.05):
                feasibility = False
                obs.append(point)
   # print('直行过程中的障碍物', obs)
    # 碰撞后是否有障碍物
    # 内碰
    if tipped_x == 0.0 and tipped_y == 0.0:  # 外侧击打与内侧传的位置信息不一样
        obs_list = find_obs(point_list, crash_x, crash_y, target_x, target_y, factor_l=2.1, factor_r=2.1)
        if obs_list:
            feasibility = False
            obs.append(obs_list)
    else:
        obs_list = find_obs(point_list, tipped_x, tipped_y, target_x, target_y, factor_l=2.2, factor_r=2.2)
        if obs_list:
            feasibility = False
            obs.append(obs_list)
  #  print('总过程中的障碍物', obs)
    return feasibility, obs


# 整理信息,这个函数是用来处理位置信息的，其中turn传的是2023_1中的self.player_is_init，其中self.player_is_init True为先手，False为后手
# state_list为self.position
def organize_position(turn, state_list):
    advantage_team = ''  # 现在场上占优势的队伍
    all_in_house = []  # 所有在大本营中的球,下面两个以此类推，形式为[[2,3],[2,4],[4,3],[5,6],[8,7]]这样，这三个列表的长度固定，分别为16.8，8
    ally_in_house = []
    enemy_in_house = []
    score = 0  # 当前领先分数（包括自己和对面）
    # 将state_list中的每两个数字作为一个点的坐标，并构建二维数组
    point_list = [[state_list[i], state_list[i + 1]] for i in range(0, len(state_list), 2)]
    # 将points中的奇数序列和偶数序列分开
    if turn:  # 先手
        ally_list = point_list[::2]  # 奇数序列
        enemy_list = point_list[1::2]  # 偶数序列
    else:
        enemy_list = point_list[::2]  # 奇数序列，步长为2
        ally_list = point_list[1::2]  # 偶数序列，从索引1开始，步长为2
    # 过滤掉为[0, 0]的坐标
    filtered_point_list = [item for item in point_list if item != [0, 0]]
    filtered_ally_list = [item for item in ally_list if item != [0, 0]]
    filtered_enemy_list = [item for item in enemy_list if item != [0, 0]]
    min_dis_ally = math.inf  # 我方最靠近中心的球距离中心的距离，下同
    min_point_ally = None  # 我方最靠近中新的球的坐标[2,3]这样
    for point in filtered_ally_list:
        dis = get_distance_circle(point[0], point[1])
        if is_in_house(point[0], point[1]):
            ally_in_house.append(point)
        if dis < min_dis_ally and point != [0, 0]:
            min_dis_ally = dis
            min_point_ally = point
    min_dis_enemy = math.inf
    min_point_enemy = None
    for point in filtered_enemy_list:
        dis = get_distance_circle(point[0], point[1])
        if is_in_house(point[0], point[1]):
            enemy_in_house.append(point)
        if dis < min_dis_enemy and point != [0, 0]:
            min_dis_enemy = dis
            min_point_enemy = point
    min_dis = math.inf
    min_point = None
    for point in filtered_point_list:
        dis = get_distance_circle(point[0], point[1])
        if is_in_house(point[0], point[1]):
            all_in_house.append(point)
        # 如果当前距离小于最小距离，更新最小距离和对应的点
        if dis < min_dis and is_in_house(point[0], point[1]):
            min_dis = dis
            min_point = point
    if min_point in filtered_ally_list:
        advantage_team = 'ally'
        for point in filtered_ally_list:
            if point != [0, 0]:
                dis = get_distance_circle(point[0], point[1])
                if dis < min_dis_enemy and is_in_house(point[0], point[1]):
                    score = score + 1
            else:
                pass
    elif min_point in filtered_enemy_list:
        advantage_team = 'enemy'
        for point in filtered_enemy_list:
            if point != [0, 0]:
                dis = get_distance_circle(point[0], point[1])
                if dis < min_dis_ally and is_in_house(point[0], point[1]):
                    score = score + 1
            else:
                pass
    # 下方返回值分别为，当前优势队伍，该队伍领先分数，场上所有球的坐标（按照发球顺序的二维列表），敌方和我方在场上所有球的坐标（二维列表），距离中心最近的球的坐标（不分敌我）
    # 其他想要的可以自己返回
    sorted_list_ally = sorted(filtered_ally_list, key=lambda point: get_distance(point[0], point[1], 2.375, 4.88))
    sorted_list_enemy = sorted(filtered_enemy_list, key=lambda point: get_distance(point[0], point[1], 2.375, 4.88))
    return advantage_team, score, filtered_point_list, sorted_list_ally, sorted_list_enemy, min_point

# 计算速度，此函数传递的y为被碰球的y坐标
"""def move(y):
    friction = 0.02026 - 0.000614 * y
    v = math.sqrt(19.62 * friction * (32.48 - y))
    return v"""

"""def move(y):
    x = y  # 自变量为 y
    return (
        1.62684164e+00 +          # 常数项（y^0）
        1.45876351e+00 * x +      # y^1 项
        2.56533845e-01 * x**2 +   # y^2 项
        -8.12394624e-01 * x**3 +  # y^3 项
        4.48717029e-01 * x**4 +   # y^4 项
        -1.31778001e-01 * x**5 +  # y^5 项
        2.38247912e-02 * x**6 +   # y^6 项
        -2.74051781e-03 * x**7 +  # y^7 项
        1.96055716e-04 * x**8 +   # y^8 项
        -7.96966630e-06 * x**9 +  # y^9 项
        1.40807935e-07 * x**10    # y^10 项
    )"""

def move(y):
    return 3.5381707 - y / 8.9938
def hit_enemy_into_house(point_list, enemy_list):
    print('把敌方壶打进营的函数正在执行')
    for enemy in enemy_list:
        # 遍历目标点列表，这里以 point_center 为例
        for point_target in point_center:
            angle = cal_angle(enemy[0], enemy[1], point_target[0], point_target[1])
            # 限制角度范围，确保击打具有一定的合理性
            if 10 < abs(angle) < 70:
                # 判断敌方球在目标点的左边还是右边
                left_or_right = 1 if enemy[0] > point_target[0] else -1
                # 尝试计算击打参数并检查路径可行性
                shot_offset, v, w = detect_path(point_list, enemy, point_target, 'in', left_or_right)
                if shot_offset:
                    return shot_offset, v, w
    return None
def alpha_no_obst(x, y, target_x=House_x, target_y=House_y, angle=0.0, dis=0.0, clear=False):
    if clear:
        if angle < -10:
            target_x = target_x + 0.2
        elif -7 < angle < -6:
            target_x = target_x - 0.189
        elif angle < 0:
            target_x = target_x - 0.21
        elif angle < 10:
            target_x = target_x + 0.205
        else:
            target_x = target_x - 0.2
        angle = cal_angle(x, y, target_x, target_y)
    if dis and y != target_y:
        if angle:
            target_x += 0.035 if target_x < x else -0.035
        else:
            target_x = x
        if angle <= -15:
            a = 0.3
        elif angle <= 0:
            a = 0.29
        elif angle <= 15:
            a = 0.295
        elif angle <= 25:
            a = 0.31
        elif angle <= 35:
            a = 0.28
        else:
            a = 0.29
        h_x, w = x + a * (x - target_x) / math.sqrt((target_x - x) ** 2 + (target_y - y) ** 2) - 2.35, 0.05
    else:
        h_x, w = x - 2.375, 0.05
    if y > 6.5:
        if math.fabs(angle) < 25:
            a = 0.05
        elif math.fabs(angle) < 35:
            a = 0.08
        else:
            a = 0.12
    else:
        if dis < 0.5:
            a = 0 if math.fabs(angle) < 25 else 0.01
        else:
            a = 0.01 if math.fabs(angle) < 25 else 0.03
    u = move(y - 2.75 * dis - a * math.fabs(angle))
    return u, h_x, w


def random_push():  # 随机放球
    rand_x = round(random.random() * 2.5 - 1.25, 4)
    rand_x = 0.5 if (0 < rand_x < 0.5) else rand_x
    rand_x = -0.5 if (-0.5 < rand_x < 0) else rand_x
    rand_v = round(random.uniform(2.7, 2.9), 4)
    return rand_v, rand_x, 0


# 目标与发射点间障碍物（有修改）
def find_obs(point_list, x, y, target_x, target_y, factor_l=2.0, factor_r=2.0):
    obs_list = []
    angle = cal_angle(x, y, target_x, target_y)
    target_x -= 2 * stone_r * math.sin(angle * math.pi / 180)
    target_y += 2 * stone_r * math.cos(angle * math.pi / 180)
    s_width = [x - stone_r * factor_l, x + stone_r * factor_r]
    t_width = [target_x - stone_r * factor_l, target_x + stone_r * factor_r]
    for point in point_list:
        # 遍历所有球的xy坐标
        X, Y = point
        # 在左边or右边
        sign = 1 if target_x - x >= 0 else -1
        if sign == 1:
            if s_width[0] < X < t_width[1]:
                if target_y < Y < y:
                    if sign * (target_x - x) > 0.1:
                        if sign * (two_point_eq(s_width[1], y, t_width[1], target_y, X) - Y) > 0:
                            if sign * (Y - two_point_eq(s_width[0], y, t_width[0], target_y, X)) > 0:
                                obs_list.append([X, Y])
                    if sign * (target_x - x) <= 0.1:
                        obs_list.append([X, Y])
        if sign == -1:
            if t_width[0] < X < s_width[1]:
                if target_y < Y < y:
                    if sign * (target_x - x) > 0.1:
                        if sign * (two_point_eq(s_width[1], y, t_width[1], target_y, X) - Y) > 0:
                            if sign * (Y - two_point_eq(s_width[0], y, t_width[0], target_y, X)) > 0:
                                obs_list.append([X, Y])
                    if sign * (target_x - x) <= 0.1:
                        obs_list.append([X, Y])
    return obs_list


def two_point_eq(x1, y1, x2, y2, x):
    return (x * (y2 - y1) - x1 * y2 + y1 * x2) / (x2 - x1)

#把球分区
def analyse_point(point):
    type = [
        'one',
        'two',
        'three',
        'four',
        'five',
        'six'
    ]
    if point[1] < 5.4:
        if point[0] < 2.375:
            type_point = type[0]
        else:
            type_point = type[1]
    elif 5.4 <= point[1] < 6.7:
        if point[0] < 2.375:
            type_point = type[2]
        else:
            type_point = type[3]
    elif 6.7 <= point[1] < 8:
        if point[0] < 2.375:
            type_point = type[4]
        else:
            type_point = type[5]
    else:
        type_point = 'too_far'
    return type_point


# 到达某点(x,y)是否有阻碍
def check_path_straight(point_list, x, y, factor=0.015):
    feasibility = True
    obs_list = []
    for point in point_list:
        if point[1] == y:
            continue
        if point[1] + stone_r > y:
            if abs(x - point[0]) <= 2 * stone_r + factor:
                obs_list.append(point)
                feasibility = False
  #  print('check_path_straight', obs_list)
    return feasibility, obs_list

# 插空专用
def check_path_straight_strict(point_list, x):
    feasibility = True
    obs_list = []
    for point in point_list:
        if point[1] + stone_r > 4.88:
            if abs(x - point[0]) > 2 * stone_r:
                pass
            else:
                obs_list.append(point)
                feasibility = False
    return feasibility, obs_list


# 检测碰撞后的影响
def detect_crash_influence(ally_list, x, y):
    benefit = True
    for ally in ally_list:
        if ally[1] < y:
            if abs(x - ally[0]) < 2 * stone_r:
                benefit = False
    return benefit
# 检测是不是好的外碰点/内碰点
def detect_position_safety(point_list, x):
    point_in_center_line = []
    for point in point_list:
        if (4.88 - 0.61) < point[1]:
            if (2.375 - 0.63) < point[0] < 2.375 + 0.63:
                point_in_center_line.append(point)
    if point_in_center_line:
        # 对列表按照横坐标进行排序
        sorted_point_in_center_line = sorted(point_in_center_line, key=lambda point_in_center: point_in_center[0])
        prohibited_area = []
        for i in range(len(sorted_point_in_center_line) - 1):
            x1, _ = point_list[i]
            x2, _ = point_list[i + 1]
            x_difference = x2 - x1
            if x_difference > 4 * stone_r:
                prohibited_area.append([x1 + stone_r, x2 - stone_r])
            else:
                continue
        all_area = [sorted_point_in_center_line[0][0] - stone_r, sorted_point_in_center_line[-1][0] + stone_r]
        safe = False
        if all_area[0] < x < all_area[1]:
            if prohibited_area:
                for danger in prohibited_area:
                    if danger[0] < x < danger[1]:
                        return False
            else:
                return True
        else:
            return False
    return True

# 中线上最上方的球是谁的
def top_curling_center(point_list, ally_list, enemy_list):
    point_in_center_line = []
    for point in point_list:
        if (4.88 - 0.61) < point[1]:
            if (2.375 - 2 * stone_r) < point[0] < (2.375 + 2 * stone_r):
                point_in_center_line.append(point)
    if point_in_center_line:
        # 对列表按照纵坐标进行排序
        sorted_point_in_center_line = sorted(point_in_center_line, key=lambda point_in_center: point_in_center[1])
        if sorted_point_in_center_line[0] in ally_list:
            top_curling = 'ally'
            return top_curling
        if sorted_point_in_center_line[0] in enemy_list:
            top_curling = 'enemy'
            return top_curling
    else:
        return None


# 把自己适合的球撞进去(外碰,13种情况)
def crash_us(point_list, ally_list):
    print('外碰函数正在执行')
    safe_fit_ally = []
    danger_fit_ally = []
    for ally in ally_list:
        type_ally = analyse_point(ally)
        if type_ally in ['three', 'four', 'five', 'six']:
            # 理想情况
            for point_target1 in point_center:
                angle = cal_angle(ally[0], ally[1], point_target1[0], point_target1[1])
                if abs(angle) < 55 and 0.545 < ally[0] < 4.205:
                    if 45 < abs(angle) < 55:
                        extra = 1.5
                    else:
                        extra = 0
                    safe = detect_position_safety(point_list, point_target1[0])
                    if safe:
                        left_or_right = 1 if ally[0] > 2.375 else -1
                        if get_distance(ally[0], ally[1], 2.375, 4.88) < 0.6:
                            continue
                        else:
                            shot_offset, v, w = detect_path(point_list, ally,
                                                            point_target1, 'out',
                                                            left_or_right)
                            if shot_offset:
                                safe_fit_ally.append([angle, shot_offset, v + extra, w])
                            else:
                                continue
                    else:
                        continue
            # 不理想
            for point_target1 in point_center:
                angle = cal_angle(ally[0], ally[1], point_target1[0], point_target1[1])
                if abs(angle) < 55 and 0.545 < ally[0] < 4.205:
                    if 45 < abs(angle) < 55:
                        extra = 1.5
                    else:
                        extra = 0
                    left_or_right = 1 if ally[0] > 2.375 else -1
                    if get_distance(ally[0], ally[1], 2.375, 4.88) < 0.6:
                        continue
                    else:
                        shot_offset, v, w = detect_path(point_list, ally,
                                                        point_target1, 'out',
                                                        left_or_right)
                        if shot_offset:
                            danger_fit_ally.append([angle, shot_offset, v + extra, w])
                        else:
                            continue
        else:
            continue
    sorted_safe_fit_ally = []
    sorted_danger_fit_ally = []
    if safe_fit_ally:
        sorted_safe_fit_ally = sorted(safe_fit_ally, key=lambda message: abs(message[0]))
    if danger_fit_ally:
        sorted_danger_fit_ally = sorted(danger_fit_ally, key=lambda message: abs(message[0]))
    return sorted_safe_fit_ally, sorted_danger_fit_ally


# 把自己的球内碰进去(内碰,13种情况)
def crash_others(point_list, enemy_list):
    print('内碰函数正在执行')
    safe_fit_enemy = []
    danger_fit_enemy = []
    for enemy in enemy_list:
        type_enemy = analyse_point(enemy)
        if type_enemy in ['three', 'four', 'five', 'six']:
            for point_target1 in point_center:
                angle = cal_angle(enemy[0], enemy[1], point_target1[0], point_target1[1])
                if 10 < abs(angle) < 70:
                    safe = detect_position_safety(point_list, point_target1[0])
                    if safe:# 理想情况
                        left_or_right = 1 if enemy[0] > point_target1[0] else -1
                        shot_offset, v, w = detect_path(point_list, enemy, point_target1,
                                                        'in',
                                                        left_or_right)
                        if shot_offset:
                            safe_fit_enemy.append([angle, shot_offset, v, w])

                    else:# 不理想
                        left_or_right = 1 if enemy[0] > point_target1[0] else -1
                        shot_offset, v, w = detect_path(point_list, enemy, point_target1,'in',left_or_right)
                        if shot_offset:
                            danger_fit_enemy.append([angle, shot_offset, v, w])

    sorted_safe_fit_enemy = []
    sorted_danger_fit_enemy = []
    if safe_fit_enemy:
        sorted_safe_fit_enemy = sorted(safe_fit_enemy, key=lambda message: abs(message[0]))
    if danger_fit_enemy:
        sorted_danger_fit_enemy = sorted(danger_fit_enemy, key=lambda message: abs(message[0]))
    return sorted_safe_fit_enemy, sorted_danger_fit_enemy

def crash_others_line(point_list, enemy_list):
    print('边线内碰函数正在执行')
    safe_fit_enemy = []
    danger_fit_enemy = []
    for enemy in enemy_list:
        if enemy[1] >= 4.88 and (enemy[0] < 0.545 or enemy[0] > 4.205):
            for point_target1 in point_center:
                angle = cal_angle(enemy[0], enemy[1], point_target1[0], point_target1[1])
                if 10 < abs(angle) < 70:
                    safe = detect_position_safety(point_list, point_target1[0])
                    if safe:# 理想情况
                        left_or_right = 1 if enemy[0] > point_target1[0] else -1
                        shot_offset, v, w = detect_path(point_list, enemy, point_target1,
                                                        'in',
                                                        left_or_right)
                        if shot_offset:
                            safe_fit_enemy.append([angle, shot_offset, v, w])

                    else:# 不理想
                        left_or_right = 1 if enemy[0] > point_target1[0] else -1
                        shot_offset, v, w = detect_path(point_list, enemy, point_target1,'in',left_or_right)
                        if shot_offset:
                            danger_fit_enemy.append([angle, shot_offset, v, w])

    sorted_safe_fit_enemy = []
    sorted_danger_fit_enemy = []
    if safe_fit_enemy:
        sorted_safe_fit_enemy = sorted(safe_fit_enemy, key=lambda message: abs(message[0]))
    if danger_fit_enemy:
        sorted_danger_fit_enemy = sorted(danger_fit_enemy, key=lambda message: abs(message[0]))
    return sorted_safe_fit_enemy, sorted_danger_fit_enemy

# 插空(广义上来说还包括传击)
def find_interval(point_list, ally_list, enemy_list):
    print('插空函数正在执行')
    point_list.append([2.375 - 0.61 - stone_r, 6])
    point_list.append([2.375 + 0.61 + stone_r, 6])
    # 首先看看能不能插空
    point_in_center_line = []#找中线壶
    for point in point_list:
        if 4.88 + stone_r < point[1]:
            if (2.375 - 0.61 * 2 - 2 * stone_r) < point[0] < 2.375 + 0.61 * 2 + 2 * stone_r:
                point_in_center_line.append(point)
    interval_point = []
    if point_in_center_line:
        # 对列表按照横坐标进行排序
        sorted_point_in_center_line = sorted(point_in_center_line, key=lambda point_in_center: point_in_center[0])
        for i in range(len(sorted_point_in_center_line) - 1):
            x1, _ = sorted_point_in_center_line[i]
            x2, _ = sorted_point_in_center_line[i + 1]
            x_difference = x2 - x1
            if x_difference > 4 * stone_r+0.04:#找可以插空的位置
                interval_point.append((x1 + x2) / 2)
    else:
        # 中线没球,则自由打到中心线
        x_list = []
        for i in (0, 0.1, -0.1, 0.2, -0.2, 0.3, -0.3, 0.4, -0.4, 0.5, -0.5, 0.6, -0.6):
            x_list.append(2.375 + i)
        for x_crash in x_list:
            feasibility, obs_list = check_path_straight(ally_list, x_crash, 4.88 - stone_r)
            if feasibility:
                interval_point.append(x_crash)
    sorted_interval_point = []
    if interval_point:
        sorted_interval_point = sorted(interval_point, key=lambda interval: abs(interval - 2.375))
    return sorted_interval_point


# 防守
def defend_center(point_list, ally_list, min_point,enemy_list):
    print('扩大得分圈')
    if  enemy_list:
        enemy_circle = []
        for enemy in enemy_list:
            if get_distance_circle(enemy[0], enemy[1]) <= House_R + stone_r:
                enemy_circle.append(enemy)

        if enemy_circle:
            enemy = enemy_circle[0]
            print('优先打双碰球')
            double = double_kill(point_list, enemy_list, enemy)
            if double:
                return double[0][2], double[0][1], double[0][3]

            print('外碰')
            ally_list_sort = sorted(ally_list, key=lambda ally_sort: abs(
                    cal_angle(ally_sort[0], ally_sort[1], enemy[0], enemy[1])))
            for point_crashed in ally_list_sort:
                type4 = analyse_point(point_crashed)
                if type4 in ['three', 'four', 'five', 'six']:
                    if abs(cal_angle(point_crashed[0], point_crashed[1], enemy[0],
                                         enemy[1])) < 55 and 0.545 < point_crashed[0] < 4.205:
                        left_or_right = 1 if point_crashed[0] > 2.375 else -1
                        shot_offset, v, w = detect_path(point_list, point_crashed,
                                                (enemy[0], enemy[1]), 'out',left_or_right)
                        if shot_offset:
                            return v + 1, shot_offset, w

            print('外撞')
            shot_offset, v, w = hit_enemy_point(point_list, enemy)
            if shot_offset:
                return v, shot_offset, w

            print('直打')
            feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
            if feasibility:
                return 8, enemy[0] - 2.375, 0.05

            print('插空')
            interval_point = find_interval(point_list, ally_list, enemy_list)
            if interval_point:
                extra = 0
                for enemy_temp in enemy_list:
                    if enemy_temp[1] > 4.88 - 2.1 * stone_r and analyse_by_color(
                                        enemy_temp) == 'top-red':
                        if abs(interval_point[0] - enemy_temp[0]) < stone_r / 2:
                            extra = 1
                        else:
                            extra = 0
                return 3 + extra, interval_point[0] - 2.375, 0
            sorted_safe_fit_enemy, sorted_danger_fit_enemy = crash_others_line(point_list, enemy_list)
            if sorted_safe_fit_enemy:
                return sorted_safe_fit_enemy[0][2], sorted_safe_fit_enemy[0][1], sorted_safe_fit_enemy[0][3]
            elif sorted_danger_fit_enemy:
                return sorted_danger_fit_enemy[0][2], sorted_danger_fit_enemy[0][1], sorted_danger_fit_enemy[0][3]

    print('防守中线函数正在执行')
    #找在中线，并且在得分区之外的球
    point_in_center_line = []
    for point in point_list:
        if (point[1] > 4.88 + 4 * stone_r) and (2.375 - stone_r - 0.02 < point[0] < 2.375 + stone_r + 0.02):
            point_in_center_line.append(point)
        
    sorted_point_in_center_line = []
    if point_in_center_line:
        sorted_point_in_center_line = sorted(point_in_center_line, key=lambda point_in_center: point_in_center[1])

    if sorted_point_in_center_line:
        shot_offset = stone_r
        if get_distance_circle(min_point[0], min_point[1]) < 0.6:
            shot_offset = stone_r if min_point[0] > 2.375 else -stone_r

        v = move(sorted_point_in_center_line[-1][1]) - 0.17 if move(
            sorted_point_in_center_line[-1][1]) - 0.17 > 2.5 else 2.5

        return v, shot_offset, -0.05
    else:
        shot_offset = -stone_r
        if get_distance_circle(min_point[0], min_point[1]) < 0.6:
            shot_offset = stone_r if min_point[0] > 2.375 else -stone_r

        return 2.8, shot_offset, -0.05
 
# 把对面球打出去
def hit_enemy(point_list, enemy_list):
    print('hit_enemy函数正在执行')
    for enemy in enemy_list:
        if get_distance_circle(enemy[0], enemy[1]) < 1.2:
            for x in range(0, 476, 10):
                value = x / 100
                angle = cal_angle(enemy[0], enemy[1], value, 3)
                if abs(angle) < 55:
                    left_or_right = 1 if enemy[0] > 2.375 else -1
                    shot_offset, v, w = detect_path(point_list, enemy,
                                                    (value, 3), 'out',
                                                    left_or_right)
                    if shot_offset:
                        return shot_offset, v + 2, w
                    else:
                        pass
                else:
                    continue
        else:
            continue
    return None, 0, 0


def hit_enemy_point(point_list, enemy):
    print('hit_enemy_point函数正在执行')
    if get_distance_circle(enemy[0], enemy[1]) < 1.2:
        for x in range(0, 476, 10):
            value = x / 100
            angle = cal_angle(enemy[0], enemy[1], value, 3)
            if abs(angle) < 55:
                left_or_right = 1 if enemy[0] > 2.375 else -1
                shot_offset, v, w = detect_path(point_list, enemy,
                                                (value, 3), 'out',
                                                left_or_right)
                if shot_offset:
                    return shot_offset, v+1, w
                else:
                    pass
            else:
                continue
    return None, 0, 0
def hit_enemy_point_In(point_list, enemy):
    print('hit_enemy_point_In函数正在执行')

    return None, 0, 0
def hit_enemy_line(point_list, enemy_list):
    print('hit_enemy_line函数正在执行')
    for enemy in enemy_list:
        if (2.375 - 0.61 * 2 - 2 * stone_r) < enemy[0] < 2.375 + 0.61 * 2 + 2 * stone_r and enemy[1] >  4.88 + 1.83 + stone_r:#在中线附近且在营外的敌人球
            value =  stone_r if enemy[0] < 2.375 else 2.375 + 1.83#打到左右边界
            angle = cal_angle(enemy[0], enemy[1], value, 4.88+ R)
            if abs(angle) < 55:
                left_or_right = 1 if enemy[0] > 2.375 else -1
                shot_offset, v, w = detect_path(point_list, enemy,
                                               (value, 4.88+R), 'out',left_or_right)
                if shot_offset:
                    return shot_offset, v, w
                else:
                    pass
            else:
                continue
    return None, 0, 0

# 把自己的球搞到中线球的最上方来防守
def defense_center_two(point_list, ally_list, enemy_list):
    point_in_center_line1 = []
    point_in_center_line2 = []
    for point in point_list:
        if (point[1] > 4.88 + stone_r) and (2.375 - stone_r - 0.02 < point[0] < 2.375 + stone_r + 0.02):
            point_in_center_line1.append(point)
        if (point[1] > 4.88) and (2.375 - stone_r - 0.02 < point[0] < 2.375 + stone_r + 0.02):
            point_in_center_line2.append(point)
        else:
            continue
    sorted_point_in_center_line1 = []
    sorted_point_in_center_line2 = []
    if point_in_center_line1:
        sorted_point_in_center_line1 = sorted(point_in_center_line1, key=lambda point_in_center: point_in_center[1])
    if point_in_center_line2:
        sorted_point_in_center_line2 = sorted(point_in_center_line2, key=lambda point_in_center: point_in_center[1])
    if sorted_point_in_center_line1:
        if sorted_point_in_center_line1[0] == sorted_point_in_center_line2[0]:
            safe_area = [4.88, sorted_point_in_center_line1[0][1] - stone_r]
        else:
            safe_area = [sorted_point_in_center_line2[0][1] + stone_r, sorted_point_in_center_line1[0][1] - stone_r]
    else:
        safe_area = [4.88 + stone_r, 6.71]
    final_return_out = []
    # 安全区已经给出，外碰内碰进去
    for ally in ally_list:
        type_ally = analyse_point(ally)
        if type_ally in ['three', 'four', 'five', 'six']:
            if (ally in point_in_center_line2) or (get_distance_circle(ally[0], ally[1]) < 0.6):
                pass
            else:
                # 外碰进去
                for target in point_center_line:
                    if safe_area[0] < target[1] < safe_area[1] and ally[1] > target[1] + 2 * stone_r:
                        angle = cal_angle(ally[0], ally[1], target[0], target[1])
                        if abs(angle) < 55 and 0.545 < ally[0] < 4.205:
                            extra = 0.8 if 50 < abs(angle) < 55 else 0
                            left_or_right = 1 if ally[0] > 2.375 else -1
                            shot_offset, v, w = detect_path(point_list, ally,
                                                            target, 'out',
                                                            left_or_right)
                            if shot_offset:
                                final_return_out.append([v + extra, shot_offset, w])
                            else:
                                pass
                        else:
                            pass
                    else:
                        continue
        else:
            continue
    sorted_final_return_out = []
    if final_return_out:
        sorted_final_return_out = sorted(final_return_out, key=lambda key: abs(key[1]))
    if sorted_final_return_out:
        v, shot_offset, w = sorted_final_return_out[0]
        return v, shot_offset, w
    return None


def find_obs_after(point_list, x, y, target_x, target_y, factor_l=2.0, factor_r=2.0):
    obs_list = []
    angle = cal_angle(x, y, target_x, target_y)
    x = target_x
    y = target_y
    target_x += 1.0 * math.sin(angle * math.pi / 180)
    target_y -= 1.0 * stone_r * math.cos(angle * math.pi / 180)
    s_width = [x - stone_r * factor_l, x + stone_r * factor_r]
    t_width = [target_x - stone_r * factor_l, target_x + stone_r * factor_r]
    for point in point_list:
        # 遍历所有球的xy坐标
        X, Y = point
        # 在左边or右边
        sign = 1 if target_x - x >= 0 else -1
        if sign == 1:
            if s_width[0] < X < t_width[1]:
                if target_y < Y < y:
                    if sign * (target_x - x) > 0.1:
                        if sign * (two_point_eq(s_width[1], y, t_width[1], target_y, X) - Y) > 0:
                            if sign * (Y - two_point_eq(s_width[0], y, t_width[0], target_y, X)) > 0:
                                obs_list.append([X, Y])
                    if sign * (target_x - x) <= 0.1:
                        obs_list.append([X, Y])
        if sign == -1:
            if t_width[0] < X < s_width[1]:
                if target_y < Y < y:
                    if sign * (target_x - x) > 0.1:
                        if sign * (two_point_eq(s_width[1], y, t_width[1], target_y, X) - Y) > 0:
                            if sign * (Y - two_point_eq(s_width[0], y, t_width[0], target_y, X)) > 0:
                                obs_list.append([X, Y])
                    if sign * (target_x - x) <= 0.1:
                        obs_list.append([X, Y])
    return obs_list


# 更加细分区域
def analyse_by_color(point):
    color = [
        'top-blue',
        'top-white',
        'top-red',
        'bottom-blue',
        'bottom-white',
        'bottom-red',
    ]
    color_type = ''
    dis = get_distance_circle(point[0], point[1])
    # 下方
    if point[1] > 4.88:
        if dis < 0.6 + stone_r:
            color_type = color[5]
        elif dis < 1.2 + stone_r:
            color_type = color[4]
        elif dis < 1.8 + stone_r:
            color_type = color[3]
        else:
            color_type = 'out'
    if point[1] <= 4.88:
        if dis < 0.6 + stone_r:
            color_type = color[2]
        elif dis < 1.2 + stone_r:
            color_type = color[1]
        elif dis < 1.8 + stone_r:
            color_type = color[0]
        else:
            color_type = 'out'
    return color_type


# 更加细分区域(特供版)
def analyse_by_color_extra(point):
    color = [
        'top-blue',
        'top-white',
        'top-red',
        'bottom-blue',
        'bottom-white',
        'bottom-red',
    ]
    color_type = ''
    dis = get_distance_circle(point[0], point[1])
    # 下方
    if point[1] > 4.88:
        if dis < 0.6 + stone_r:
            color_type = color[5]
        elif dis < 1.2 + stone_r:
            color_type = color[4]
        elif dis < 1.83 - 3*stone_r:
            color_type = color[3]
        else:
            color_type = 'out'
    if point[1] <= 4.88:
        if dis < 0.6 + stone_r:
            color_type = color[2]
        elif dis < 1.2 + stone_r:
            color_type = color[1]
        elif dis < 1.8 + stone_r:
            color_type = color[0]
        else:
            color_type = 'out'
    return color_type


# 红色区域内有没有我们的球
def our_curling_in_red(ally_list):
    our_in_red = []
    for ally in ally_list:
        dis = get_distance_circle(ally[0], ally[1])
        if dis < 0.6 + stone_r:
            our_in_red.append(ally)
    return our_in_red

# 对面红色区域内有几个球
def count_enemy_in_red(enemy_list):
    count = 0
    for enemy in enemy_list:
        dis = get_distance_circle(enemy[0], enemy[1])
        if dis < 0.6 + stone_r:
            count = count + 1
        else:
            continue
    return count


# 双击
def double_kill(point_list, enemy_list, enemy_crashed):
    print('双击检测正在进行')
    fit_enemy = []
    for enemy in enemy_list:
        if enemy[1] > enemy_crashed[1] + 2 * stone_r:
            angle = cal_angle(enemy[0], enemy[1], enemy_crashed[0], enemy_crashed[1])
            if 30 < abs(angle) < 60 and abs(enemy[0] - 2.375) > abs(enemy_crashed[0] - 2.375):
                left_or_right = 1 if enemy[0] > enemy_crashed[0] else -1
                shot_offset, v, w = detect_path(point_list, enemy, enemy_crashed,'in',left_or_right)
                feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                if shot_offset and feasibility:
                    fit_enemy.append([angle, shot_offset, v + 3, w])
    sorted_fit_enemy = []
    if fit_enemy:
        sorted_fit_enemy = sorted(fit_enemy, key=lambda message: abs(message[0]))
    return sorted_fit_enemy


# 中间两个球很近的时候的区位分析
def near_analyse(enemy_1, enemy_2):
    angle = abs(cal_angle(enemy_1[0], enemy_1[1], enemy_2[0], enemy_2[1]))
    near_type = [
        'vertical',
        'even',
        'slant',
    ]
    if angle < 10:
        near = near_type[0]
    elif 10 <= angle <= 75:
        near = near_type[2]
    else:
        near = near_type[1]
    center = (enemy_1[0] + enemy_2[0]) / 2
    return near, center


# 中间两个球很远的区位分析
def far_analyse(enemy_1, enemy_2):
    angle = abs(cal_angle(enemy_1[0], enemy_1[1], enemy_2[0], enemy_2[1]))
    far_type = [
        'vertical',
        'even',
        'slant',
    ]
    if angle < 10:
        far = far_type[0]
    elif 10 <= angle <= 75:
        far = far_type[2]
    else:
        far = far_type[1]
    return far
# 用对面的球把对面打出去
def double_shot(point_list, enemy_list, enemy_crashed):
    print('double_shot函数正坐在进行')
    fit_shot = []
    for enemy in enemy_list:
        if enemy[0] == enemy_crashed[0]:
            continue
        type_enemy = analyse_by_color(enemy)
        if type_enemy in ['top-red', 'bottom-white', 'bottom-red'] and enemy_crashed[1] < enemy[1]:
            angle = cal_angle(enemy[0], enemy[1], enemy_crashed[0], enemy_crashed[1])
            if abs(angle) < 10 and 0.545 < enemy[0] < 4.205:
                left_or_right = 1
                shot_offset, v, w = detect_path(point_list, enemy,
                                                (enemy_crashed[0], enemy_crashed[1]), 'out',
                                                left_or_right, True)
                if shot_offset:
                    fit_shot.append([angle, shot_offset, v + 3, w])

    sorted_fit_shot = []
    if fit_shot:
        sorted_fit_shot = sorted(fit_shot, key=lambda message: abs(message[0]))
    return sorted_fit_shot
# 用对面的球把对面打出去(增加情况)
def double_shot_extra(point_list, enemy_list, enemy_crashed):
    print('double_shot函数正坐在进行')
    fit_shot = []
    for enemy in enemy_list:
        if enemy[0] == enemy_crashed[0]:
            continue
        type_enemy = analyse_by_color_extra(enemy)
        if type_enemy in ['top-red', 'bottom-white', 'bottom-red', 'bottom-blue'] and enemy_crashed[1] < enemy[1]:
            angle = cal_angle(enemy[0], enemy[1], enemy_crashed[0], enemy_crashed[1])
            if abs(angle) < 10 and 0.545 < enemy[0] < 4.205:
                left_or_right = 1
                shot_offset, v, w = detect_path(point_list, enemy,
                                                (enemy_crashed[0], enemy_crashed[1]), 'out',
                                                left_or_right, True)
                if shot_offset:
                    fit_shot.append([angle, shot_offset, v + 3, w])
                else:
                    pass
        else:
            pass
    sorted_fit_shot = []
    if fit_shot:
        sorted_fit_shot = sorted(fit_shot, key=lambda message: abs(message[0]))
    return sorted_fit_shot
# 直碰靠近中心的哪个球是否会出现问题
def straight_problem(enemy_list):
    enemy_1 = enemy_list[0]
    enemy_2 = enemy_list[1]
    dis1 = get_distance_circle(enemy_1[0], enemy_1[1])
    dis2 = get_distance_circle(enemy_2[0], enemy_2[1])
    # 假设直碰后的位置,横坐标不变，纵坐标增加两个半径
    ally_final = [enemy_1[0], enemy_1[1] + 2 * stone_r]
    dis3 = get_distance_circle(ally_final[0], ally_final[1])
    if dis3 >= dis2:
        feasibility = False
    else:
        feasibility = True
    return feasibility


# 中心有没有对面的球
def enemy_red(enemy_list):
    enemy_red = []
    for enemy in enemy_list:
        if get_distance_circle(enemy[0], enemy[1]) < 0.61 + stone_r:
            enemy_red.append(enemy)
    return enemy_red


# 用对面的球把对面打出去
def double_shot_strict(point_list, enemy_list, enemy_crashed):
    print('double_shot函数正坐在进行')
    fit_shot = []
    for enemy in enemy_list:
        if enemy[0] == enemy_crashed[0]:
            continue
        type_enemy = analyse_by_color(enemy)
        if type_enemy in ['top-red', 'bottom-red'] and enemy_crashed[1] < enemy[1]:
            angle = cal_angle(enemy[0], enemy[1], enemy_crashed[0], enemy_crashed[1])
            if abs(angle) < 10 and 0.545 < enemy[0] < 4.205:
                left_or_right = 1
                shot_offset, v, w = detect_path(point_list, enemy,
                                                (enemy_crashed[0], enemy_crashed[1]), 'out',
                                                left_or_right, True)
                if shot_offset:
                    fit_shot.append([angle, shot_offset, v + 3, w])
                else:
                    pass
        else:
            pass
    sorted_fit_shot = []
    if fit_shot:
        sorted_fit_shot = sorted(fit_shot, key=lambda message: abs(message[0]))
    return sorted_fit_shot


# 增加铺垫球
def add_base(point_list):
    x_list = []
    shot = None
    for i in (
    0, 0.1, -0.1, 0.2, -0.2, 0.3, -0.3, 0.4, -0.4, 0.5, -0.5, 0.6, -0.6, 0.7, -0.7, 0.8, -0.8, 0.9, -0.9, 1, -1, 1.1,
    -1.1, 1.2, -1.2):
        x_list.append(2.375 + i)
    for x_crash in x_list:
        feasibility, obs_list = check_path_straight(point_list, x_crash, 4.88 + 1.83, factor=0.1)
        if feasibility:
            return random.uniform(2.7, 2.75), x_crash - 2.375, 0
        else:
            continue
    return shot


# 预测直碰和往红心放球那个收益更高
def predict(point_list, ally_list, enemy_list, point):
    print('预测函数里：', point_list, ally_list, enemy_list, point)
    # =========================
    # 改造成两种结果,第一种
    point_list1 = point_list + [point]
    ally_list1 = ally_list + [point]
    print('预测函数里：', point_list1, ally_list1, enemy_list)
    advantage_team1, score1 = predict_score(point_list1, ally_list1, enemy_list)
    # 第二种
    if point_list:
        point_list.remove(enemy_list[0])
    point_list.append([enemy_list[0][0], enemy_list[0][1] + 2 * stone_r])
    ally_list.append([enemy_list[0][0], enemy_list[0][1] + 2 * stone_r])
    enemy_list.remove(enemy_list[0])
    print('预测函数里：', point_list, ally_list, enemy_list)
    advantage_team2, score2 = predict_score(point_list, ally_list, enemy_list)
    # =========================
    if advantage_team1 == 'ally' and advantage_team2 == 'ally':
        if score1 > score2:
            return 'first'
        else:
            return 'second'
    if advantage_team1 == 'ally':
        return 'first'
    if advantage_team2 == 'ally':
        return 'second'
    return None


# predict预测函数
def predict_score(point_list, ally_list, enemy_list):
    advantage_team = ''  # 现在场上占优势的队伍
    score = 0  # 当前领先分数（包括自己和对面）
    # 将state_list中的每两个数字作为一个点的坐标，并构建二维数组
    # 将points中的奇数序列和偶数序列分开
    min_dis_ally = math.inf  # 我方最靠近中心的球距离中心的距离，下同
    for point in ally_list:
        dis = get_distance_circle(point[0], point[1])
        if dis < min_dis_ally and point != [0, 0]:
            min_dis_ally = dis
    min_dis_enemy = math.inf
    for point in enemy_list:
        dis = get_distance_circle(point[0], point[1])
        if dis < min_dis_enemy and point != [0, 0]:
            min_dis_enemy = dis
    min_dis = math.inf
    min_point = None
    for point in point_list:
        dis = get_distance_circle(point[0], point[1])
        # 如果当前距离小于最小距离，更新最小距离和对应的点
        if dis < min_dis and is_in_house(point[0], point[1]):
            min_dis = dis
            min_point = point
    if min_point in ally_list:
        advantage_team = 'ally'
        for point in ally_list:
            if point != [0, 0]:
                dis = get_distance_circle(point[0], point[1])
                if dis < min_dis_enemy and is_in_house(point[0], point[1]):
                    score = score + 1
            else:
                pass
    elif min_point in enemy_list:
        advantage_team = 'enemy'
        for point in enemy_list:
            if point != [0, 0]:
                dis = get_distance_circle(point[0], point[1])
                if dis < min_dis_ally and is_in_house(point[0], point[1]):
                    score = score + 1
            else:
                pass
    return advantage_team, score


# 应对传击的函数
def double_shot_width(point_list, enemy_list, enemy_crashed):
    print('double_shot函数正坐在进行')
    fit_shot = []
    for enemy in enemy_list:
        if enemy[0] == enemy_crashed[0]:
            continue
        type4 = analyse_point(enemy)
        if type4 in ['three', 'four', 'five', 'six'] and enemy_crashed[1] < enemy[1]:
            angle = cal_angle(enemy[0], enemy[1], enemy_crashed[0], enemy_crashed[1])
            if abs(angle) < 10 and 0.545 < enemy[0] < 4.205:
                left_or_right = 1
                shot_offset, v, w = detect_path(point_list, enemy,
                                                (enemy_crashed[0], enemy_crashed[1]), 'out',
                                                left_or_right, True)
                if shot_offset:
                    fit_shot.append([angle, shot_offset, v + 3, w])

    sorted_fit_shot = []
    if fit_shot:
        sorted_fit_shot = sorted(fit_shot, key=lambda message: abs(message[0]))
    return sorted_fit_shot
