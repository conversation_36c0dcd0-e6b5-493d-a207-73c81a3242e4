'''
1. 扩展信息状态数组
2. 设计动作空间
3. 设计策略价值网络层
4. 设计蒙特卡洛树搜索
5. 设计训练过程
6. 设计评估过程
'''
import os,time,random,math
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from sympy.physics.units import action
from torch import softmax, optim
from collections import deque
import strategy.冰冰薄荷糖 as Ice
import multiprocessing as mp
from AIRobot import AIRobot
from HIT你的壶 import strategy

# -*- coding: utf-8 -*-


#营垒圆心坐标
House_x = 2.375
House_y = 4.88
#营垒半径
House_R = 1.830
#冰壶半径
Stone_R = 0.14

# 连接密钥：参照数字冰壶服务器界面中给出的连接信息填写，注意这个参数每次新启动服务器都会改变。
key = "lidandan_44d1365b-f7b7-4536-9457-c8892c72d5d7"
host = 7788
# 计算某一冰壶距离营垒圆心的距离
def get_dist(x, y):
    return math.sqrt((x - House_x) ** 2 + (y - House_y) ** 2)


# 根据冰壶位置坐标列表获取得分情况并生成信息状态数组
def get_infostate(position, shot_num,player_is_init):
    init = np.empty([8], dtype=float)
    gote = np.empty([8], dtype=float)
    both = np.empty([16], dtype=float)
    # 计算双方冰壶到营垒圆心的距离
    for i in range(8):
        init[i] = get_dist(position[4 * i], position[4 * i + 1])
        both[2 * i] = init[i]
        gote[i] = get_dist(position[4 * i + 2], position[4 * i + 3])
        both[2 * i + 1] = gote[i]
    # 找到距离圆心较远一方距离圆心最近的壶
    if min(init) <= min(gote):
        win = 0  # 先手得分
        d_std = min(gote)
    else:
        win = 1  # 后手得分
        d_std = min(init)

    infostate = []  # 状态数组
    init_score = 0  # 先手得分
    # 先将所有壶的坐标和归属整理出来
    stones = []
    for i in range(16):
        x = position[2 * i]
        y = position[2 * i + 1]
        if x != 0 or y != 0:
            belong = 0 if i % 2 == 0 else 1  # 0为先手，1为后手
            stones.append((i, belong, x, y))
        else:
            stones.append((i, -1, 0, 0))  # 无效壶
    # 计算每个壶的状态信息
    for i in range(16):
        idx, belong, x, y = stones[i]
        if belong == -1:
            infostate.append([ 0, 0, 0, 0, 0, 0, 0, 0, 0])
            continue
        dist = get_dist(x, y)

        # 有效得分壶判定
        if (dist < d_std) and (dist < (House_R + Stone_R)) and ((idx % 2) == win):
            valid = 1
            if win == 0:
                init_score += 1
            # 如果是后手得分
            else:
                init_score -= 1
        else:
            valid = 0
        # 是否在营垒内
        in_house = 1 if dist <= House_R else 0
        # 计算与其他壶的距离之和
        dist_sum = 0
        for j in range(16):
            if i == j:
                continue
            _, _, x2, y2 = stones[j]
            if x2 == 0 and y2 == 0:
                continue
            dist_sum += math.sqrt((x - x2) ** 2 + (y - y2) ** 2)
        # 计算与左右边界的距离（假设冰壶场宽为4.75，左右边界x=0和x=4.75）
        left_dist = abs(x - 0)
        right_dist = abs(x - 4.75)
        # 每个球的状态信息：[此球属于先手还是后手，x坐标, y坐标, 距离营垒圆心的距离, 是否有效得分壶, 是否在营垒内, 这个壶跟其他所有壶的距离, 这个壶跟左右边界的距离]
        infostate.append([
            belong, x, y, dist, valid, in_house, dist_sum, left_dist, right_dist
        ])
    # 按dist升序排列（无效壶排最后）
    infostate = sorted(infostate, key=lambda x: x[3] if x[1] != 0 or x[2] != 0 else float('inf'))
    # 返回先手得分和转为一维的状态数组，首位加上当前己方先后手
    return init_score, np.concatenate([[shot_num],[player_is_init], np.array(infostate).flatten()])


# 冰壶游戏状态类
class CurlingGameState:
    def  __init__(self):
        self.position = [0] * 32  # 16个冰壶的x,y坐标
        self.shot_num = 0  # 当前投掷次数
        self.player_is_init = 0  # 当前玩家（0为先手，1为后手）
        self.score = 0  # 当前比分
        self.action_space = self.generate_action_space()  # 生成动作空间

    # 生成离散化的动作空间
    def generate_action_space(self):
        # 低速：在(2.4,2.7)之间以0.1为步长进行离散
        slow = np.arange(2.5, 2.7, 0.1)#1
        # 中速：在(2.8,3.2)之间以0.05为步长进行离散
        normal = np.arange(2.70, 3.5, 0.05)#22
        # 高速
        fast = np.arange(3.5, 8, 0.2)#17
        # 将低速、中速、高速三个数组连接起来
        speed = np.concatenate((slow, normal, fast))#40
        # 横向偏移在(-2,2)之间以0.4为步长进行离散
        deviation = np.arange(-2.375, 2.375, 0.3) #15
        # 角速度在(-3.14, 3.14)之间以0.628为步长进行离散
        angspeed = np.arange(-3.14, 3.14, 0.628)#10

        action_space = []
        for v in speed:
            for h in deviation:
                for w in angspeed:
                    action_space.append((v, h, w))

        print("动作空间大小：", len(action_space))
        return action_space

    # 更新游戏状态
    def update_state(self, new_state):
        self.position = new_state['position']
        self.shot_num = new_state['shot_num']
        self.player_is_init = new_state['player_is_init']
        self.score = new_state['score']

    # 检查游戏是否结束
    def game_end(self):
        return self.shot_num == 16

    # 获取胜者
    def get_winner(self):
        if self.score > 0:
            return 1  # 先手胜
        elif self.score < 0:
            return -1  # 后手胜
        else:
            return 0  # 平局

    # 获取可用动作
    def available_moves(self):
        return self.action_space

    def copy(self):
        # 创建一个新的 CurlingGameState 实例
        new_state = CurlingGameState()
        # 复制所有属性
        new_state.position = self.position.copy()
        new_state.shot_num = self.shot_num
        new_state.player_is_init = self.player_is_init
        new_state.score = self.score
        return new_state


# 策略价值网络
class PolicyValueNet(nn.Module):
    def __init__(self, input_size=146, action_size=6720):
        super(PolicyValueNet, self).__init__()
        self.fc1 = nn.Linear(input_size, 256)  # 定义全连接层1
        self.fc1.weight.data.normal_(0, 0.1)  # 按(0, 0.1)的正态分布初始化权重


        self.fc2 = nn.Linear(256, 256)  # 定义全连接层2
        self.fc2.weight.data.normal_(0, 0.1)  # 按(0, 0.1)的正态分布初始化权重


        self.dropout = nn.Dropout(0.3)

        self.fc_policy = nn.Linear(256, action_size)  # 定义策略网络输出层
        self.fc_policy.weight.data.normal_(0, 0.1)  # 按(0, 0.1)的正态分布初始化权重
        self.fc_value = nn.Linear(256, 1)  # 定义价值网络输出层
        self.fc_value.weight.data.normal_(0, 0.1)  # 按(0, 0.1)的正态分布初始化权重
       # device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        #self.net = PolicyValueNet().to(device)

    def forward(self, x):
        x = F.relu(self.fc1(x))  # 输入张量经全连接层1传递后经relu函数激活
        x = self.dropout(x)  # 添加dropout层以防止过拟合
        x = F.relu(self.fc2(x))  # 经全连接层2传递后经relu函数激活
        x = self.dropout(x)  # 再次添加dropout层

        policy_logits = self.fc_policy(x)  # 经策略网络输出层传递得到策略输出
        value = self.fc_value(x)  # 经价值网络输出层传递得到价值输出
        value = torch.tanh(value)  # 对价值输出进行tanh归一化
        return policy_logits, value


c_puct = 5  # 探索常数
n_playout = 10  # 每次移动的模拟次数
temperature = 1e-3  # 温度参数，控制探索程度


# 蒙特卡洛树搜索类
class MCTS:
    def __init__(self, policy_value_fn, c_puct=c_puct, n_playout=n_playout):
        self.policy_value_fn = policy_value_fn  # 策略价值函数
        self.c_puct = c_puct  # 探索常数
        self.n_playout = n_playout  # 每次移动的模拟次数
        self.Q = {}  # 存储动作价值
        self.N = {}  # 存储访问次数
        self.P = {}  # 存储先验概率

    def get_action_probs(self, state, temperature=temperature):
        moves = state.available_moves()  # 获取可用动作
        """
        执行多次 playout，并返回可用动作及其对应的概率。
        :param state: 当前游戏状态
        :param temperature: 温度参数，控制探索程度
        :return: 动作列表和对应的概率分布
        """
        for _ in range(self.n_playout):
            self._playout(state)

        s = self._get_state_key(state)
        counts = np.array([self.N.get((s, a), 0) for a in moves])

        if temperature == 0:  # 如果温度为0，选择访问次数最多的动作
            bestAction = np.argmax(counts)
            probs = np.zeros_like(counts)
            probs[bestAction] = 1
            return [moves[bestAction]], probs

        else:  # 否则，使用 softmax 函数计算概率分布
            counts = counts.astype(float)
            if temperature < 1e-3:
                temperature = 1e-3  # 设置一个最小温度值

            counts = np.log(counts + 1e-10) / temperature
            counts_max = np.max(counts)
            probs = np.exp(counts - counts_max)
            probs /= np.sum(probs)

            return moves, probs

    def _playout(self, state):
        """
        从当前状态开始执行一次 playout。
        :param state: 当前游戏状态
        :return: 叶子节点的评估值
        """
        s = self._get_state_key(state)
        if s not in self.P:  # 如果是新状态，使用策略网络进行评估
            action_probs, leaf_value = self.policy_value_fn(state)
            self.P[s] = dict(zip(state.available_moves(), action_probs))
            return -leaf_value

        if s not in self.N:
            self.N[s] = 0

        # 选择最佳动作（UCB公式）
        best_score = -float('inf')
        best_action = None
        for action, prob in self.P[s].items():
            if (s, action) in self.Q:
                u = self.Q[(s, action)] + self.c_puct * prob * np.sqrt(self.N[s]) / (1 + self.N[(s, action)])
            else:
                u = self.c_puct * prob * np.sqrt(self.N[s] + 1e-8)
            if u > best_score:
                best_score = u
                best_action = action

        # 评估选择的动作
        value = self._evaluate(state, best_action)

        # 更新统计信息
        if (s, best_action) in self.Q:
            self.Q[(s, best_action)] = (self.N[(s, best_action)] * self.Q[(s, best_action)] + value) / (
                        self.N[(s, best_action)] + 1)
            self.N[(s, best_action)] += 1
        else:
            self.Q[(s, best_action)] = value
            self.N[(s, best_action)] = 1

        self.N[s] = self.N.get(s, 0) + 1
        return -value

    def _evaluate(self, state, action):
        """
        使用策略价值网络评估状态
        """
        # 确保 state 是 CurlingGameState 对象
        if not isinstance(state, CurlingGameState):
            raise TypeError("state must be a CurlingGameState object")

        # 将状态转换为网络输入格式
        state_input = self._state_to_input(state, action)

        # 使用策略价值网络进行评估
        with torch.no_grad():
            policy_logits, value = self.policy_value_fn(state)

        # 返回价值估计
        return value

    def _state_to_input(self, state, action):
        """
        将游戏状态和动作转换为网络输入格式
        """
        # 确保 state 是 CurlingGameState 对象
        if not isinstance(state, CurlingGameState):
            raise TypeError("state must be a CurlingGameState object")

        # 使用 get_infostate 函数获取状态表示
        _, state_info = get_infostate(state.position, state.shot_num+1, state.player_is_init)
        state_vector = torch.FloatTensor(state_info)
        action_vector = torch.FloatTensor(action)
        return torch.cat([state_vector, action_vector]).unsqueeze(0)

    def _get_state_key(self, state):
        # 使用元组而不是列表，因为元组是可哈希的
        return tuple(state.position + [state.shot_num, int(state.player_is_init), state.score])


# 核回归类
class KernelRegression:
    def __init__(self, kernel_width=0.1):
        self.kernel_width = kernel_width
        self.X = []
        self.y = []

    def add_data(self, x, y):
        self.X.append(x)
        self.y.append(y)

    def predict(self, x):
        weights = np.exp(-np.sum((np.array(self.X) - x) ** 2, axis=1) / (2 * self.kernel_width ** 2))
        return np.sum(weights * np.array(self.y)) / np.sum(weights)


class SharedReplayBuffer:
    def __init__(self, capacity):
        self.buffer = mp.Queue(maxsize=capacity)

    def push(self, experience):
        if self.buffer.full():
            self.buffer.get()  # 如果满了,移除最早的经验
        self.buffer.put(experience)

    def sample(self, batch_size):
        # if self.buffer.qsize() < batch_size:
        #    return list(self.buffer.queue) # 如果经验不足,返回所有可用经验
        # return random.sample(list(self.buffer.queue), batch_size)
        batch = [self.buffer.get() for _ in range(self.buffer.qsize())]
        if len(batch) < batch_size:
            return batch
        return random.sample(batch, batch_size)


class SharedModel:
    def __init__(self):
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.net = PolicyValueNet().to(device)
        self.net.share_memory()  # 使模型参数在多个进程间共享
        self.optimizer = optim.Adam(self.net.parameters(), lr=0.002, weight_decay=1e-4)
        self.lock = mp.Lock()  # 用于同步更新
        self.replay_buffer = SharedReplayBuffer(capacity=10000)

    def get_parameters(self):
        return self.net.state_dict()

    def set_parameters(self, parameters):
        self.net.load_state_dict(parameters)

    def update(self, state, mcts_probs, winner):
        # 将经验存储到共享经验库
        self.replay_buffer.push((state, mcts_probs, winner))

        loss = -1
        # 从经验库中采样进行训练
        with self.lock:
            experiences = self.replay_buffer.sample(batch_size=32)
            for exp in experiences:
                loss = self.train_step(*exp)
        return loss

    def train_step(self, state, mcts_probs, winner):
        # 准备输入数据
        _, state_info = get_infostate(state.position, state.shot_num + 1, state.player_is_init)
        state_tensor = torch.FloatTensor(state_info).unsqueeze(0)
        mcts_probs = torch.FloatTensor(mcts_probs).unsqueeze(0)
        winner = torch.FloatTensor([winner]).unsqueeze(0)

        # 前向传播
        policy_logits, value = self.net(state_tensor)

        # 计算损失
        value_loss = nn.functional.mse_loss(value, winner)
        policy_loss = -torch.mean(torch.sum(mcts_probs * nn.functional.log_softmax(policy_logits, dim=1), dim=1))
        loss = value_loss + policy_loss

        # 反向传播和优化
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        return loss.item()


class AlphaZeroCurling(AIRobot):
    def __init__(self, key, name, host, port, shared_model,
                 model_file_name, log_file_name, round_max=10000):
        super().__init__(key, name, host, port)
        self.shared_model = shared_model
        self.mcts = MCTS(self.policy_value_fn)
        self.round_max = round_max
        self.model_file_name = model_file_name
        self.log_file_name = log_file_name
        self.states = []
        self.mcts_probs = []
        self.score = 0

    def map_continuous_to_discrete(self,action, action_space):

        action_array = np.array(action_space)
        distances = np.sum((action_array - np.array(action)) ** 2, axis=1)
        best_match = np.argmin(distances)

        return best_match
    def policy_value_fn(self, game_state, hit_weight = 0.5):
        v_hit, h_hit, w_hit = strategy(game_state.position, game_state.shot_num + 1, game_state.player_is_init)
        action = (v_hit, h_hit, w_hit)
        hit_action = self.map_continuous_to_discrete(action, game_state.action_space)

        state_tensor = torch.FloatTensor(get_infostate(game_state.position, game_state.shot_num + 1, game_state.player_is_init)[1]).unsqueeze(0)
        with torch.no_grad():
            policy_logits, value = self.shared_model.net(state_tensor)
        policy = nn.functional.softmax(policy_logits, dim=1).squeeze(0).numpy()

        hybrid_policy = (1-hit_weight) * np.array(policy)
        hybrid_policy[hit_action] += hit_weight * 0.8

        noise_alpha = 0.2
        noise = np.random.dirichlet([noise_alpha] * len(hybrid_policy))
        noisy_policy = 0.75 * hybrid_policy + 0.25 * noise  # 控制噪声比例

        return noisy_policy, value.item()

    def get_action(self, state, temperature=1e-3):
        # 确保 state 是 CurlingGameState 对象
        if not isinstance(state, CurlingGameState):
            raise TypeError("state must be a CurlingGameState object")

        # 使用 MCTS 获取动作概率
        actions, probs = self.mcts.get_action_probs(state, temperature)

        # 如果温度接近于0，选择最佳动作
        if temperature < 1e-3:
            best_action = actions[np.argmax(probs)]
        else:
            # 否则，根据概率随机选择动作
            # 创建一个表示动作索引的数组
            action_indices = np.arange(len(actions))
            # 使用概率选择一个动作的索引
            selected_index = np.random.choice(action_indices, p=probs)
            # 从动作列表中获取选定的动作
            best_action = actions[selected_index]
        return best_action

    def recv_setstate(self, msg_list):
        # 当前完成投掷数
        self.shot_num = int(msg_list[0])
        # 总对局数
        self.round_total = int(msg_list[2])

        # 如果达到最大轮数，结束训练
        if self.round_num == self.round_max:
            self.on_line = False
            return

        # 每局开始时初始化数据存储
        if self.shot_num == 0:
            self.states, self.mcts_probs = [], []
            self.last_score = 0
            # 根据先后手选取模型并设定当前选手第一壶是当局比赛的第几壶
            if self.player_is_init:
                self.first_shot = 0
            else:
                self.first_shot = 1

        # 创建新的游戏状态
        state = CurlingGameState()
        state.update_state({
            'position': self.position,
            'shot_num': self.shot_num,
            'player_is_init': self.player_is_init,
            'score': self.score
        })

        # 对每个投掷进行处理
        if self.shot_num < 16:
            # 创建当前游戏状态
            state = CurlingGameState()
            state.position = self.position
            state.shot_num = self.shot_num
            state.player_is_init = self.player_is_init

            # 设置温度参数
            temperature = 1.0 if self.shot_num < 4 else 1e-3
            # 获取动作概率
            acts, act_probs = self.mcts.get_action_probs(state, temperature)

            # 存储状态、概率和玩家信息
            self.states.append(state)
            self.mcts_probs.append(act_probs)

            # 选择动作
            action = self.get_action(state, temperature)
            self.action = action  # 存储动作以供 get_bestshot 方法使用

        # 一局结束后进行训练
        if self.shot_num == 16:

            for i in range(len(self.states)):
                # 使用共享模型进行训练
                loss = self.shared_model.update(self.states[i], self.mcts_probs[i], self.score)

            # 更新轮数和记录日志
            self.round_num += 1
            if loss > 0:
                with open(self.log_file_name, 'a+') as log_file:
                    log_file.write(f"score {self.score} {self.round_num}\n")
                    log_file.write(f"loss {loss} {self.round_num}\n")
            print(f"<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<Round {self.round_num}>>>>>>>>>>>>>>>>>>>>>")
            # 每12轮保存一次模型(自我对弈训练中为了避免重复保存仅先手方保存模型)
            if self.player_is_init and (self.round_num % 12 == 0):
                torch.save(self.shared_model.get_parameters(), self.model_file_name)
                print('============= Checkpoint Saved =============')

    def get_bestshot(self):
        """
        计算并返回最佳的投掷动作。
        这个方法在收到服务器的 "GO" 指令时被调用。
        :return: 格式化的 BESTSHOT 消息
        """
        # 创建当前游戏状态
        state = CurlingGameState()
        state.position = self.position
        state.shot_num = self.shot_num
        state.player_is_init = self.player_is_init
        state.score = self.score

        # 使用 MCTS 获取动作概率
        actions, probs = self.mcts.get_action_probs(state, temperature=1e-3)

        # 选择概率最大的动作
        best_action = actions[np.argmax(probs)]

        # 将最佳动作转换为 BESTSHOT 消息格式
        return f"BESTSHOT {best_action[0]} {best_action[1]} {best_action[2]}"





# 创建一个函数来启动单个 AI 进程：
def run_ai(key, name, host, port, shared_model, model_file_name, log_file_name):
    ai = AlphaZeroCurling(key, name, host, port, shared_model, model_file_name, log_file_name)
    ai.recv_forever()


# 修改主函数以启动两个 AI 进程：
if __name__ == "__main__":
    shared_model = SharedModel()

    # 如果存在已保存的模型，则加载它
    model_file_name = 'model/MCTS_model.pth'
    if os.path.exists(model_file_name):
        shared_model.set_parameters(torch.load(model_file_name))
        print(f"Model file {model_file_name} loaded!")
    # 日志文件
    log_file_name = 'log/MCTS_' + time.strftime("%y%m%d_%H%M%S") + '.log'

    # 创建两个进程，分别代表两个 AI
    p1 = mp.Process(target=run_ai,
                    args=(key, "MCTSRobot1", "127.0.0.1", host, shared_model, model_file_name, log_file_name))
    time.sleep(1)
    p2 = mp.Process(target=run_ai,
                    args=(key, "MCTSRobot2", "127.0.0.1", host, shared_model, model_file_name, log_file_name))

    # 启动进程
    p1.start()
    #p2.start()

    # 等待进程结束
    p1.join()
    #p2.join()
