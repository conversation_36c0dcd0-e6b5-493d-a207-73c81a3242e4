def getCrash_y(target_y, offset_x):
    offset_y = 2 * stone_r * math.sqrt(1 - offset_x**2  / 4 * stone_r**2)  # 碰撞时的y的差
    y = target_y + offset_y
    return y


def getCrash_V(target_y, offset_x, v0):
    y =getCrash_y(target_y, offset_x)
    v3 = (22.25-y + 9.5716) / 8.9938  # 到达那个位置的v，可用于动能定理
    v = math.sqrt(v0**2 - v3**2)  # 动能定理
    return v


def getCrashPoint(target_x, target_y, offset_x):  # 碰撞点在左下角
    crash_x = target_x - offset_x
    crash_y = getCrash_y(target_y, offset_x)
    return crash_x, crash_y

def get_v__crash_y__relative_x(X_1,Y_1,X_2,Y_2):
    near_y = max(Y_2, Y_1)
    far_y = min(Y_1, Y_2)
    left_or_right = 1  # 指的是近的球在远球的左或右，左-1，右1

    if Y_1 >= Y_2:
        near_x = X_1
        far_x = X_2
    else:
        near_x = X_2
        far_x = X_1

    if near_x <= far_x:
        left_or_right = -1

    angle_one = math.atan((near_y - far_y) / abs(far_x - near_x))
    angle_two = math.asin((2 * stone_r) / (get_distance(far_x, far_y, near_x, near_y)))
    angle_three = ((math.pi / 2) - angle_one - angle_two)
    offset_x = get_distance(far_x, far_y, near_x, near_y) * math.cos(angle_two) * math.sin(angle_three)
    offset_y = get_distance(far_x, far_y, near_x, near_y) * math.cos(angle_two) * math.cos(angle_three)
    crash_x = far_x + offset_x * left_or_right
    crash_y = far_y + offset_y
    dis_2 =get_distance(crash_x,crash_y,far_x,far_y)
    v_1 = (22.25 - dis_2 + 9.5716) / 8.9938
    v_0 =(22.25 - crash_y + 9.5716) / 8.9938
    angle = cal_angle(crash_x,crash_y,far_x,far_y)
    relative_x = (angle+4.662+1.517*v_1-4.221*crash_y)/(-12.898)
    crash_x_2 = near_x +relative_x*left_or_right*(-1)
    v = math.sqrt(v_1**2+v_0**2)

    return v_0 ,crash_x_2-2.375,0,relative_x


def cal_detal_inside_2(X_1, Y_1, X_2, Y_2):  # 两个球的坐标，不用考虑先后
    near_y = max(Y_2, Y_1)
    far_y = min(Y_1, Y_2)
    left_or_right = 1  # 指的是近的球在远球的左或右，左-1，右1

    if Y_1 >= Y_2:
        near_x = X_1
        far_x = X_2
    else:
        near_x = X_2
        far_x = X_1

    if near_x <= far_x:
        left_or_right = -1

    angle_one = math.atan((near_y - far_y) / abs(far_x - near_x))
    angle_two = math.asin((2 * stone_r) / (get_distance(far_x, far_y, near_x, near_y)))
    angle_three = ((math.pi / 2) - angle_one - angle_two)
    offset_x = get_distance(far_x, far_y, near_x, near_y) * math.cos(angle_two) * math.sin(angle_three)
    offset_y = get_distance(far_x, far_y, near_x, near_y) * math.cos(angle_two) * math.cos(angle_three)
    crash_x = far_x + offset_x * left_or_right
    crash_y = far_y + offset_y
    angle, dis = cal_angle_dis(crash_x, crash_y, far_x, far_y)
    print(f'angle={angle},dis={dis}')
    factor = 0
    if abs(angle) < 20 or abs(angle) > 80:
        pass
    elif 20 <= angle < 30:
        factor = -0.02
    elif 30 <= angle < 40:
        factor = -0.026
    elif 40 <= angle < 50:
        factor = -0.027
    elif 50 <= angle < 60:
        factor = -0.028
    elif 60 <= angle < 70:
        factor = -0.029
    elif 70 <= angle <= 80:
        factor = -0.03
    elif -22 < angle <= -20:
        factor = 0.028
    elif -24 < angle <= -22:
        factor = 0.027
    elif -26 < angle <= -24:
        factor = 0.026
    elif -30 < angle <= -26:
        factor = 0.0253
    elif -40 < angle <= -30:
        factor = 0.025
    elif -50 < angle <= -40:
        factor = 0.026
    elif -60 < angle <= -50:
        factor = 0.027
    elif -80 <= angle <= -60:
        factor = 0.026

    y0 = near_y - far_y
    v0 = move(y0)

    shot_x = crash_x - 2.375 + factor * left_or_right
    v = (22.25 - dis + 9.5716) / 8.9938
    print(v0)
    return v0, shot_x, 0