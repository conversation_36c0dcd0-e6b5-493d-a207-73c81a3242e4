# -*- coding: utf-8 -*-
import socket
import time
import random
import math
import argparse
from utils import *


# 常量定义
CENTER_X = 2.375  # 大本营中心位置坐标
CENTER_Y = 4.88
HOUSE_R = 1.830  # 大本营半径
STONE_R = 0.145  # 冰壶半径

# 中心点坐标
POINT_CENTER = [
    [2.375, 4.88],
    [2.375 + 0.21, 4.88 - 0.21],
    [2.375 + 0.21, 4.88 + 0.21],
    [2.375 - 0.21, 4.88 + 0.21],
    [2.375 - 0.21, 4.88 - 0.21],
    [2.375 + 0.21, 4.88],
    [2.375 - 0.21, 4.88],
    [2.375, 4.88 - 0.21],
    [2.375, 4.88 + 0.21],
]


def get_distance(x1, y1, x2=CENTER_X, y2=CENTER_Y):
    """计算两点之间的距离"""
    return math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2)


def is_in_house(x, y):
    """判断冰壶是否在大本营内"""
    distance = get_distance(x, y)
    return distance - STONE_R < HOUSE_R


def cal_angle(x1, y1, x2, y2):
    """计算两点之间的角度"""
    dis = get_distance(x1, y1, x2, y2)
    if y1 != y2 and dis > 0.1:
        return math.atan((x2 - x1) / (y1 - y2)) * 180 / math.pi
    return 90.0


def organize_position(turn, state_list, has_hammer):
    """整理场上冰壶位置信息，加入hammer判断"""
    advantage_team = ''
    all_in_house = []
    ally_in_house = []
    enemy_in_house = []
    score = 0

    # 将state_list转换为点列表
    point_list = [[state_list[i], state_list[i + 1]] for i in range(0, len(state_list), 2)]

    # 根据先后手分配我方和对方冰壶
    if turn:
        ally_list = point_list[::2]
        enemy_list = point_list[1::2]
    else:
        enemy_list = point_list[::2]
        ally_list = point_list[1::2]

    # 过滤无效点
    filtered_point_list = [p for p in point_list if p != [0, 0]]
    filtered_ally_list = [p for p in ally_list if p != [0, 0]]
    filtered_enemy_list = [p for p in enemy_list if p != [0, 0]]

    # 计算最近点
    min_dis_ally = min((get_distance(p[0], p[1]) for p in filtered_ally_list), default=math.inf)
    min_dis_enemy = min((get_distance(p[0], p[1]) for p in filtered_enemy_list), default=math.inf)

    # 判断优势队伍
    if min_dis_ally < min_dis_enemy:
        advantage_team = 'ally'
        score = sum(
            1 for p in filtered_ally_list if is_in_house(p[0], p[1]) and get_distance(p[0], p[1]) < min_dis_enemy)
    elif min_dis_enemy < min_dis_ally:
        advantage_team = 'enemy'
        score = sum(
            1 for p in filtered_enemy_list if is_in_house(p[0], p[1]) and get_distance(p[0], p[1]) < min_dis_ally)

    # 加入hammer判断
    if has_hammer:
        if advantage_team == 'ally':
            score += 1  # 有hammer时我方优势更大
        elif advantage_team == '':
            advantage_team = 'ally'  # 平局时有hammer的一方占优

    # 对敌方冰壶按距离排序
    sorted_enemy_list = sorted(filtered_enemy_list, key=lambda p: get_distance(p[0], p[1]))

    return advantage_team, score, filtered_point_list, filtered_ally_list, sorted_enemy_list, min(
        filtered_point_list, key=lambda p: get_distance(p[0], p[1]), default=None)


"""                                 """
def aggressive_attack(point_list, ally_list, enemy_list, min_point, has_hammer):
    """积极进攻策略（对方领先+我方有后手）"""
    print('执行积极进攻策略')

    # 优先双杀机会
    for enemy in enemy_list[:2]:  # 只检查前两个最靠近中心的
        double_shot = find_double_shot(point_list, enemy_list, enemy)
        if double_shot:
            print('发现双杀机会:', double_shot)
            return list_to_str(double_shot)

    # 其次直接攻击最近敌方壶
    if enemy_list:
        enemy = enemy_list[0]
        feasibility, _ = check_path_straight(point_list, enemy[0], enemy[1])
        if feasibility:
            power = 7.2 if has_hammer else 6.8  # 有后手时增加力度
            print('直接攻击最近壶:', enemy)
            return list_to_str([power, enemy[0] - CENTER_X, 0])

    # 再次尝试外碰
    for ally in ally_list:
        if ally[1] > enemy[1]:  # 选择位置更深的我方壶
            angle = abs(cal_angle(ally[0], ally[1], enemy[0], enemy[1]))
            if angle < 60:
                path = detect_path(point_list, ally, enemy, 'out', 1 if ally[0] > CENTER_X else -1)
                if path[0]:
                    print('外碰执行:', ally, '->', enemy)
                    return list_to_str([path[1] + (1.0 if has_hammer else 0), path[0], path[2]])

    # 保底策略
    return defend_center(point_list, ally_list, min_point)


def stable_defense(point_list, ally_list, enemy_list, min_point):
    """稳健防守策略（对方领先+我方无后手）"""
    print('执行稳健防守策略')

    # 优先保护中心
    defense = defend_center(point_list, ally_list, min_point)
    if defense:
        return defense

    # 其次尝试轻推干扰
    for enemy in enemy_list[:2]:
        if enemy[1] > CENTER_Y + 1.0:  # 只干扰前场壶
            feasibility, _ = check_path_straight(point_list, enemy[0], enemy[1], factor=0.3)
            if feasibility:
                print('轻推干扰:', enemy)
                return list_to_str([4.5, enemy[0] - CENTER_X, -0.05])

    # 最后尝试传击
    for ally in ally_list:
        for enemy in enemy_list:
            if get_distance(ally[0], ally[1], enemy[0], enemy[1]) < 1.5:
                path = detect_path(point_list, ally, enemy, 'in', 1 if ally[0] > enemy[0] else -1)
                if path[0]:
                    print('传击执行:', ally, '->', enemy)
                    return list_to_str([path[1] + 0.5, path[0], path[2]])

    return list_to_str([2.98, 0, 0])


def consolidate_lead(point_list, ally_list, enemy_list, min_point):
    """巩固领先策略（我方领先+有后手）"""
    print('执行巩固领先策略')

    # 优先防守中心
    if min_point in ally_list:
        defense = defend_center(point_list, ally_list, min_point)
        if defense:
            return defense

    # 其次清除威胁壶
    for enemy in enemy_list:
        if get_distance_circle(enemy[0], enemy[1]) < 1.0:
            feasibility, _ = check_path_straight(point_list, enemy[0], enemy[1])
            if feasibility:
                print('清除威胁壶:', enemy)
                return list_to_str([6.5, enemy[0] - CENTER_X, 0])

    # 再次增加得分壶
    for x in [0, 0.1, -0.1, 0.2, -0.2]:
        target_x = CENTER_X + x
        feasibility, _ = check_path_straight(point_list, target_x, CENTER_Y)
        if feasibility:
            print('增加得分壶:', target_x)
            return list_to_str([2.9, x, 0])

    return list_to_str([2.98, 0, 0])


def extend_lead(point_list, ally_list, enemy_list, min_point):
    """扩大领先策略（我方领先+无后手）"""
    print('执行扩大领先策略')

    # 优先清除对方得分壶
    for enemy in enemy_list:
        if get_distance_circle(enemy[0], enemy[1]) < 0.8:
            feasibility, _ = check_path_straight(point_list, enemy[0], enemy[1])
            if feasibility:
                print('清除对方得分壶:', enemy)
                return list_to_str([7, enemy[0] - CENTER_X, 0])

    # 其次增加我方得分壶
    if min_point in ally_list:
        for x in [0, 0.1, -0.1]:
            target_x = CENTER_X + x
            feasibility, _ = check_path_straight(point_list, target_x, CENTER_Y)
            if feasibility:
                print('增加得分壶:', target_x)
                return list_to_str([3.0, x, 0])

    # 最后防守布局
    return defend_center(point_list, ally_list, min_point)


def build_scoring_position(point_list, ally_list, enemy_list, has_hammer):
    """建立得分位置（平局+有后手）"""
    print('执行建立得分位置策略')

    # 优先占据中心
    for x in [0, 0.1, -0.1]:
        target_x = CENTER_X + x
        feasibility, _ = check_path_straight(point_list, target_x, CENTER_Y)
        if feasibility:
            power = 2.98 if has_hammer else 2.9
            print('占据中心:', target_x)
            return list_to_str([power, x, 0])

    # 其次清除对方壶
    for enemy in enemy_list[:2]:
        feasibility, _ = check_path_straight(point_list, enemy[0], enemy[1])
        if feasibility:
            print('清除对方壶:', enemy)
            return list_to_str([6.5, enemy[0] - CENTER_X, 0])

    return list_to_str([2.98, 0, 0])


def disrupt_opponent(point_list, ally_list, enemy_list):
    """干扰对方布局（平局+无后手）"""
    print('执行干扰对方策略')

    # 优先干扰对方关键壶
    for enemy in enemy_list:
        if get_distance_circle(enemy[0], enemy[1]) < 1.0:
            feasibility, _ = check_path_straight(point_list, enemy[0], enemy[1])
            if feasibility:
                print('干扰对方壶:', enemy)
                return list_to_str([7, enemy[0] - CENTER_X, 0])

    # 其次占据有利位置
    for x in [0.5, -0.5, 0.6, -0.6]:
        target_x = CENTER_X + x
        feasibility, _ = check_path_straight(point_list, target_x, CENTER_Y + 1.0)
        if feasibility:
            print('占据有利位置:', target_x)
            return list_to_str([2.9, x, 0])

    return list_to_str([2.98, 0, 0])


# ============== 辅助函数（保持与原有代码一致） ==============

def find_double_shot(point_list, enemy_list, target):
    """寻找双杀机会"""
    for other in enemy_list:
        if other != target and get_distance(other[0], other[1], target[0], target[1]) < 3 * STONE_R:
            angle = abs(cal_angle(other[0], other[1], target[0], target[1]))
            if 30 < angle < 60:
                path = detect_path(point_list, other, target, 'in', 1 if other[0] > target[0] else -1)
                if path[0]:
                    return [path[1] + 1.5, path[0], path[2]]
    return None


def final_second_shot(point_list, ally_list, enemy_list, min_point, advantage_team, has_hammer):
    """倒数第二壶策略（shot15）"""
    if advantage_team == 'ally':
        print('我方领先，防守为主')
        if has_hammer:
            print('保持后手优势，双重防守')
            # 尝试设置双重障碍
            for x in [0.3, -0.3, 0.4, -0.4]:
                target_x = CENTER_X + x
                feasibility, _ = check_path_straight(point_list, target_x, CENTER_Y + 1.5)
                if feasibility:
                    return list_to_str([2.9, x, -0.05])
        return defend_center(point_list, ally_list, min_point)

    elif advantage_team == 'enemy':
        print('对方领先，积极进攻')
        if has_hammer:
            print('最后机会，全力得分')
            # 尝试直接得分
            for x in [0, 0.1, -0.1]:
                target_x = CENTER_X + x
                feasibility, _ = check_path_straight(point_list, target_x, CENTER_Y)
                if feasibility:
                    return list_to_str([2.98, x, 0])

        # 必须清除对方得分壶
        if enemy_list and min_point:
            print('强制清除对方得分壶')
            feasibility, _ = check_path_straight(point_list, min_point[0], min_point[1])
            if feasibility:
                return list_to_str([7.5, min_point[0] - CENTER_X, 0])

    # 平局时的特殊处理
    print('平局状态，争取得分位置')
    return build_scoring_position(point_list, ally_list, enemy_list, has_hammer)


def final_shot(point_list, ally_list, enemy_list, min_point, advantage_team, has_hammer):
    """最后一壶策略（shot16）"""
    if advantage_team == 'ally' and has_hammer:
        print('我方领先且有后手，保守得分')
        # 确保至少得1分
        if min_point and min_point in ally_list:
            print('保护现有得分壶')
            return defend_center(point_list, ally_list, min_point)

        # 尝试轻推得分
        for x in [0, 0.1, -0.1]:
            target_x = CENTER_X + x
            feasibility, _ = check_path_straight(point_list, target_x, CENTER_Y)
            if feasibility:
                return list_to_str([2.9, x, 0])

    elif advantage_team == 'enemy' and not has_hammer:
        print('对方领先且我方无后手，必须得分')
        # 尝试双飞等高风险高回报策略
        double_fly = find_double_fly(point_list, ally_list, enemy_list)
        if double_fly:
            return list_to_str(double_fly)

        # 强制得分尝试
        if min_point and min_point in enemy_list:
            print('强制击打对方得分壶')
            feasibility, _ = check_path_straight(point_list, min_point[0], min_point[1])
            if feasibility:
                return list_to_str([7.0, min_point[0] - CENTER_X, 0])

    elif has_hammer:
        print('平局但有后手，争取多分')
        # 尝试得2分以上的布局
        multi_score = setup_multi_score(point_list, ally_list)
        if multi_score:
            return list_to_str(multi_score)

    else:
        print('平局无后手，干扰对方')
        # 尽量使双方不得分
        return disrupt_opponent(point_list, ally_list, enemy_list)

    # 保底策略
    return list_to_str([2.98, 0, 0])


def transition_shot(point_list, ally_list, enemy_list, min_point, advantage_team, has_hammer):
    """过渡壶策略（shot13-14）"""
    if advantage_team == 'ally':
        print('过渡阶段-保持领先')
        if has_hammer:
            # 有后手时建立多重保护
            for ally in ally_list:
                if get_distance_circle(ally[0], ally[1]) < 1.0:
                    for x in [0.2, -0.2, 0.3, -0.3]:
                        target_x = CENTER_X + x
                        feasibility, _ = check_path_straight(point_list, target_x, ally[1] - 0.5)
                        if feasibility:
                            return list_to_str([4.0, x, 0])
        else:
            # 无后手时积极清除威胁
            for enemy in enemy_list:
                if get_distance_circle(enemy[0], enemy[1]) < 1.2:
                    feasibility, _ = check_path_straight(point_list, enemy[0], enemy[1])
                    if feasibility:
                        return list_to_str([7.5, enemy[0] - CENTER_X, 0])

    elif advantage_team == 'enemy':
        print('过渡阶段-追赶比分')
        if has_hammer:
            # 有后手时保留选择权
            return build_scoring_position(point_list, ally_list, enemy_list, True)
        else:
            # 无后手时必须积极进攻
            if enemy_list and min_point:
                feasibility, _ = check_path_straight(point_list, min_point[0], min_point[1])
                if feasibility:
                    return list_to_str([7.0, min_point[0] - CENTER_X, 0])

    # 平局时的过渡策略
    print('过渡阶段-平局状态')
    if has_hammer:
        return setup_multi_score(point_list, ally_list) or \
            list_to_str([2.98, 0, 0])
    else:
        return disrupt_opponent(point_list, ally_list, enemy_list)


# ============== 终局专用辅助函数 ==============

def find_double_fly(point_list, ally_list, enemy_list):
    """寻找双飞机会（高风险高回报）"""
    if len(enemy_list) >= 2:
        first, second = enemy_list[:2]
        if get_distance(first[0], first[1], second[0], second[1]) < 2.5 * STONE_R:
            angle = abs(cal_angle(first[0], first[1], second[0], second[1]))
            if 15 < angle < 45:
                path = detect_path(point_list, first, second, 'in', 1 if first[0] > second[0] else -1)
                if path[0]:
                    return [path[1] + 2.0, path[0], path[2]]
    return None


def setup_multi_score(point_list, ally_list):
    """布置多得分的局面"""
    # 尝试形成三角得分区
    center_already = any(get_distance(p[0], p[1]) < 0.5 for p in ally_list)
    if center_already:
        for x in [0.5, -0.5]:
            target_x = CENTER_X + x
            feasibility, _ = check_path_straight(point_list, target_x, CENTER_Y + 0.3)
            if feasibility:
                return [2.9, x, 0]

    # 保底中心站位
    for x in [0, 0.1, -0.1]:
        target_x = CENTER_X + x
        feasibility, _ = check_path_straight(point_list, target_x, CENTER_Y)
        if feasibility:
            return [2.98, x, 0]
    return None


def strategy(state_list, shotnum, turn, has_hammer):
    """主策略函数，加入hammer优化"""
    advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point = organize_position(
        turn, state_list, has_hammer)

    print('enemy_list:', enemy_list)
    print('后手优势状态:', '有' if has_hammer else '无')

    # 前5壶策略（铺垫阶段）
    if shotnum < 6:
        if shotnum == 1:
            # 第一壶：根据hammer状态调整初始位置
            x = -1.05 if not has_hammer else -0.95  # 有后手时更保守
            shot = str('BESTSHOT {} {} {}'.format(2.90, x, 0.05))
            print(shot[9:])
            print('左侧铺垫第一球', '(保守)' if has_hammer else '')
            return shot

        elif shotnum == 2:
            # 第二壶：有后手时更积极进攻
            if enemy_list and get_distance_circle(enemy_list[0][0], enemy_list[0][1]) < HOUSE_R + STONE_R:
                feasibility, obs_list = check_path_straight(point_list, enemy_list[0][0], enemy_list[0][1])
                if feasibility:
                    # 有后手时增加力度确保清除
                    power = 5.2 if has_hammer else 5.0
                    shot = list_to_str([power, enemy_list[0][0] - CENTER_X, 0])
                    print('进攻敌方首壶', '(加强)' if has_hammer else '')
                    return shot

            # 无目标则继续铺垫，有后手时更靠中线
            x = -1.05 if not has_hammer else -0.9
            shot = None
            if not find_obs(point_list, x + CENTER_X, 12, x + CENTER_X, 6, 2, 2):
                shot = str('BESTSHOT {} {} {}'.format(2.95, x, 0.05))
                print(shot[9:])
                print('左侧铺垫第二球', '(靠中)' if has_hammer else '')
                return shot

            if shot is None:
                x_list = []
                # 有后手时优先选择更靠近中线的位置
                offsets = [0, 0.1, -0.1, 0.2, -0.2] if has_hammer else [0.1, -0.1, 0.2, -0.2, 0.3, -0.3]
                for i in offsets:
                    x_list.append(CENTER_X + i)
                for x_crash in x_list:
                    feasibility, obs_list = check_path_straight(point_list, x_crash, CENTER_Y)
                    if feasibility:
                        power = 3.0 if has_hammer else 2.95  # 有后手时稍加力度
                        shot = list_to_str([power, x_crash - CENTER_X, 0])
                        print('中线进攻', '(加强)' if has_hammer else '')
                        return shot

            if shot is None:
                print('随机前五壶')
                return random_push()

        elif shotnum == 3:
            # 第三壶：有后手时更侧重防守布局
            shot = None
            if enemy_list:
                for enemy in enemy_list:
                    if get_distance_circle(enemy[0], enemy[1]) < HOUSE_R + STONE_R:
                        feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                        if feasibility:
                            # 有后手时不急于清除，保留选择权
                            if has_hammer and advantage_team != 'enemy':
                                print('保留后手优势，暂不进攻')
                                break

                            power = 5.0
                            shot = list_to_str([power, enemy[0] - CENTER_X, 0])
                            return shot

            # 右侧铺垫，有后手时更靠外
            x = 1.1 if not has_hammer else 1.3
            if not find_obs(point_list, x + CENTER_X, 12, x + CENTER_X, 6, 2, 2):
                power = 2.95 if not has_hammer else 2.9  # 有后手时稍加力度
                shot = str('BESTSHOT {} {} {}'.format(power, x, 0.05))
                print(shot[9:])
                print('右侧铺垫', '(外扩)' if has_hammer else '')
                return shot

            if shot is None:
                x_list = []
                # 有后手时优先选择防守位置
                offsets = [0.3, -0.3, 0.4, -0.4] if has_hammer else [0.1, -0.1, 0.2, -0.2]
                for i in offsets:
                    x_list.append(CENTER_X + i)
                for x_crash in x_list:
                    feasibility, obs_list = check_path_straight(point_list, x_crash, CENTER_Y)
                    if feasibility:
                        power = 2.95 if not has_hammer else 3.05
                        shot = list_to_str([power, x_crash - CENTER_X, 0])
                        print('中线布局', '(防守)' if has_hammer else '')
                        return shot

            if shot is None:
                print('随机前五壶')
                return random_push()

        elif shotnum == 4:
            # 第四壶：根据hammer状态调整策略
            shot = None
            if enemy_list:
                for enemy in enemy_list:
                    if get_distance_circle(enemy[0], enemy[1]) < HOUSE_R + STONE_R:
                        feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                        if feasibility:
                            # 有后手时更保守，保留选择权
                            if has_hammer and advantage_team != 'enemy' and shotnum < 15:
                                print('保留后手优势，暂缓进攻')
                                break

                            power = 5.0
                            shot = list_to_str([power, enemy[0] - CENTER_X, 0])
                            return shot

            # 有后手时优先建立得分位置
            if has_hammer:
                for x in [0, 0.1, -0.1, 0.2, -0.2]:
                    target_x = CENTER_X + x
                    feasibility, obs_list = check_path_straight(point_list, target_x, CENTER_Y)
                    if feasibility:
                        shot = list_to_str([3.0, x, 0])
                        print('建立得分位置')
                        return shot

            # 默认右侧铺垫
            x = 1.1
            if not find_obs(point_list, x + CENTER_X, 12, x + CENTER_X, 6, 2, 2):
                power = 2.95 if not has_hammer else 2.95
                shot = str('BESTSHOT {} {} {}'.format(power, x, 0.05))
                print(shot[9:])
                print('右侧铺垫')
                return shot

            if shot is None:
                print('随机前五壶')
                return random_push()

        elif shotnum == 5:
            # 第五壶：有后手时更积极布局
            if enemy_list:
                for enemy in enemy_list:
                    if get_distance_circle(enemy[0], enemy[1]) < HOUSE_R + STONE_R:
                        feasibility, obs_list = check_path_straight(point_list, enemy[0], enemy[1])
                        if feasibility:
                            # 有后手时更果断清除
                            power = 5.2 if has_hammer else 5.0
                            shot = list_to_str([power, enemy[0] - CENTER_X, 0])
                            print('清除敌方壶', '(加强)' if has_hammer else '')
                            return shot

            # 有后手时优先占据中心
            if has_hammer:
                for x in [0, 0.1, -0.1]:
                    target_x = CENTER_X + x
                    feasibility, obs_list = check_path_straight(point_list, target_x, CENTER_Y)
                    if feasibility:
                        shot = list_to_str([3.0, x, 0])
                        print('占据中心位置')
                        return shot

            # 尝试随机位置
            x_list = []
            offsets = [0, 0.1, -0.1, 0.2, -0.2] if has_hammer else [0.1, -0.1, 0.2, -0.2, 0.3, -0.3]
            for i in offsets:
                x_list.append(CENTER_X + i)
            for x_crash in x_list:
                feasibility, obs_list = check_path_straight(point_list, x_crash, 6.71)
                if feasibility:
                    power = random.uniform(2.9, 2.95) if not has_hammer else random.uniform(2.9, 2.9)
                    shot = list_to_str([power, x_crash - CENTER_X, 0])
                    return shot

    # ... [保持原有的中局和终局策略代码] ...

    # 中局策略，考虑hammer因素
    elif 6 <= shotnum <=10:
        if advantage_team == 'enemy':
            print('对方领先{}分'.format(advantage_score))
            if has_hammer:
                print('我方有后手优势，采取积极进攻')
                return aggressive_attack(point_list, ally_list, enemy_list, min_point, has_hammer)
            else:
                print('对方有后手优势，采取稳健防守')
                return stable_defense(point_list, ally_list, enemy_list, min_point)

        elif advantage_team == 'ally':
            print('我方领先{}分'.format(advantage_score))
            if has_hammer:
                print('保持后手优势，巩固领先')
                return consolidate_lead(point_list, ally_list, enemy_list, min_point)
            else:
                print('无后手优势，扩大领先')
                return extend_lead(point_list, ally_list, enemy_list, min_point)

        else:
            print('比分持平')
            if has_hammer:
                print('我方有后手优势，建立得分位置')
                return build_scoring_position(point_list, ally_list, enemy_list, has_hammer)
            else:
                print('对方有后手优势，干扰对方布局')
                return disrupt_opponent(point_list, ally_list, enemy_list)

    elif shotnum ==11:
        x_list = []
        for i in ( 0.2, -0.2, 0.3, -0.3, 0.4, -0.4,0.5,-0.5,0.6,-0.6):
            x_list.append(2.375 + i)
        for x_crash in x_list:
            feasibility, obs_list = check_path_straight(point_list, x_crash, 6.71)
            if feasibility:
                shot = list_to_str([2.98, x_crash - 2.375, 0])
                return shot


    elif shotnum == 12:
        x_list = []
        for i in (0.2, -0.2, 0.3, -0.3, 0.4, -0.4, 0.5, -0.5, 0.6, -0.6):
            x_list.append(2.375 + i)
        for x_crash in x_list:
            feasibility, obs_list = check_path_straight(point_list, x_crash, 6.71)
            if feasibility:
                shot = list_to_str([2.98, x_crash - 2.375, 0])
                return shot





        # 终局策略（13-16壶）
    elif 12 < shotnum <= 16:
        if shotnum == 15:
            print('终局阶段-倒数第二壶（shot15）')
            return final_second_shot(point_list, ally_list, enemy_list, min_point, advantage_team, has_hammer)

        elif shotnum == 16:
            print('终局阶段-最后一壶（shot16）')
            return final_shot(point_list, ally_list, enemy_list, min_point, advantage_team, has_hammer)

        else:  # 13-14壶
            print('终局阶段-过渡壶（shot13-14）')
            return transition_shot(point_list, ally_list, enemy_list, min_point, advantage_team, has_hammer)

    # 默认策略
    return list_to_str([5, 0, 0])





class AIRobot:
    def __init__(self, key, name="冰冰无礼", host='127.0.0.1', port=7788, show_msg=False):
        # 新建Socket对象
        self.ai_sock = socket.socket()
        # 创建Socket连接
        self.ai_sock.connect((host, port))
        print("已建立socket连接", host, port)

        # 是否显示接收/发送的消息
        self.show_msg = show_msg
        # 发送连接密钥
        self.send_msg("CONNECTKEY:" + key)

        # 设定机器人名称
        self.name = name
        # 初始化冰壶位置
        self.position = [0] * 32
        # 初始化冰壶运动信息
        self.motioninfo = [0] * 5
        # 设定起始局数
        self.round_num = 0

    # 通过socket对象发送消息
    def send_msg(self, msg):
        if self.show_msg:
            print("  >>>> " + msg)
        # 将消息数据从字符串类型转换为bytes类型后发送
        self.ai_sock.send(msg.strip().encode())

    # 通过socket对象接收消息并进行解析
    def recv_msg(self):
        # 为避免TCP粘包问题，数字冰壶服务器发送给AI选手的每一条信息均以0（数值为0的字节）结尾
        # 这里采用了逐个字节接收后拼接的方式处理信息，多条信息之间以0为信息终结符
        buffer = bytearray()
        while True:
            # 接收1个字节
            data = self.ai_sock.recv(1)
            # 接收到空数据或者信息处终结符(0)即中断循环
            if not data or data == b'\0':
                break
            # 将当前字节拼接到缓存中
            buffer.extend(data)
        # 将消息数据从bytes类型转换为字符串类型后去除前后空格
        msg_str = buffer.decode().strip()
        if self.show_msg:
            print("<<<< " + msg_str)

        # 用空格将消息字符串分隔为列表
        msg_list = msg_str.split(" ")
        # 列表中第一个项为消息代码
        msg_code = msg_list[0]
        # 列表中后续的项为各个参数
        msg_list.pop(0)
        # 返回消息代码和消息参数列表
        return msg_code, msg_list

    # 与大本营中心距离
    def get_dist(self, x, y):
        House_x = 2.375
        House_y = 4.88
        return math.sqrt((x - House_x) ** 2 + (y - House_y) ** 2)

    # 大本营内是否有壶
    def is_in_house(self, dist):
        House_R = 1.830
        Stone_R = 0.145
        if dist < (House_R + Stone_R):
            return 1
        else:
            return 0

    def recv_setstate(self, msg_list):
        # 当前完成投掷数
        self.shot_num = int(msg_list[0])
        # 当前完成对局数
        self.round_num = int(msg_list[1])
        # 总对局数
        self.round_total = int(msg_list[2])
        # 预备投掷者（0为持蓝色球者，1为持红色球者）
        self.next_shot = int(msg_list[3])

    # 接收并处理消息
    def recv_forever(self):
        # 空消息计数器归零
        retNullTime = 0
        self.on_line = True

        while self.on_line:
            # 接收消息并解析
            msg_code, msg_list = self.recv_msg()
            # 如果接到空消息则将计数器加一
            if msg_code == "":
                retNullTime = retNullTime + 1
            # 如果接到五条空消息则关闭Socket连接
            if retNullTime == 5:
                break
                # 如果消息代码是……
            if msg_code == "CONNECTNAME":
                if msg_list[0] == "Player1":
                    self.player_is_init = True
                    print("玩家1，首局先手")
                else:
                    self.player_is_init = False
                    print("玩家2，首局后手")
            if msg_code == "ISREADY":
                # 发送"READYOK"
                self.send_msg("READYOK")
                time.sleep(0.5)
                # 发送"NAME"和AI选手名
                self.send_msg("NAME " + self.name)
                print(self.name + " 准备完毕！")
            if msg_code == "NEWGAME":
                time0 = time.time()
            if msg_code == "SETSTATE":
                self.recv_setstate(msg_list)
            if msg_code == "POSITION":
                for n in range(32):
                    self.position[n] = float(msg_list[n])
            if msg_code == "GO":
                # 制定策略生成投壶信息
                print('==================shot_num↓=====================', self.shot_num + 1)
                has_hammer = not self.player_is_init  # 后手有 hammer，先手无 hammer
                shot_msg = strategy(self.position, self.shot_num + 1, self.player_is_init, has_hammer)
                print('==================shot_num↑=====================', self.shot_num + 1)
                # 发送投壶消息
                print(self.position)
                self.send_msg(shot_msg)
            if msg_code == "MOTIONINFO":
                for n in range(5):
                    self.motioninfo[n] = float(msg_list[n])
            # 如果消息代码是"SCORE"
            if msg_code == "SCORE":
                # time1 = time.time()
                # print("%s 第%d局耗时%.1f秒" % (time.strftime("[%Y/%m/%d %H:%M:%S]"), self.round_num + 1, time1 - time0),
                #       end=" ")
                # time0 = time1
                # 从消息参数列表中获取得分
                self.score = int(msg_list[0])
                # 得分的队伍在下一局是先手
                if self.score > 0:
                    print("我方得" + str(self.score) + "分", end=" ")
                    # 如果不是无限对战模式(固定先后手)
                    if self.round_total != (-1):
                        self.player_is_init = True
                # 失分的队伍在下一局是后手
                elif self.score < 0:
                    print("对方得" + str(self.score * -1) + "分", end=" ")
                    # 如果不是无限对战模式(固定先后手)
                    if self.round_total != (-1):
                        self.player_is_init = False
                # 平局下一局交换先后手
                else:
                    print("双方均未得分", end=" ")
                    # 如果不是无限对战模式(固定先后手)
                    if self.round_total != (-1):
                        self.player_is_init = not self.player_is_init
                if self.player_is_init:
                    print("我方下局先手")
                else:
                    print("我方下局后手")
            # 如果消息代码是"GAMEOVER"
            if msg_code == "GAMEOVER":
                if msg_list[0] == "WIN":
                    print("我方获胜")
                elif msg_list[0] == "LOSE":
                    print("对方获胜")
                else:
                    print("双方平局")

        # 关闭Socket连接
        self.ai_sock.close()
        print("已关闭socket连接")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='冰糖壶芦-test')
    parser.add_argument('-H', '--host', help='tcp server host', default='127.0.0.1', required=False)
    parser.add_argument('-p', '--port', help='tcp server port', default=7788, required=False)
    args, unknown = parser.parse_known_args()
    print(args)

    # 根据数字冰壶服务器界面中给出的连接信息修改CONNECTKEY，注意这个数据每次启动都会改变。
    key = "hituavlab_4747b2fe-ac7f-46c8-90fc-807257768f85"
    # 初始化AI选手
    airobot = AIRobot(key, host=args.host, port=int(args.port))
    # 启动AI选手处理和服务器的通讯
    airobot.recv_forever()
