# -*- coding: utf-8 -*-
import socket
import time
import random
import math
import argparse
from util import *
from nomalstrategy import *

# python 与客户端连接
# 初始化
center_x = 2.375  # 大本营中心位置坐标
center_y = 4.88
House_R = 1.830  # 大本营半径
Stone_R = 0.145  # 冰壶半径


# pyinstaller -F CurlingAI.py
def strategy(state_list, shotnum, turn):
    sorted_res = []

    if shotnum == 1:
        state_list = [0] * 32
    print(state_list)
    # ==============
    advantage_team, advantage_score, point_list, ally_list, enemy_list, min_point = organize_position(turn, state_list)

    print('enemy_list:', enemy_list)
    print(shotnum)

    if shotnum <= 16:
        shot_offset, v, w = hit_enemy_line(point_list, enemy_list)
        if shot_offset:
            return list_to_str([v, shot_offset, w])
        safe_fit_enemy, danger_fit_enemy = crash_others1(point_list, enemy_list)
        if safe_fit_enemy:
            return list_to_str([safe_fit_enemy[0][2], safe_fit_enemy[0][1], safe_fit_enemy[0][3]])
        if danger_fit_enemy:
            return list_to_str([danger_fit_enemy[0][2], danger_fit_enemy[0][1], danger_fit_enemy[0][3]])
        return str('BESTSHOT {} {} {}'.format(2.7,0,0))
class AIRobot:
    def __init__(self, key, name="冰冰薄荷糖", host='127.0.0.1', port=7788, show_msg=False):
        # 新建Socket对象
        self.ai_sock = socket.socket()
        # 创建Socket连接
        self.ai_sock.connect((host, port))
        print("已建立socket连接", host, port)

        # 是否显示接收/发送的消息
        self.show_msg = show_msg
        # 发送连接密钥
        self.send_msg("CONNECTKEY:" + key)

        # 设定机器人名称
        self.name = name
        # 初始化冰壶位置
        self.position = [0] * 32
        # 初始化冰壶运动信息
        self.motioninfo = [0] * 5
        # 设定起始局数
        self.round_num = 0

    # 通过socket对象发送消息
    def send_msg(self, msg):
        if self.show_msg:
            print("  >>>> " + msg)
        # 将消息数据从字符串类型转换为bytes类型后发送
        self.ai_sock.send(msg.strip().encode())

    # 通过socket对象接收消息并进行解析
    def recv_msg(self):
        # 为避免TCP粘包问题，数字冰壶服务器发送给AI选手的每一条信息均以0（数值为0的字节）结尾
        # 这里采用了逐个字节接收后拼接的方式处理信息，多条信息之间以0为信息终结符
        buffer = bytearray()
        while True:
            # 接收1个字节
            data = self.ai_sock.recv(1)
            # 接收到空数据或者信息处终结符(0)即中断循环
            if not data or data == b'\0':
                break
            # 将当前字节拼接到缓存中
            buffer.extend(data)
        # 将消息数据从bytes类型转换为字符串类型后去除前后空格
        msg_str = buffer.decode().strip()
        if self.show_msg:
            print("<<<< " + msg_str)

        # 用空格将消息字符串分隔为列表
        msg_list = msg_str.split(" ")
        # 列表中第一个项为消息代码
        msg_code = msg_list[0]
        # 列表中后续的项为各个参数
        msg_list.pop(0)
        # 返回消息代码和消息参数列表
        return msg_code, msg_list

    # 与大本营中心距离
    def get_dist(self, x, y):
        House_x = 2.375
        House_y = 4.88
        return math.sqrt((x - House_x) ** 2 + (y - House_y) ** 2)

    # 大本营内是否有壶
    def is_in_house(self, dist):
        House_R = 1.830
        Stone_R = 0.145
        if dist < (House_R + Stone_R):
            return 1
        else:
            return 0

    def recv_setstate(self, msg_list):
        # 当前完成投掷数
        self.shot_num = int(msg_list[0])
        # 当前完成对局数
        self.round_num = int(msg_list[1])
        # 总对局数
        self.round_total = int(msg_list[2])
        # 预备投掷者（0为持蓝色球者，1为持红色球者）
        self.next_shot = int(msg_list[3])

    # 接收并处理消息
    def recv_forever(self):
        # 空消息计数器归零
        retNullTime = 0
        self.on_line = True

        while self.on_line:
            # 接收消息并解析
            msg_code, msg_list = self.recv_msg()
            # 如果接到空消息则将计数器加一
            if msg_code == "":
                retNullTime = retNullTime + 1
            # 如果接到五条空消息则关闭Socket连接
            if retNullTime == 5:
                break
                # 如果消息代码是……
            if msg_code == "CONNECTNAME":
                if msg_list[0] == "Player1":
                    self.player_is_init = True
                    print("玩家1，首局先手")
                else:
                    self.player_is_init = False
                    print("玩家2，首局后手")
            if msg_code == "ISREADY":
                # 发送"READYOK"
                self.send_msg("READYOK")
                time.sleep(0.5)
                # 发送"NAME"和AI选手名
                self.send_msg("NAME " + self.name)
                print(self.name + " 准备完毕！")
            if msg_code == "NEWGAME":
                time0 = time.time()
            if msg_code == "SETSTATE":
                self.recv_setstate(msg_list)
            if msg_code == "POSITION":
                for n in range(32):
                    self.position[n] = float(msg_list[n])
            if msg_code == "GO":
                # 制定策略生成投壶信息
                print('==================shot_num↓=====================', self.shot_num + 1)
                shot_msg = strategy(self.position, self.shot_num + 1, self.player_is_init)
                print('==================shot_num↑=====================', self.shot_num + 1)
                # 发送投壶消息
                print(shot_msg)
                print(self.position)
                self.send_msg(shot_msg)
            if msg_code == "MOTIONINFO":
                for n in range(5):
                    self.motioninfo[n] = float(msg_list[n])
            # 如果消息代码是"SCORE"
            if msg_code == "SCORE":
                # time1 = time.time()
                # print("%s 第%d局耗时%.1f秒" % (time.strftime("[%Y/%m/%d %H:%M:%S]"), self.round_num + 1, time1 - time0),
                #       end=" ")
                # time0 = time1
                # 从消息参数列表中获取得分
                self.score = int(msg_list[0])
                # 得分的队伍在下一局是先手
                if self.score > 0:
                    print("我方得" + str(self.score) + "分", end=" ")
                    # 如果不是无限对战模式(固定先后手)
                    if self.round_total != (-1):
                        self.player_is_init = True
                # 失分的队伍在下一局是后手
                elif self.score < 0:
                    print("对方得" + str(self.score * -1) + "分", end=" ")
                    # 如果不是无限对战模式(固定先后手)
                    if self.round_total != (-1):
                        self.player_is_init = False
                # 平局下一局交换先后手
                else:
                    print("双方均未得分", end=" ")
                    # 如果不是无限对战模式(固定先后手)
                    if self.round_total != (-1):
                        self.player_is_init = not self.player_is_init
                if self.player_is_init:
                    print("我方下局先手")
                else:
                    print("我方下局后手")
                    # 初始化冰壶位置
                self.position = [0] * 32
                    # 初始化冰壶运动信息
                self.motioninfo = [0] * 5
                    # 设定起始局数
                self.round_num = 0
            # 如果消息代码是"GAMEOVER"
            if msg_code == "GAMEOVER":
                if msg_list[0] == "WIN":
                    print("我方获胜")
                elif msg_list[0] == "LOSE":
                    print("对方获胜")
                else:
                    print("双方平局")


        # 关闭Socket连接
        self.ai_sock.close()
        print("已关闭socket连接")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='冰糖壶芦-test')
    parser.add_argument('-H', '--host', help='tcp server host', default='127.0.0.1', required=False)
    parser.add_argument('-p', '--port', help='tcp server port', default=7788, required=False)
    args, unknown = parser.parse_known_args()
    print(args)

    # 根据数字冰壶服务器界面中给出的连接信息修改CONNECTKEY，注意这个数据每次启动都会改变。
    key = "hituavlab_4747b2fe-ac7f-46c8-90fc-807257768f85"
    # 初始化AI选手
    airobot = AIRobot(key, host=args.host, port=int(args.port))
    # 启动AI选手处理和服务器的通讯
    airobot.recv_forever()
