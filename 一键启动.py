import subprocess
import time
import os
import webbrowser


def start_servers_and_open_webpages():
    # 配置信息
    servers = {
        9003: {
            "path": r"D:\university\社团\无人机实验室\2025\冰壶比赛\数字冰壶单机版\9003 7788\curling_server.exe",
            "url": "http://localhost:9003"
        },
        9004: {
            "path": r"D:\university\社团\无人机实验室\2025\冰壶比赛\数字冰壶单机版\9004 7789\curling_server.exe",
            "url": "http://localhost:9004"
        },
        9005: {
            "path": r"D:\university\社团\无人机实验室\2025\冰壶比赛\数字冰壶单机版\9005 7790\curling_server.exe",
            "url": "http://localhost:9005"
        },
        9009: {
            "path": r"D:\university\社团\无人机实验室\2025\冰壶比赛\数字冰壶单机版\9009 7794\curling_server.exe",
            "url": "http://localhost:9009"
        },
        9010: {
            "path": r"D:\university\社团\无人机实验室\2025\冰壶比赛\数字冰壶单机版\9010 7795\curling_server.exe",
            "url": "http://localhost:9010"
        },
        9011: {
            "path": r"D:\university\社团\无人机实验室\2025\冰壶比赛\数字冰壶单机版\9011 7796\curling_server.exe",
            "url": "http://localhost:9011"
        }
    }

    try:
        print("开始启动所有服务和网页...")

        # 1. 检查所有路径是否存在
        print("\n正在检查路径有效性...")
        all_paths_valid = True
        for port, config in servers.items():
            if not os.path.exists(config["path"]):
                print(f" 路径不存在: {config['path']}")
                all_paths_valid = False

        if not all_paths_valid:
            print("\n 请修正以上路径错误后再试！")
            return

        # 2. 启动所有服务器
        print("\n正在启动服务器程序...")
        processes = []
        for port, config in servers.items():
            try:
                program_dir = os.path.dirname(config["path"])
                print(f"正在启动 {port} 端口服务...")

                process = subprocess.Popen(
                    f'start cmd /k "{config["path"]}"',
                    shell=True,
                    cwd=program_dir
                )
                processes.append(process)
                time.sleep(1)  # 间隔防止端口冲突

                print(f"{port} 端口服务已启动")
            except Exception as e:
                print(f"{port} 端口启动失败: {str(e)}")

        # 3. 打开所有网页
        print("\n正在打开网页...")
        for port, config in servers.items():
            try:
                webbrowser.open_new_tab(config["url"])
                print(f"已打开: {config['url']}")
                time.sleep(0.3)  # 防止浏览器卡顿
            except Exception as e:
                print(f"无法打开 {config['url']}: {str(e)}")


    except Exception as e:
        print(f"\n发生未预期错误: {str(e)}")


if __name__ == "__main__":
    start_servers_and_open_webpages()