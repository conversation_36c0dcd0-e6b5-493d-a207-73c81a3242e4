﻿  main.cpp
E:\VS2019_SampleAI_2022\VS2019_SampleAI\CurlingAICode\main.cpp(25,47): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
E:\VS2019_SampleAI_2022\VS2019_SampleAI\CurlingAICode\main.cpp(35,39): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
  strategy.cpp
E:\VS2019_SampleAI_2022\VS2019_SampleAI\CurlingAICode\strategy.cpp(8,27): warning C4244: “return”: 从“double”转换到“float”，可能丢失数据
E:\VS2019_SampleAI_2022\VS2019_SampleAI\CurlingAICode\strategy.cpp(14,25): warning C4244: “return”: 从“double”转换到“float”，可能丢失数据
  正在生成代码...
  CurlingAI.vcxproj -> E:\VS2019_SampleAI_2022\VS2019_SampleAI\CurlingAICode\x64\Debug\CurlingAI.exe
