2025-07-29T23:30:44+08:00 [INFO] 欢迎进入哈尔滨工业大学人工智能研究院有限公司数字冰壶比赛
2025-07-29T23:30:44+08:00 [INFO] 请使用Chrome或Edge浏览器输入地址localhost:9010或打开安装目录下curling_web开始比赛
2025-07-29T23:30:44+08:00 [INFO] CurlingAI.exe TCP通信端口: 7795
2025-07-29T23:31:02+08:00 [INFO] 比赛平台连接成功
2025-07-29T23:31:02+08:00 [INFO] 比赛平台连接成功
2025-07-29T23:31:31+08:00 [INFO] New CurlingAI client connected.
2025-07-29T23:31:32+08:00 [INFO] New CurlingAI client connected.
2025-07-30T00:27:24+08:00 [INFO] Please Close CurlingAI!
2025-07-30T00:27:24+08:00 [INFO] Please Close CurlingAI!
00: use of closed network connection
2025-07-29T20:28:18+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:5200: use of closed network connection
2025-07-29T20:28:18+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:5200: use of closed network connection
2025-07-29T20:28:18+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:5200: use of closed network connection
2025-07-29T20:28:18+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:5200: use of closed network connection
2025-07-29T20:32:17+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:5200: use of closed network connection
2025-07-29T21:37:55+08:00 [INFO] Please Close CurlingAI!
2025-07-29T21:37:55+08:00 [INFO] Please Close CurlingAI!
 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-11T19:58:44+08:00 [INFO] Please Close CurlingAI!
2025-07-11T19:58:44+08:00 [INFO] Please Close CurlingAI!
2025-07-11T19:58:45+08:00 [INFO] 比赛平台连接成功
2025-07-11T19:58:45+08:00 [INFO] 比赛平台连接成功
2025-07-11T19:59:40+08:00 [INFO] New CurlingAI client connected.
2025-07-11T19:59:41+08:00 [INFO] New CurlingAI client connected.
2025-07-11T20:03:13+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:44463: use of closed network connection
2025-07-11T20:03:13+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:44463: use of closed network connection
2025-07-11T20:03:13+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:44463: use of closed network connection
2025-07-11T20:03:13+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:44463: use of closed network connection
2025-07-11T20:03:13+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:44463: use of closed network connection
2025-07-11T20:03:17+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:44463: use of closed network connection
2025-07-11T20:11:33+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-11T20:11:33+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-11T20:11:33+08:00 [INFO] Please Close CurlingAI!
2025-07-11T20:11:33+08:00 [INFO] Please Close CurlingAI!
2025-07-11T20:11:34+08:00 [INFO] 比赛平台连接成功
2025-07-11T20:11:34+08:00 [INFO] 比赛平台连接成功
2025-07-11T20:11:52+08:00 [INFO] New CurlingAI client connected.
2025-07-11T20:11:52+08:00 [INFO] New CurlingAI client connected.
2025-07-11T20:23:50+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:44631: use of closed network connection
2025-07-11T20:23:50+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:44631: use of closed network connection
2025-07-11T20:23:50+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:44631: use of closed network connection
2025-07-11T20:23:50+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:44631: use of closed network connection
2025-07-11T20:23:50+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:44631: use of closed network connection
2025-07-11T20:23:51+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7795->127.0.0.1:44631: use of closed network connection
rlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:12785: use of closed network connection
2025-07-11T10:47:35+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:12785: use of closed network connection
2025-07-11T10:47:36+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:12785: use of closed network connection
2025-07-11T10:49:12+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-11T10:49:12+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-11T10:49:12+08:00 [INFO] Please Close CurlingAI!
2025-07-11T10:49:12+08:00 [INFO] Please Close CurlingAI!
2025-07-11T10:49:13+08:00 [INFO] 比赛平台连接成功
2025-07-11T10:49:14+08:00 [INFO] 比赛平台连接成功
2025-07-11T10:49:26+08:00 [INFO] New CurlingAI client connected.
2025-07-11T10:49:38+08:00 [INFO] New CurlingAI client connected.
2025-07-11T13:44:28+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-11T13:44:28+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-11T13:44:28+08:00 [INFO] Please Close CurlingAI!
2025-07-11T13:44:28+08:00 [INFO] Please Close CurlingAI!
0 [INFO] 比赛平台连接成功
2025-07-10T17:36:57+08:00 [INFO] 比赛平台连接成功
2025-07-10T17:37:06+08:00 [INFO] New CurlingAI client connected.
2025-07-10T17:37:06+08:00 [INFO] New CurlingAI client connected.
2025-07-10T17:37:19+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:43657: use of closed network connection
2025-07-10T20:12:16+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T20:12:16+08:00 [INFO] Please Close CurlingAI!
2025-07-10T20:12:16+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T20:12:17+08:00 [INFO] Please Close CurlingAI!
2025-07-10T20:12:20+08:00 [INFO] 比赛平台连接成功
2025-07-10T20:12:20+08:00 [INFO] 比赛平台连接成功
2025-07-10T20:12:32+08:00 [INFO] New CurlingAI client connected.
2025-07-10T20:12:32+08:00 [INFO] New CurlingAI client connected.
2025-07-10T20:13:21+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:52755: use of closed network connection
2025-07-10T20:14:45+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T20:14:45+08:00 [INFO] Please Close CurlingAI!
2025-07-10T20:14:45+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T20:14:45+08:00 [INFO] Please Close CurlingAI!
2025-07-10T20:14:47+08:00 [INFO] 比赛平台连接成功
2025-07-10T20:14:47+08:00 [INFO] 比赛平台连接成功
2025-07-10T20:15:04+08:00 [INFO] New CurlingAI client connected.
2025-07-10T20:15:04+08:00 [INFO] New CurlingAI client connected.
2025-07-10T20:15:55+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:52909: use of closed network connection
2025-07-10T20:16:41+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T20:16:41+08:00 [INFO] Please Close CurlingAI!
2025-07-10T20:16:41+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T20:16:41+08:00 [INFO] Please Close CurlingAI!
2025-07-10T20:16:43+08:00 [INFO] 比赛平台连接成功
2025-07-10T20:16:43+08:00 [INFO] 比赛平台连接成功
2025-07-10T20:16:53+08:00 [INFO] New CurlingAI client connected.
2025-07-10T20:16:53+08:00 [INFO] New CurlingAI client connected.
2025-07-10T20:19:05+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T20:19:05+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T20:19:05+08:00 [INFO] Please Close CurlingAI!
2025-07-10T20:19:05+08:00 [INFO] Please Close CurlingAI!
2025-07-10T20:19:07+08:00 [INFO] 比赛平台连接成功
2025-07-10T20:19:07+08:00 [INFO] 比赛平台连接成功
2025-07-10T20:19:23+08:00 [INFO] New CurlingAI client connected.
2025-07-10T20:19:23+08:00 [INFO] New CurlingAI client connected.
2025-07-10T20:42:12+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:53167: use of closed network connection
2025-07-10T20:42:12+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:53167: use of closed network connection
2025-07-10T20:42:12+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:53167: use of closed network connection
2025-07-10T20:42:12+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:53167: use of closed network connection
2025-07-10T20:42:12+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:53167: use of closed network connection
2025-07-10T20:42:13+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:53167: use of closed network connection
2025-07-10T20:51:13+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T20:51:13+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T20:51:13+08:00 [INFO] Please Close CurlingAI!
2025-07-10T20:51:13+08:00 [INFO] Please Close CurlingAI!
2025-07-10T20:54:41+08:00 [INFO] 比赛平台连接成功
2025-07-10T20:54:41+08:00 [INFO] 比赛平台连接成功
2025-07-10T20:54:53+08:00 [INFO] New CurlingAI client connected.
2025-07-10T20:54:53+08:00 [INFO] New CurlingAI client connected.
2025-07-10T20:56:05+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:55205: use of closed network connection
2025-07-10T20:56:05+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:55205: use of closed network connection
2025-07-10T20:56:05+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:55205: use of closed network connection
2025-07-10T20:56:05+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:55205: use of closed network connection
2025-07-10T20:56:05+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:55205: use of closed network connection
2025-07-10T20:56:06+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:55205: use of closed network connection
2025-07-10T20:56:31+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T20:56:31+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T20:56:31+08:00 [INFO] Please Close CurlingAI!
2025-07-10T20:56:31+08:00 [INFO] Please Close CurlingAI!
2025-07-10T20:56:33+08:00 [INFO] 比赛平台连接成功
2025-07-10T20:56:33+08:00 [INFO] 比赛平台连接成功
2025-07-10T20:57:55+08:00 [INFO] New CurlingAI client connected.
2025-07-10T20:57:55+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:00:52+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:55397: use of closed network connection
2025-07-10T21:03:28+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:03:28+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:03:30+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:03:30+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:03:41+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:03:41+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:08:10+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:43418: use of closed network connection
2025-07-10T21:08:10+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:43418: use of closed network connection
2025-07-10T21:08:10+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:43418: use of closed network connection
2025-07-10T21:08:10+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:43418: use of closed network connection
2025-07-10T21:08:10+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:43418: use of closed network connection
2025-07-10T21:08:11+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:43418: use of closed network connection
2025-07-10T21:16:01+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:16:01+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:16:01+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:16:01+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:16:03+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:16:03+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:16:37+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:16:37+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:16:37+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:16:37+08:00 [ERRO] Already have two players connected
2025-07-10T21:16:37+08:00 [INFO] Robot started successfully
2025-07-10T21:20:17+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:53052: use of closed network connection
2025-07-10T21:20:17+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:53052: use of closed network connection
2025-07-10T21:20:17+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:53052: use of closed network connection
2025-07-10T21:20:22+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:53052: use of closed network connection
2025-07-10T21:20:22+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:53052: use of closed network connection
2025-07-10T21:22:11+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:22:11+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:22:11+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:22:11+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:22:11+08:00 [INFO] Robot started successfully
2025-07-10T21:22:15+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:22:15+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:22:29+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:22:29+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:22:29+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:22:29+08:00 [ERRO] Already have two players connected
2025-07-10T21:22:29+08:00 [INFO] Robot started successfully
2025-07-10T21:24:49+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:24:49+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:24:49+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:24:49+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:24:49+08:00 [INFO] Robot started successfully
2025-07-10T21:24:50+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:24:50+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:25:05+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:25:05+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:25:06+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:25:06+08:00 [ERRO] Already have two players connected
2025-07-10T21:25:06+08:00 [INFO] Robot started successfully
2025-07-10T21:26:57+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:26:57+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:26:57+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:26:57+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:26:57+08:00 [INFO] Robot started successfully
2025-07-10T21:26:58+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:26:58+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:27:14+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:27:14+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:27:14+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:27:14+08:00 [ERRO] Already have two players connected
2025-07-10T21:27:15+08:00 [INFO] Robot started successfully
2025-07-10T21:28:49+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:28:49+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:28:49+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:28:49+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:28:49+08:00 [INFO] Robot started successfully
2025-07-10T21:28:50+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:28:50+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:28:59+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:29:15+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:29:15+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:29:15+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:29:15+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:29:19+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:29:19+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:29:29+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:29:30+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:35:06+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:15375: use of closed network connection
2025-07-10T21:35:06+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:15375: use of closed network connection
2025-07-10T21:35:06+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:15375: use of closed network connection
2025-07-10T21:35:06+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:15375: use of closed network connection
2025-07-10T21:35:06+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:15375: use of closed network connection
2025-07-10T21:35:07+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:15375: use of closed network connection
2025-07-10T21:39:48+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:39:48+08:00 [INFO] Game disconnected: websocket: close 1000 (normal)
2025-07-10T21:39:48+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:39:48+08:00 [INFO] Please Close CurlingAI!
2025-07-10T21:39:50+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:39:50+08:00 [INFO] 比赛平台连接成功
2025-07-10T21:40:08+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:40:09+08:00 [INFO] New CurlingAI client connected.
2025-07-10T21:44:56+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:28453: use of closed network connection
2025-07-10T21:44:56+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:28453: use of closed network connection
2025-07-10T21:44:56+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:28453: use of closed network connection
2025-07-10T21:44:56+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:28453: use of closed network connection
2025-07-10T21:44:56+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:28453: use of closed network connection
2025-07-10T21:44:57+08:00 [ERRO] Transform web message to CurlingAI failed: write tcp 127.0.0.1:7788->127.0.0.1:28453: use of closed network connection
2025-07-10T22:30:30+08:00 [INFO] Please Close CurlingAI!
2025-07-10T22:30:30+08:00 [INFO] Please Close CurlingAI!
